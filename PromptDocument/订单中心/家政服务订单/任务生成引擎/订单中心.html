<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汇成人力资源服务平台 - 运营后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #3498db;
            --secondary: #2980b9;
            --success: #2ecc71;
            --warning: #f39c12;
            --danger: #e74c3c;
            --light: #f8f9fa;
            --dark: #343a40;
            --gray: #6c757d;
            --light-gray: #e9ecef;
            --border: #dee2e6;
            --sidebar-width: 240px;
            --header-height: 60px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #f5f7fa;
            color: #333;
            display: flex;
            min-height: 100vh;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            width: var(--sidebar-width);
            background: linear-gradient(135deg, #1a2a6c, #2a5298);
            color: white;
            height: 100vh;
            position: fixed;
            padding: 20px 0;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            z-index: 1000;
        }
        
        .logo {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .logo img {
            width: 40px;
            height: 40px;
            margin-right: 10px;
            background: white;
            border-radius: 8px;
            padding: 5px;
        }
        
        .logo h1 {
            font-size: 18px;
            font-weight: 600;
        }
        
        .nav-section {
            margin-bottom: 20px;
        }
        
        .nav-section h3 {
            font-size: 12px;
            text-transform: uppercase;
            padding: 10px 20px;
            color: rgba(255,255,255,0.6);
            letter-spacing: 1px;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s;
            border-left: 3px solid transparent;
            font-size: 14px;
        }
        
        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        
        .nav-link.active {
            background: rgba(255,255,255,0.15);
            color: white;
            border-left: 3px solid var(--success);
        }
        
        .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .nav-item .nav-link.has-submenu {
            justify-content: space-between;
        }
        
        .submenu-arrow {
            font-size: 12px;
            transition: transform 0.3s;
        }
        
        .has-submenu.open .submenu-arrow {
            transform: rotate(180deg);
        }

        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            background: rgba(0,0,0,0.15);
        }

        .submenu .nav-link {
            padding-left: 53px; /* Adjust for icon indent */
            font-size: 13px;
        }

        .submenu .nav-link:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .badge {
            background: var(--danger);
            color: white;
            border-radius: 10px;
            padding: 2px 8px;
            font-size: 11px;
            margin-left: auto;
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
        }
        
        /* 顶部导航栏 */
        .topbar {
            height: var(--header-height);
            background: white;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--border);
            position: sticky;
            top: 0;
            z-index: 999;
        }
        
        .search-box {
            background: var(--light);
            border-radius: 20px;
            padding: 8px 15px;
            width: 300px;
            display: flex;
            align-items: center;
        }
        
        .search-box input {
            border: none;
            background: transparent;
            padding: 0 10px;
            width: 100%;
            outline: none;
        }
        
        .user-actions {
            display: flex;
            align-items: center;
        }
        
        .notification {
            position: relative;
            margin-right: 20px;
            cursor: pointer;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--danger);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: var(--primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        
        /* 工作台内容 */
        .dashboard {
            padding: 20px;
        }
        
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .dashboard-header h2 {
            font-size: 24px;
            color: var(--dark);
            display: flex;
            align-items: center;
        }
        
        .dashboard-header h2 i {
            margin-right: 10px;
            color: var(--primary);
        }
        
        .date-display {
            background: white;
            padding: 10px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 14px;
        }
        
        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-header h3 {
            font-size: 16px;
            font-weight: 600;
            color: var(--dark);
        }
        
        .card-actions {
            display: flex;
        }
        
        .card-actions button {
            background: none;
            border: none;
            color: var(--gray);
            cursor: pointer;
            margin-left: 10px;
            font-size: 14px;
        }
        
        .card-body {
            padding: 20px;
        }
        
        /* 网格布局 */
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        /* KPI卡片 */
        .kpi-card {
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .kpi-value {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary);
            margin: 10px 0;
        }
        
        .kpi-label {
            font-size: 14px;
            color: var(--gray);
        }
        
        .kpi-trend {
            margin-top: 5px;
            font-size: 12px;
            color: var(--success);
        }
        
        .kpi-trend.down {
            color: var(--danger);
        }
        
        /* 快速入口 */
        .quick-access {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            text-align: center;
        }
        
        .quick-item {
            padding: 15px;
            background: var(--light);
            border-radius: 8px;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .quick-item:hover {
            background: var(--primary);
            color: white;
            transform: translateY(-3px);
        }
        
        .quick-item:hover i {
            color: white;
        }
        
        .quick-item i {
            font-size: 24px;
            color: var(--primary);
            margin-bottom: 10px;
        }
        
        .quick-item span {
            display: block;
            font-size: 13px;
        }
        
        /* 任务列表 */
        .task-list {
            list-style: none;
        }
        
        .task-item {
            padding: 15px;
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .task-item:last-child {
            border-bottom: none;
        }
        
        .task-item:hover {
            background: var(--light);
        }
        
        .task-checkbox {
            margin-right: 15px;
        }
        
        .task-content {
            flex: 1;
        }
        
        .task-title {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .task-meta {
            font-size: 13px;
            color: var(--gray);
            display: flex;
        }
        
        .task-meta div {
            margin-right: 15px;
        }
        
        .task-priority {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .priority-high {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger);
        }
        
        .priority-medium {
            background: rgba(243, 156, 18, 0.1);
            color: var(--warning);
        }
        
        .priority-low {
            background: rgba(46, 204, 113, 0.1);
            color: var(--success);
        }
        
        /* 日历样式 */
        .calendar {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 5px;
        }
        
        .calendar-header {
            grid-column: 1 / -1;
            text-align: center;
            padding: 10px;
            font-weight: 500;
            color: var(--dark);
            border-bottom: 1px solid var(--border);
        }
        
        .calendar-day {
            text-align: center;
            padding: 10px 5px;
            font-size: 12px;
            color: var(--gray);
        }
        
        .calendar-date {
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            cursor: pointer;
            font-size: 14px;
        }
        
        .calendar-date:hover {
            background: var(--light);
        }
        
        .calendar-date.today {
            background: var(--primary);
            color: white;
        }
        
        .calendar-date.event {
            position: relative;
        }
        
        .calendar-date.event::after {
            content: "";
            position: absolute;
            bottom: 3px;
            left: 50%;
            transform: translateX(-50%);
            width: 5px;
            height: 5px;
            border-radius: 50%;
            background: var(--danger);
        }
        
        /* 公告样式 */
        .announcement {
            padding: 15px;
            border-bottom: 1px solid var(--border);
        }
        
        .announcement:last-child {
            border-bottom: none;
        }
        
        .announcement-title {
            font-weight: 500;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }
        
        .announcement-title i {
            color: var(--warning);
            margin-right: 8px;
        }
        
        .announcement-meta {
            font-size: 12px;
            color: var(--gray);
            margin-bottom: 10px;
        }
        
        .announcement-content {
            font-size: 14px;
        }
        
        /* 任务中心标签页 */
        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border);
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 12px 20px;
            cursor: pointer;
            font-weight: 500;
            color: var(--gray);
            position: relative;
        }
        
        .tab.active {
            color: var(--primary);
        }
        
        .tab.active::after {
            content: "";
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--primary);
        }
        
        .tab-badge {
            margin-left: 8px;
            background: var(--danger);
            color: white;
            border-radius: 10px;
            padding: 1px 6px;
            font-size: 11px;
            font-weight: normal;
        }
        
        /* 筛选区域 */
        .filter-bar {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .filter-group {
            display: flex;
            align-items: center;
        }
        
        .filter-group label {
            margin-right: 8px;
            font-size: 14px;
            color: var(--gray);
        }
        
        select, input {
            padding: 8px 12px;
            border: 1px solid var(--border);
            border-radius: 4px;
            background: white;
            font-size: 14px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background: var(--primary);
            color: white;
        }
        
        .btn-outline {
            background: none;
            border: 1px solid var(--border);
            color: var(--gray);
        }
        
        /* 任务详情抽屉 */
        .drawer {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 500px;
            background: white;
            box-shadow: -3px 0 15px rgba(0,0,0,0.1);
            z-index: 2000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        
        /* 家政服务订单详情抽屉 - 更宽 */
        #domestic-detail-drawer {
            width: 800px;
            max-width: 90vw;
        }
        
        .drawer.open {
            transform: translateX(0);
        }
        
        .drawer-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .drawer-body {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }
        
        .drawer-footer {
            padding: 15px 20px;
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            font-size: 14px;
        }
        
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border);
            border-radius: 4px;
            font-size: 14px;
        }
        
        .timeline {
            list-style: none;
            position: relative;
            padding-left: 20px;
            margin-left: 10px;
        }
        
        .timeline::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--light-gray);
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
            padding-left: 20px;
        }
        
        .timeline-item::before {
            content: "";
            position: absolute;
            left: -10px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--primary);
            border: 2px solid white;
        }
        
        .timeline-time {
            font-size: 12px;
            color: var(--gray);
            margin-bottom: 5px;
        }
        
        .timeline-content {
            background: var(--light);
            padding: 10px 15px;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1500;
            display: none;
        }
        
        .overlay.active {
            display: block;
        }

        /* 审批弹窗样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 3000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            max-width: 90%;
            max-height: 90%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .modal-body {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        /* 阿姨选择弹窗样式 */
        .auntie-item {
            transition: all 0.3s ease;
        }

        .auntie-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .auntie-item.selected {
            border-color: var(--primary) !important;
            background-color: rgba(52, 152, 219, 0.05) !important;
        }

        /* 订单中心专用样式 */
        .order-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .order-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid var(--primary);
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary);
            margin: 10px 0;
        }

        .stat-label {
            color: var(--gray);
            font-size: 14px;
        }

        .business-tabs {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 20px;
        }

        .business-nav {
            display: flex;
            border-bottom: 1px solid var(--border);
            padding: 0 20px;
        }

        .business-tab {
            padding: 15px 20px;
            cursor: pointer;
            position: relative;
            color: var(--gray);
            font-weight: 500;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }

        .business-tab.active {
            color: var(--primary);
            border-bottom-color: var(--primary);
        }

        .business-tab:hover {
            color: var(--primary);
            background: rgba(52, 152, 219, 0.05);
        }

        .order-content {
            padding: 20px;
        }

        .order-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn-new {
            background: var(--success);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-new:hover {
            background: #27ae60;
        }

        .order-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .order-table th {
            background: var(--light);
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: var(--dark);
            border-bottom: 1px solid var(--border);
        }

        .order-table td {
            padding: 15px;
            border-bottom: 1px solid var(--border);
            vertical-align: middle;
        }

        .order-table tr:hover {
            background: rgba(52, 152, 219, 0.02);
        }

        .order-id {
            color: var(--primary);
            font-weight: 500;
            cursor: pointer;
        }

        .order-id:hover {
            text-decoration: underline;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: rgba(243, 156, 18, 0.1);
            color: #e67e22;
        }

        .status-processing {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }

        .status-completed {
            background: rgba(46, 204, 113, 0.1);
            color: #27ae60;
        }

        .status-cancelled {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .status-warning {
            background: rgba(155, 89, 182, 0.1);
            color: #9b59b6;
        }

        .status-draft {
            background: rgba(149, 165, 166, 0.1);
            color: #95a5a6;
        }

        .status-approval-pending {
            background: rgba(149, 165, 166, 0.1);
            color: #7f8c8d;
        }

        .status-approved {
            background: rgba(52, 152, 219, 0.1);
            color: #2980b9;
        }

        .status-rejected {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .status-success {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
        }

        /* 收款信息卡片样式 */
        .payment-info-card {
            background: linear-gradient(135deg, rgba(39, 174, 96, 0.05) 0%, rgba(46, 204, 113, 0.05) 100%);
            border-left: 4px solid #27ae60;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-action {
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .btn-view {
            background: var(--primary);
            color: white;
        }

        .btn-edit {
            background: var(--warning);
            color: white;
        }

        .btn-delete {
            background: var(--danger);
            color: white;
        }

        .btn-action:hover {
            opacity: 0.8;
        }

        /* 操作日志样式 */
        .operation-log-drawer {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 600px;
            background: white;
            box-shadow: -3px 0 15px rgba(0,0,0,0.1);
            z-index: 2000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        
        .operation-log-drawer.open {
            transform: translateX(0);
        }
        
        .operation-log-drawer .drawer-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .operation-log-drawer .drawer-body {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }
        
        .operation-log-drawer .drawer-footer {
            padding: 15px 20px;
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: flex-end;
        }
        
        .log-item {
            border: 1px solid var(--border);
            border-radius: 6px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .log-header {
            background: var(--light);
            padding: 12px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border);
        }
        
        .log-time {
            font-size: 12px;
            color: var(--gray);
        }
        
        .log-operator {
            font-weight: 500;
            color: var(--dark);
        }
        
        .log-action {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .log-action-create {
            background: rgba(46, 204, 113, 0.1);
            color: var(--success);
        }
        
        .log-action-edit {
            background: rgba(243, 156, 18, 0.1);
            color: var(--warning);
        }
        
        .log-action-delete {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger);
        }
        
        .log-action-approve {
            background: rgba(52, 152, 219, 0.1);
            color: var(--primary);
        }
        
        .log-action-reject {
            background: rgba(155, 89, 182, 0.1);
            color: #9b59b6;
        }
        
        .log-content {
            padding: 15px;
        }
        
        .log-changes {
            margin-top: 10px;
        }
        
        .change-item {
            display: flex;
            margin-bottom: 8px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .change-field {
            font-weight: 500;
            color: var(--dark);
            min-width: 100px;
            margin-right: 15px;
        }
        
        .change-old {
            color: var(--danger);
            text-decoration: line-through;
            margin-right: 10px;
        }
        
        .change-new {
            color: var(--success);
            font-weight: 500;
        }
        
        .log-comment {
            margin-top: 10px;
            padding: 10px;
            background: #fff3cd;
            border-left: 4px solid var(--warning);
            border-radius: 4px;
            font-style: italic;
        }

        /* 派单功能样式 */
        .dispatch-drawer {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 700px;
            background: white;
            box-shadow: -3px 0 15px rgba(0,0,0,0.1);
            z-index: 2000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        
        .dispatch-drawer.open {
            transform: translateX(0);
        }
        
        .dispatch-drawer .drawer-header {
            padding: 20px;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--light);
        }
        
        .dispatch-drawer .drawer-footer {
            padding: 20px;
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            background: var(--light);
        }
        
        .dispatch-tabs {
            display: flex;
            border-bottom: 1px solid var(--border);
            background: var(--light);
        }
        
        .dispatch-tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .dispatch-tab.active {
            background: white;
            border-bottom-color: var(--primary);
            color: var(--primary);
        }
        
        .dispatch-tab:hover {
            background: rgba(52, 152, 219, 0.05);
        }
        
        .dispatch-content {
            flex: 1;
            overflow-y: auto;
        }
        
        .dispatch-section {
            padding: 20px;
            display: none;
        }
        
        .dispatch-section.active {
            display: block;
        }
        
        .search-filter {
            margin-bottom: 20px;
            padding: 15px;
            background: var(--light);
            border-radius: 6px;
        }
        
        .search-filter input {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border);
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .filter-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .filter-tag {
            padding: 5px 12px;
            background: white;
            border: 1px solid var(--border);
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-tag.active {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        
        .provider-list {
            display: grid;
            gap: 15px;
        }
        
        .provider-card {
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        
        .provider-card:hover {
            border-color: var(--primary);
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
        }
        
        .provider-card.selected {
            border-color: var(--primary);
            background: rgba(52, 152, 219, 0.05);
        }
        
        .provider-card.selected::after {
            content: '✓';
            position: absolute;
            top: 10px;
            right: 10px;
            width: 20px;
            height: 20px;
            background: var(--primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .provider-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .provider-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 12px;
        }
        
        .provider-info h4 {
            margin: 0 0 5px 0;
            font-size: 16px;
        }
        
        .provider-info p {
            margin: 0;
            font-size: 12px;
            color: var(--gray);
        }
        
        .provider-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin: 10px 0;
        }
        
        .stat-item {
            text-align: center;
            padding: 8px;
            background: var(--light);
            border-radius: 4px;
        }
        
        .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: var(--primary);
        }
        
        .stat-label {
            font-size: 11px;
            color: var(--gray);
        }
        
        .provider-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 10px;
        }
        
        .provider-tag {
            padding: 3px 8px;
            background: rgba(46, 204, 113, 0.1);
            color: var(--success);
            border-radius: 10px;
            font-size: 11px;
        }
        
        .provider-tag.warning {
            background: rgba(243, 156, 18, 0.1);
            color: var(--warning);
        }
        
        .provider-tag.danger {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger);
        }
        
        .dispatch-summary {
            background: var(--light);
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .summary-item:last-child {
            margin-bottom: 0;
        }
        
        .summary-label {
            color: var(--gray);
            font-size: 14px;
        }
        
        .summary-value {
            font-weight: 500;
            color: var(--dark);
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid var(--border);
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button.active {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .high-school-practice {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .enterprise-training {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .personal-registration {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .domestic-service {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        /* 项目管理特色样式 */
        .project-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .project-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .project-title {
            font-size: 18px;
            font-weight: 600;
        }

        .project-status {
            padding: 5px 15px;
            background: rgba(255,255,255,0.2);
            border-radius: 15px;
            font-size: 12px;
        }

        .project-meta {
            padding: 20px;
            border-bottom: 1px solid var(--border);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .meta-item {
            display: flex;
            flex-direction: column;
        }

        .meta-label {
            font-size: 12px;
            color: var(--gray);
            margin-bottom: 5px;
        }

        .meta-value {
            font-weight: 500;
            color: var(--dark);
        }

        .class-list {
            padding: 20px;
        }

        .class-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid var(--border);
            border-radius: 6px;
            margin-bottom: 10px;
            transition: all 0.3s;
        }

        .class-item:hover {
            border-color: var(--primary);
            box-shadow: 0 2px 5px rgba(52, 152, 219, 0.1);
        }

        .class-info {
            flex: 1;
        }

        .class-name {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .class-details {
            font-size: 12px;
            color: var(--gray);
        }

        .class-actions {
            display: flex;
            gap: 8px;
        }

        .drawer-body .form-group-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            align-items: center;
        }
        .drawer-body .form-group-row input {
            margin-bottom: 0;
        }
        .drawer-body .form-group-row button {
            background: var(--danger);
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-weight: bold;
            flex-shrink: 0;
        }

        /* 电子签章样式 */
        .signature-section {
            margin-top: 25px;
        }

        .signature-list {
            list-style: none;
            padding: 0;
        }
        
        .contract-management {
            border: 1px solid var(--border);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .contract-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid var(--border);
        }
        
        .contract-tab {
            flex: 1;
            padding: 12px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: var(--gray);
            transition: all 0.3s;
        }
        
        .contract-tab.active {
            background: white;
            color: var(--primary);
            border-bottom: 2px solid var(--primary);
        }
        
        .contract-tab:hover:not(.active) {
            background: #e9ecef;
        }
        
        .contract-content {
            padding: 20px;
        }

        .signature-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
            border: 1px solid var(--border);
            border-radius: 6px;
            margin-bottom: 10px;
            background: #fdfdfd;
        }

        .signature-party {
            display: flex;
            align-items: center;
            font-weight: 500;
        }

        .signature-party i {
            font-size: 16px;
            margin-right: 12px;
            width: 20px;
            text-align: center;
            color: var(--gray);
        }

        .signature-status .status-badge {
            min-width: 80px;
            text-align: center;
        }
        
        .status-unsigned {
            background: rgba(108, 117, 125, 0.1);
            color: #6c757d;
        }

        .status-rejected {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .status-approval-pending {
            background: rgba(155, 89, 182, 0.1);
            color: #9b59b6;
        }

        /* 附件样式 */
        .attachment-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border: 1px solid var(--border);
            border-radius: 6px;
            margin-top: 10px;
            background: #fdfdfd;
        }

        .attachment-item i {
            font-size: 16px;
            margin-right: 12px;
            color: var(--gray);
        }

        .attachment-name {
            flex: 1;
            font-weight: 500;
        }

        /* 服务评价样式 */
        .evaluation-section {
            margin-top: 25px;
        }
        .evaluation-rating {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .evaluation-rating .stars {
            color: #f39c12; /* var(--warning) */
            font-size: 20px;
            margin-right: 15px;
        }
        .evaluation-rating .rating-text {
            font-size: 16px;
            font-weight: 600;
        }
        .evaluation-tags {
            margin-bottom: 15px;
        }
        .evaluation-tags .tag {
            display: inline-block;
            background: rgba(46, 204, 113, 0.1);
            color: var(--success);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 13px;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        .evaluation-comment {
            background: var(--light);
            padding: 15px;
            border-radius: 6px;
            font-style: italic;
            color: var(--dark);
            line-height: 1.6;
        }
        .evaluation-comment::before {
            content: '\f10d';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            margin-right: 10px;
            color: var(--gray);
        }

        /* 家政服务订单详情Tab页样式 */
        .domestic-tab-content {
            display: none;
        }
        
        .domestic-tab-content.active {
            display: block;
        }
        
        /* 家政服务订单详情Tab导航优化 */
        #domestic-detail-drawer .business-nav {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border);
            padding-bottom: 10px;
        }
        
        #domestic-detail-drawer .business-tab {
            padding: 8px 16px;
            font-size: 13px;
            white-space: nowrap;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            background: var(--light);
            color: var(--gray);
            border: 1px solid transparent;
        }
        
        #domestic-detail-drawer .business-tab:hover {
            background: rgba(52, 152, 219, 0.1);
            color: var(--primary);
        }
        
        #domestic-detail-drawer .business-tab.active {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        
        /* 家政服务订单详情内容区域优化 */
        #domestic-detail-drawer .order-content {
            padding: 0;
        }
        
        #domestic-detail-drawer .section {
            margin-bottom: 30px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        #domestic-detail-drawer .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--primary);
            color: var(--dark);
        }
        
        /* 任务列表样式 */
        .task-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .task-table th {
            background: var(--light);
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: var(--dark);
            border-bottom: 1px solid var(--border);
            font-size: 13px;
        }
        
        .task-table td {
            padding: 12px;
            border-bottom: 1px solid var(--border);
            vertical-align: middle;
            font-size: 13px;
        }
        
        /* 家政服务订单详情中的任务表格优化 */
        #domestic-detail-drawer .task-table {
            font-size: 12px;
        }
        
        #domestic-detail-drawer .task-table th {
            padding: 10px 8px;
            font-size: 12px;
        }
        
        #domestic-detail-drawer .task-table td {
            padding: 10px 8px;
            font-size: 12px;
        }
        
        #domestic-detail-drawer .task-table .task-sequence {
            font-size: 11px;
        }
        
        #domestic-detail-drawer .task-table .task-status {
            font-size: 10px;
            padding: 3px 6px;
        }
        
        #domestic-detail-drawer .task-actions button {
            padding: 2px 6px;
            font-size: 10px;
        }
        
        .task-table tr:hover {
            background: rgba(52, 152, 219, 0.02);
        }
        
        .task-checkbox {
            width: 16px;
            height: 16px;
        }
        
        .task-sequence {
            font-weight: 500;
            color: var(--primary);
        }
        
        .task-id {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            color: var(--gray);
            background: var(--light);
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 500;
        }
        
        .task-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .task-status-pending {
            background: rgba(149, 165, 166, 0.1);
            color: #7f8c8d;
        }
        
        .task-status-assigned {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }
        
        .task-status-completed {
            background: rgba(46, 204, 113, 0.1);
            color: #27ae60;
        }
        
        .task-status-cancelled {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }
        
        .task-actions {
            display: flex;
            gap: 5px;
        }
        
        .task-actions button {
            padding: 3px 8px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.3s;
        }
        
        .task-actions .btn-assign {
            background: var(--primary);
            color: white;
        }
        
        .task-actions .btn-complete {
            background: var(--success);
            color: white;
        }
        
        .task-actions .btn-cancel {
            background: var(--danger);
            color: white;
        }
        
        .task-actions .btn-edit {
            background: var(--warning);
            color: white;
        }
        
        /* 家政服务订单详情中的筛选栏优化 */
        #domestic-detail-drawer .filter-bar {
            background: var(--light);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
        }
        
        #domestic-detail-drawer .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        #domestic-detail-drawer .filter-group label {
            font-size: 12px;
            font-weight: 500;
            color: var(--dark);
            white-space: nowrap;
        }
        
        #domestic-detail-drawer .filter-group select,
        #domestic-detail-drawer .filter-group input {
            padding: 6px 10px;
            border: 1px solid var(--border);
            border-radius: 4px;
            font-size: 12px;
            min-width: 120px;
        }
        
        #domestic-detail-drawer .filter-group input[type="date"] {
            min-width: 130px;
        }
    </style>
</head>
<body>
    <!-- 侧边导航 -->
    <div class="sidebar">
        <div class="logo">
            <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTYgMjU2Ij48cmVjdCB3aWR0aD0iMjU2IiBoZWlnaHQ9IjI1NiIgZmlsbD0id2hpdGUiIHJ4PSIxMiIvPjxwYXRoIGQ9Ik0xMjggMzJMMTUyIDk2TDIyNCA5NkwxNjAgMTQ0TDE4NCAyMjRMMTI4IDE3Nkw3MiAyMjRMOTYgMTQ0TDMyIDk2TDEwNCA5NkwxMjggMzJaIiBmaWxsPSIjMzQ5OGRiIi8+PC9zdmc+" alt="汇成平台">
            <h1>汇成人力资源</h1>
        </div>
        
        <div class="nav-section">
            <h3>核心功能</h3>
            <a href="#" class="nav-link active" data-view="dashboard">
                <i class="fas fa-home"></i> 工作台
            </a>
            <a href="#" class="nav-link" data-view="tasks">
                <i class="fas fa-tasks"></i> 任务中心
                <span class="badge">15</span>
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-user-friends"></i> OneID人才库
            </a>
        </div>
        
        <div class="nav-section">
            <h3>公共能力</h3>
            <a href="#" class="nav-link" data-view="opportunity-center">
                <i class="fas fa-bullseye"></i> 商机中心
            </a>
            <a href="#" class="nav-link" data-view="leads-center">
                <i class="fas fa-filter"></i> 线索中心
                <span class="badge">8</span>
            </a>
            <div class="nav-item">
                <a href="#" class="nav-link has-submenu">
                    <i class="fas fa-file-invoice"></i> 订单中心 <i class="fas fa-chevron-down submenu-arrow"></i>
                </a>
                <div class="submenu">
                    <a href="#" class="nav-link" data-business="practice">高校实践订单</a>
                    <a href="#" class="nav-link" data-business="training">企业培训订单</a>
                    <a href="#" class="nav-link" data-business="personal">个人培训与认证订单</a>
                    <a href="#" class="nav-link" data-business="domestic">家政服务订单</a>
                </div>
            </div>
            <a href="#" class="nav-link" data-view="resource-center">
                <i class="fas fa-box-open"></i> 资源中心
            </a>
        </div>
        
        <div class="nav-section">
            <h3>业务模块</h3>
            <a href="#" class="nav-link" data-view="training-management">
                <i class="fas fa-chalkboard-teacher"></i> 培训管理
            </a>
            <a href="#" class="nav-link" data-view="employment-service">
                <i class="fas fa-user-tie"></i> 就业服务
            </a>
            <a href="#" class="nav-link" data-view="part-time-job">
                <i class="fas fa-clock"></i> 兼职零工
            </a>
        </div>
        
        <div class="nav-section">
            <h3>系统管理</h3>
            <a href="#" class="nav-link">
                <i class="fas fa-users-cog"></i> 用户管理
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-shield-alt"></i> 角色权限
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-cog"></i> 系统设置
            </a>
        </div>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="topbar">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="搜索...">
            </div>
            
            <div class="user-actions">
                <div class="notification">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </div>
                <div class="notification">
                    <i class="fas fa-envelope"></i>
                    <span class="notification-badge">5</span>
                </div>
                <div class="user-profile">
                    <div class="user-avatar">张</div>
                    <div>
                        <div>张三</div>
                        <div style="font-size: 12px; color: var(--gray);">管理员</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 工作台内容 -->
        <div class="dashboard">
            <div class="dashboard-header">
                <h2><i class="fas fa-home"></i> 个人工作台</h2>
                <div class="date-display">
                    <i class="fas fa-calendar-alt"></i> 2024年6月19日 星期三 下午15:00
                </div>
            </div>
            
            <!-- 第一行：KPI指标和快速入口 -->
            <div class="grid">
                <div class="card">
                    <div class="card-header">
                        <h3>核心指标</h3>
                    </div>
                    <div class="card-body">
                        <div class="grid" style="grid-template-columns: repeat(4, 1fr);">
                            <div class="kpi-card">
                                <div class="kpi-label">今日新增线索</div>
                                <div class="kpi-value">24</div>
                                <div class="kpi-trend">
                                    <i class="fas fa-arrow-up"></i> 12% 较昨日
                                </div>
                            </div>
                            <div class="kpi-card">
                                <div class="kpi-label">待跟进商机</div>
                                <div class="kpi-value">8</div>
                                <div class="kpi-trend down">
                                    <i class="fas fa-arrow-down"></i> 3% 较昨日
                                </div>
                            </div>
                            <div class="kpi-card">
                                <div class="kpi-label">待处理订单</div>
                                <div class="kpi-value">16</div>
                                <div class="kpi-trend">
                                    <i class="fas fa-arrow-up"></i> 8% 较昨日
                                </div>
                            </div>
                            <div class="kpi-card">
                                <div class="kpi-label">人才库数量</div>
                                <div class="kpi-value">12,534</div>
                                <div class="kpi-trend">
                                    <i class="fas fa-arrow-up"></i> 1.5% 较上周
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3>快速入口</h3>
                    </div>
                    <div class="card-body">
                        <div class="quick-access">
                            <div class="quick-item">
                                <i class="fas fa-plus-circle"></i>
                                <span>新建订单</span>
                            </div>
                            <div class="quick-item">
                                <i class="fas fa-book"></i>
                                <span>发布课程</span>
                            </div>
                            <div class="quick-item">
                                <i class="fas fa-users"></i>
                                <span>客户管理</span>
                            </div>
                            <div class="quick-item">
                                <i class="fas fa-file-contract"></i>
                                <span>合同管理</span>
                            </div>
                            <div class="quick-item">
                                <i class="fas fa-chart-line"></i>
                                <span>数据报告</span>
                            </div>
                            <div class="quick-item">
                                <i class="fas fa-user-plus"></i>
                                <span>新建用户</span>
                            </div>
                            <div class="quick-item">
                                <i class="fas fa-tools"></i>
                                <span>系统设置</span>
                            </div>
                            <div class="quick-item">
                                <i class="fas fa-question-circle"></i>
                                <span>帮助中心</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 第二行：任务和日历 -->
            <div class="grid">
                <div class="card" style="grid-column: span 2;">
                    <div class="card-header">
                        <h3>我的待办</h3>
                        <div class="card-actions">
                            <button>查看全部 <i class="fas fa-arrow-right"></i></button>
                        </div>
                    </div>
                    <div class="card-body">
                        <ul class="task-list">
                            <li class="task-item">
                                <div class="task-checkbox">
                                    <input type="checkbox">
                                </div>
                                <div class="task-content">
                                    <div class="task-title">审批 XX大学实践项目合作协议</div>
                                    <div class="task-meta">
                                        <div><i class="far fa-user"></i> 李四</div>
                                        <div><i class="far fa-clock"></i> 今天 16:00前</div>
                                        <div><i class="fas fa-tag"></i> 高校实践</div>
                                    </div>
                                </div>
                                <span class="task-priority priority-high">高优先级</span>
                            </li>
                            <li class="task-item">
                                <div class="task-checkbox">
                                    <input type="checkbox">
                                </div>
                                <div class="task-content">
                                    <div class="task-title">处理李女士的月嫂服务投诉</div>
                                    <div class="task-meta">
                                        <div><i class="far fa-user"></i> 王五</div>
                                        <div><i class="far fa-clock"></i> 今天 17:30前</div>
                                        <div><i class="fas fa-tag"></i> 家政服务</div>
                                    </div>
                                </div>
                                <span class="task-priority priority-high">高优先级</span>
                            </li>
                            <li class="task-item">
                                <div class="task-checkbox">
                                    <input type="checkbox">
                                </div>
                                <div class="task-content">
                                    <div class="task-title">审核6月金牌月嫂认证考试结果</div>
                                    <div class="task-meta">
                                        <div><i class="far fa-user"></i> 系统自动</div>
                                        <div><i class="far fa-clock"></i> 明天 10:00前</div>
                                        <div><i class="fas fa-tag"></i> 培训认证</div>
                                    </div>
                                </div>
                                <span class="task-priority priority-medium">中优先级</span>
                            </li>
                            <li class="task-item">
                                <div class="task-checkbox">
                                    <input type="checkbox">
                                </div>
                                <div class="task-content">
                                    <div class="task-title">确认YY企业培训订单收款</div>
                                    <div class="task-meta">
                                        <div><i class="far fa-user"></i> 赵六</div>
                                        <div><i class="far fa-clock"></i> 明天 12:00前</div>
                                        <div><i class="fas fa-tag"></i> 企业培训</div>
                                    </div>
                                </div>
                                <span class="task-priority priority-medium">中优先级</span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3>日程安排</h3>
                    </div>
                    <div class="card-body">
                        <div class="calendar">
                            <div class="calendar-header">2024年6月</div>
                            <div class="calendar-day">日</div>
                            <div class="calendar-day">一</div>
                            <div class="calendar-day">二</div>
                            <div class="calendar-day">三</div>
                            <div class="calendar-day">四</div>
                            <div class="calendar-day">五</div>
                            <div class="calendar-day">六</div>
                            
                            <!-- 日历日期 -->
                            <div class="calendar-date"></div>
                            <div class="calendar-date"></div>
                            <div class="calendar-date"></div>
                            <div class="calendar-date"></div>
                            <div class="calendar-date">1</div>
                            <div class="calendar-date">2</div>
                            <div class="calendar-date">3</div>
                            
                            <div class="calendar-date">4</div>
                            <div class="calendar-date">5</div>
                            <div class="calendar-date">6</div>
                            <div class="calendar-date">7</div>
                            <div class="calendar-date">8</div>
                            <div class="calendar-date">9</div>
                            <div class="calendar-date">10</div>
                            
                            <div class="calendar-date">11</div>
                            <div class="calendar-date">12</div>
                            <div class="calendar-date event">13</div>
                            <div class="calendar-date">14</div>
                            <div class="calendar-date">15</div>
                            <div class="calendar-date">16</div>
                            <div class="calendar-date">17</div>
                            
                            <div class="calendar-date">18</div>
                            <div class="calendar-date today">19</div>
                            <div class="calendar-date">20</div>
                            <div class="calendar-date event">21</div>
                            <div class="calendar-date event">22</div>
                            <div class="calendar-date">23</div>
                            <div class="calendar-date">24</div>
                            
                            <div class="calendar-date">25</div>
                            <div class="calendar-date">26</div>
                            <div class="calendar-date">27</div>
                            <div class="calendar-date">28</div>
                            <div class="calendar-date">29</div>
                            <div class="calendar-date">30</div>
                            <div class="calendar-date"></div>
                        </div>
                        
                        <div style="margin-top: 20px;">
                            <div style="font-weight: 500; margin-bottom: 10px;">今日安排</div>
                            <ul style="list-style: none; font-size: 14px;">
                                <li style="padding: 5px 0; display: flex; align-items: center;">
                                    <i class="fas fa-circle" style="color: #3498db; font-size: 8px; margin-right: 10px;"></i>
                                    <div>
                                        <div>15:30 高校实践项目评审会</div>
                                        <div style="font-size: 12px; color: var(--gray);">3号楼201会议室</div>
                                    </div>
                                </li>
                                <li style="padding: 5px 0; display: flex; align-items: center;">
                                    <i class="fas fa-circle" style="color: #e74c3c; font-size: 8px; margin-right: 10px;"></i>
                                    <div>
                                        <div>16:40 月度运营数据分析报告</div>
                                        <div style="font-size: 12px; color: var(--gray);">线上会议</div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 第三行：公告 -->
            <div class="card">
                <div class="card-header">
                    <h3>系统公告</h3>
                </div>
                <div class="card-body">
                    <div class="announcement">
                        <div class="announcement-title">
                            <i class="fas fa-bullhorn"></i> 系统维护通知
                        </div>
                        <div class="announcement-meta">
                            2024年6月18日 发布人：系统管理员
                        </div>
                        <div class="announcement-content">
                            平台将于2024年6月22日(周六)凌晨2:00-4:00进行系统维护，届时所有服务将暂停使用。请提前做好工作安排，给您带来不便敬请谅解。
                        </div>
                    </div>
                    
                    <div class="announcement">
                        <div class="announcement-title">
                            <i class="fas fa-gift"></i> 端午节放假通知
                        </div>
                        <div class="announcement-meta">
                            2024年6月17日 发布人：行政部
                        </div>
                        <div class="announcement-content">
                            根据国家法定节假日安排，公司端午节放假时间为2024年6月22日(周六)至6月24日(周一)，共3天。6月25日(周二)正常上班。节假日期间，请各部门安排好值班人员。
                        </div>
                    </div>
                    
                    <div class="announcement">
                        <div class="announcement-title">
                            <i class="fas fa-star"></i> OneID人才库功能升级
                        </div>
                        <div class="announcement-meta">
                            2024年6月15日 发布人：产品部
                        </div>
                        <div class="announcement-content">
                            人才库中枢(OneID)已完成重大升级，新增跨场景用户识别功能，支持自动合并同一用户在不同业务场景中的数据。详细更新内容请查看产品更新日志。
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 订单中心模块 -->
        <div id="order-center" class="dashboard" style="display: none;">
            <!-- 统计数据 -->
            <div class="order-stats">
                <div class="stat-card">
                    <div class="stat-number">156</div>
                    <div class="stat-label">总订单数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">23</div>
                    <div class="stat-label">待处理订单</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥2,847,590</div>
                    <div class="stat-label">本月订单金额</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">89.5%</div>
                    <div class="stat-label">订单完成率</div>
                </div>
            </div>

            <!-- 业务线标签页 -->
            <div class="order-content">
                <!-- 高校实践订单内容 -->
                <div id="practice-orders" class="business-content">
                    <div class="order-toolbar">
                        <div class="toolbar-left">
                            <select id="practice-status-filter" style="padding: 8px; border: 1px solid var(--border); border-radius: 4px;">
                                <option value="">全部订单状态</option>
                                <option value="draft">草稿</option>
                                <option value="pending_approval">待审批</option>
                                <option value="approving">审批中</option>
                                <option value="approved">已批准</option>
                                <option value="rejected">已拒绝</option>
                                <option value="pending_payment">待支付</option>
                                <option value="executing">执行中</option>
                                <option value="completed">已完成</option>
                                <option value="cancelled">已取消</option>
                            </select>
                            <select id="practice-payment-filter" style="padding: 8px; border: 1px solid var(--border); border-radius: 4px; margin-left: 10px;">
                                <option value="">全部支付状态</option>
                                <option value="pending">待支付</option>
                                <option value="paid">已支付</option>
                                <option value="refunded">已退款</option>
                                <option value="cancelled">已取消</option>
                            </select>
                            <input type="text" id="practice-search" placeholder="搜索项目名称、高校、企业..." 
                                   style="padding: 8px; border: 1px solid var(--border); border-radius: 4px; width: 250px; margin-left: 10px;">
                        </div>
                        <div class="toolbar-right">
                            <button class="btn" style="padding: 8px 15px; border: 1px solid var(--border); background: white; border-radius: 4px;">
                                <i class="fas fa-download"></i> 导出
                            </button>
                            <button class="btn-new" onclick="openPracticeDrawer('new')">
                                <i class="fas fa-plus"></i> 新建高校实践订单
                            </button>
                        </div>
                    </div>

                    <!-- 高校实践订单表格视图 -->
                    <table class="order-table">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>项目/高校</th>
                                <th>合作企业</th>
                                <th>项目周期</th>
                                <th>负责人</th>
                                <th>订单金额</th>
                                <th>订单状态</th>
                                <th>支付状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="practice-orders-tbody">
                            <!-- 内容将由JS动态生成 -->
                        </tbody>
                    </table>
                </div>

                <!-- 企业培训订单内容 -->
                <div id="training-orders" class="business-content" style="display: none;">
                                    <div class="order-toolbar">
                    <div class="toolbar-left">
                        <select id="training-status-filter" style="padding: 8px; border: 1px solid var(--border); border-radius: 4px;">
                            <option value="">全部订单状态</option>
                            <option value="草稿">草稿</option>
                            <option value="待审批">待审批</option>
                            <option value="审批中">审批中</option>
                            <option value="待支付">待支付</option>
                            <option value="待履约">待履约</option>
                            <option value="履约中">履约中</option>
                            <option value="已完成">已完成</option>
                            <option value="已关闭">已关闭</option>
                            <option value="审批驳回">审批驳回</option>
                        </select>
                        <select id="training-payment-filter" style="padding: 8px; border: 1px solid var(--border); border-radius: 4px; margin-left: 10px;">
                            <option value="">全部支付状态</option>
                            <option value="未支付">未支付</option>
                            <option value="已支付">已支付</option>
                            <option value="已退款">已退款</option>
                            <option value="已取消">已取消</option>
                        </select>
                        <input type="text" id="training-search" placeholder="搜索企业名称、培训项目..." 
                               style="padding: 8px; border: 1px solid var(--border); border-radius: 4px; width: 250px; margin-left: 10px;">
                    </div>
                    <div class="toolbar-right">
                        <button class="btn-new" onclick="openTrainingDrawer('new')">
                            <i class="fas fa-plus"></i> 新建企业培训订单
                        </button>
                    </div>
                </div>

                    <table class="order-table">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>企业名称</th>
                                <th>培训项目</th>
                                <th>培训人数</th>
                                <th>培训周期</th>
                                <th>订单金额</th>
                                <th>订单状态</th>
                                <th>支付状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="training-orders-tbody">
                            <!-- JS动态生成 -->
                        </tbody>
                    </table>
                </div>

                <!-- 个人培训报名订单内容 -->
                <div id="personal-orders" class="business-content" style="display: none;">
                    <div class="order-toolbar">
                        <div class="toolbar-left">
                            <select id="personal-status-filter" style="padding: 8px; border: 1px solid var(--border); border-radius: 4px;">
                                <option value="">全部订单状态</option>
                                <option value="待支付">待支付</option>
                                <option value="学习中">学习中</option>
                                <option value="待考试">待考试</option>
                                <option value="已完成">已完成</option>
                                <option value="已通过">已通过</option>
                                <option value="已关闭">已关闭</option>
                            </select>
                            <select id="personal-payment-filter" style="padding: 8px; border: 1px solid var(--border); border-radius: 4px; margin-left: 10px;">
                                <option value="">全部支付状态</option>
                                <option value="未支付">未支付</option>
                                <option value="已支付">已支付</option>
                                <option value="已退款">已退款</option>
                                <option value="已取消">已取消</option>
                            </select>
                            <select id="personal-type-filter" style="padding: 8px; border: 1px solid var(--border); border-radius: 4px; margin-left: 10px;">
                                <option value="">全部类型</option>
                                <option value="个人培训">个人培训</option>
                                <option value="考试认证">考试认证</option>
                            </select>
                            <input type="text" id="personal-search" placeholder="搜索学员姓名、课程名称..." 
                                   style="padding: 8px; border: 1px solid var(--border); border-radius: 4px; width: 250px; margin-left: 10px;">
                        </div>
                        <div class="toolbar-right">
                            <button class="btn-new" onclick="openPersonalDrawer('new')">
                                <i class="fas fa-plus"></i> 新建个人培训订单
                            </button>
                        </div>
                    </div>

                    <table class="order-table">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>学员姓名</th>
                                <th>订单类型</th>
                                <th>课程/考试项目</th>
                                <th>订单来源</th>
                                <th>订单金额</th>
                                <th>订单状态</th>
                                <th>支付状态</th>
                                <th>学习/考试状态</th>
                                <th>报名时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="personal-orders-tbody">
                            <!-- JS动态生成 -->
                        </tbody>
                    </table>
                </div>

                <!-- 家政服务订单内容 -->
                <div id="domestic-orders" class="business-content" style="display: none;">
                    <div class="order-toolbar">
                        <div class="toolbar-left">
                            <select id="domestic-status-filter" style="padding: 8px; border: 1px solid var(--border); border-radius: 4px;">
                                <option value="">全部订单状态</option>
                                <option value="待派单">待派单</option>
                                <option value="待服务">待服务</option>
                                <option value="服务中">服务中</option>
                                <option value="已完成">已完成</option>
                                <option value="已取消">已取消</option>
                                <option value="已关闭">已关闭</option>
                            </select>
                            <select id="domestic-payment-filter" style="padding: 8px; border: 1px solid var(--border); border-radius: 4px; margin-left: 10px;">
                                <option value="">全部支付状态</option>
                                <option value="未支付">未支付</option>
                                <option value="已支付">已支付</option>
                                <option value="已退款">已退款</option>
                                <option value="已取消">已取消</option>
                            </select>
                            <select id="domestic-type-filter" style="padding: 8px; border: 1px solid var(--border); border-radius: 4px; margin-left: 10px;">
                                <option value="">全部服务类型</option>
                                <option value="月嫂服务">月嫂服务</option>
                                <option value="育儿嫂服务">育儿嫂服务</option>
                                <option value="家务保姆">家务保姆</option>
                                <option value="小时工">小时工</option>
                                <option value="家电清洗">家电清洗</option>
                                <option value="深度保洁">深度保洁</option>
                            </select>
                            <input type="text" id="domestic-search" placeholder="搜索客户姓名、服务人员、服务机构..." 
                                   style="padding: 8px; border: 1px solid var(--border); border-radius: 4px; width: 250px; margin-left: 10px;">
                        </div>
                        <div class="toolbar-right">
                            <button class="btn-new" onclick="openDomesticDrawer('new')">
                                <i class="fas fa-plus"></i> 新建家政服务订单
                            </button>
                        </div>
                    </div>
                    <table class="order-table">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>客户信息</th>
                                <th>服务类型</th>
                                <th>服务人员</th>
                                <th>服务机构</th>
                                <th>服务金额</th>
                                <th>任务进度</th>
                                <th>订单状态</th>
                                <th>支付状态</th>
                                <th>预约时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="domestic-orders-tbody">
                            <!-- JS 动态生成 -->
                        </tbody>
                    </table>
                </div>

                <!-- 考试认证报名订单内容 -->
                <div id="certification-orders" class="business-content" style="display: none;">
                    <table class="order-table">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>考生姓名</th>
                                <th>考试项目</th>
                                <th>考试费用</th>
                                <th>支付状态</th>
                                <th>考试时间</th>
                                <th>报名时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="order-id">CE202406001</span></td>
                                <td>赵同学</td>
                                <td>高级母婴护理师认证</td>
                                <td>¥1,200</td>
                                <td><span class="status-badge status-completed">已支付</span></td>
                                <td>2024-07-15</td>
                                <td>2024-06-12</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-action btn-view">查看</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 商机中心模块 -->
        <div id="opportunity-center" class="dashboard" style="display: none;">
            <div class="dashboard-header">
                <h2><i class="fas fa-bullseye"></i> 商机中心</h2>
            </div>
            <div class="card">
                <div class="card-body">
                    商机中心内容建设中...
                </div>
            </div>
        </div>
    </div>

    <!-- 新建/编辑高校实践订单抽屉 -->
    <div id="practice-order-drawer" class="drawer">
        <div class="drawer-header">
            <span id="practice-drawer-title">新建高校实践订单</span>
            <button style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
        </div>
        <div class="drawer-body">
            <form id="practice-order-form">
                <div class="section">
                    <div class="section-title">项目信息</div>
                    <div class="form-group">
                        <label>关联商机</label>
                        <select class="form-control" name="relatedOpportunity">
                            <option value="">请选择关联商机（可选）</option>
                            <option value="OPP202406001">OPP202406001 - XX大学暑期实践项目商机</option>
                            <option value="OPP202406002">OPP202406002 - YY大学春季实习商机</option>
                            <option value="OPP202406003">OPP202406003 - ZZ科技大学AI产业认知项目商机</option>
                            <option value="OPP202406004">OPP202406004 - AA外国语大学新媒体运营项目商机</option>
                        </select>
                        <small style="color: var(--gray); font-size: 12px;">选择关联商机可自动填充部分项目信息</small>
                    </div>
                    <div class="form-group">
                        <label>关联线索</label>
                        <select class="form-control" name="relatedLead">
                            <option value="">请选择关联线索（可选）</option>
                            <option value="LEAD202406001">LEAD202406001 - 张三 138****1234 官网注册</option>
                            <option value="LEAD202406002">LEAD202406002 - 李四 139****5678 电话咨询</option>
                            <option value="LEAD202406003">LEAD202406003 - 王五 137****9012 微信咨询</option>
                            <option value="LEAD202406004">LEAD202406004 - 赵六 136****3456 线下活动</option>
                        </select>
                        <small style="color: var(--gray); font-size: 12px;">选择关联线索可自动填充客户信息</small>
                    </div>
                    <div class="form-group">
                        <label>项目名称 <span style="color:red">*</span></label>
                        <input type="text" class="form-control" name="projectName" required>
                    </div>
                    <div class="form-group">
                        <label>合作高校 <span style="color:red">*</span></label>
                        <input type="text" class="form-control" name="university" required>
                    </div>
                    <div class="form-group">
                        <label>合作企业 <span style="color:red">*</span></label>
                        <input type="text" class="form-control" name="enterpriseName" required>
                    </div>
                    <div class="form-group">
                        <label>项目周期 <span style="color:red">*</span></label>
                        <input type="date" class="form-control" name="startDate" required style="width:48%;display:inline-block;"> -
                        <input type="date" class="form-control" name="endDate" required style="width:48%;display:inline-block;float:right;">
                    </div>
                    <div class="form-group">
                        <label>项目负责人 <span style="color:red">*</span></label>
                        <input type="text" class="form-control" name="manager" required>
                    </div>
                    <div class="form-group">
                        <label>订单金额 <span style="color:red">*</span></label>
                        <input type="number" class="form-control" name="amount" min="0" required>
                    </div>
                    <div class="form-group">
                        <label>支付状态 <span style="color:red">*</span></label>
                        <select class="form-control" name="paymentStatus" id="practice-payment-status" onchange="togglePaymentInfo('practice')">
                            <option value="pending">待支付</option>
                            <option value="completed">已支付</option>
                            <option value="refunded">已退款</option>
                        </select>
                    </div>
                    
                    <!-- 支付信息区域 -->
                    <div id="practice-payment-info" style="display: none;">
                        <div class="section-title" style="margin-top: 20px; margin-bottom: 15px; color: #27ae60;">
                            <i class="fas fa-money-bill-wave"></i> 支付信息
                        </div>
                        <div class="form-group">
                            <label>收款金额 <span style="color:red">*</span></label>
                            <input type="number" class="form-control" name="actualAmount" min="0" step="0.01" placeholder="请输入实际收款金额">
                        </div>
                        <div class="form-group">
                            <label>收款方式 <span style="color:red">*</span></label>
                            <select class="form-control" name="paymentMethod">
                                <option value="">请选择收款方式</option>
                                <option value="现金">现金</option>
                                <option value="微信支付">微信支付</option>
                                <option value="支付宝">支付宝</option>
                                <option value="银行转账">银行转账</option>
                                <option value="POS机刷卡">POS机刷卡</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>收款日期 <span style="color:red">*</span></label>
                            <input type="datetime-local" class="form-control" name="paymentDate">
                        </div>
                        <div class="form-group">
                            <label>操作人 <span style="color:red">*</span></label>
                            <input type="text" class="form-control" name="operator" placeholder="请输入操作人姓名">
                        </div>
                        <div class="form-group">
                            <label>收款备注</label>
                            <textarea class="form-control" name="remarks" rows="3" placeholder="请输入收款备注信息（可选）"></textarea>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>合同类型</label>
                        <div>
                            <input type="radio" id="contract-type-electronic" name="contractType" value="electronic" checked>
                            <label for="contract-type-electronic" style="font-weight:normal; display:inline-block; margin-right: 15px;">电子合同</label>
                            <input type="radio" id="contract-type-paper" name="contractType" value="paper">
                            <label for="contract-type-paper" style="font-weight:normal; display:inline-block;">纸质合同</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>纸质合同附件</label>
                        <input type="file" class="form-control" name="paperContract" accept=".pdf,.doc,.docx,.jpg,.png">
                        <small id="paper-contract-filename" style="margin-top: 5px; color: var(--gray); display: block;"></small>
                    </div>
                </div>
            </form>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline">取消</button>
            <button class="btn btn-primary">保存</button>
        </div>
    </div>

    <!-- 查看详情抽屉 -->
    <div id="practice-detail-drawer" class="drawer">
        <div class="drawer-header">
            <span>高校实践订单详情</span>
            <button style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
        </div>
        <div class="drawer-body" id="practice-detail-body">
            <!-- 详情内容由JS动态渲染 -->
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline">关闭</button>
            <button class="btn btn-primary">编辑</button>
        </div>
    </div>

    <!-- 新建/编辑企业培训订单抽屉 -->
    <div id="training-order-drawer" class="drawer">
        <div class="drawer-header">
            <span id="training-drawer-title">新建企业培训订单</span>
            <button style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
        </div>
        <div class="drawer-body">
            <form id="training-order-form">
                <div class="section">
                    <div class="section-title">订单信息</div>
                    <div class="form-group">
                        <label>关联商机</label>
                        <select class="form-control" name="relatedOpportunity">
                            <option value="">请选择关联商机（可选）</option>
                            <option value="OPP202406005">OPP202406005 - XX企业员工培训项目商机</option>
                            <option value="OPP202406006">OPP202406006 - YY公司技能提升培训商机</option>
                            <option value="OPP202406007">OPP202406007 - ZZ集团管理培训商机</option>
                            <option value="OPP202406008">OPP202406008 - AA科技公司技术培训商机</option>
                        </select>
                        <small style="color: var(--gray); font-size: 12px;">选择关联商机可自动填充部分企业信息</small>
                    </div>
                    <div class="form-group">
                        <label>关联线索</label>
                        <select class="form-control" name="relatedLead">
                            <option value="">请选择关联线索（可选）</option>
                            <option value="LEAD202406005">LEAD202406005 - 陈经理 135****7890 企业咨询</option>
                            <option value="LEAD202406006">LEAD202406006 - 刘总监 134****1234 培训需求</option>
                            <option value="LEAD202406007">LEAD202406007 - 张总 133****5678 合作洽谈</option>
                            <option value="LEAD202406008">LEAD202406008 - 王经理 132****9012 技能培训</option>
                        </select>
                        <small style="color: var(--gray); font-size: 12px;">选择关联线索可自动填充企业信息</small>
                    </div>
                    <div class="form-group">
                        <label>企业名称 <span style="color:red">*</span></label>
                        <input type="text" class="form-control" name="enterpriseName" required>
                    </div>
                    <div class="form-group">
                        <label>培训项目 <span style="color:red">*</span></label>
                        <input type="text" class="form-control" name="trainingProjectName" required>
                    </div>
                     <div class="form-group">
                        <label>培训周期 <span style="color:red">*</span></label>
                        <input type="date" class="form-control" name="startDate" required style="width:48%;display:inline-block;"> -
                        <input type="date" class="form-control" name="endDate" required style="width:48%;display:inline-block;float:right;">
                    </div>
                    <div class="form-group">
                        <label>培训人数 <span style="color:red">*</span></label>
                        <input type="number" class="form-control" name="participantsCount" min="1" required>
                    </div>
                    <div class="form-group">
                        <label>订单金额 <span style="color:red">*</span></label>
                        <input type="number" class="form-control" name="amount" min="0" required>
                    </div>
                    <div class="form-group">
                        <label>支付状态 <span style="color:red">*</span></label>
                        <select class="form-control" name="paymentStatus" id="training-payment-status" onchange="togglePaymentInfo('training')">
                            <option value="pending">待支付</option>
                            <option value="completed">已支付</option>
                            <option value="refunded">已退款</option>
                        </select>
                    </div>
                    
                    <!-- 支付信息区域 -->
                    <div id="training-payment-info" style="display: none;">
                        <div class="section-title" style="margin-top: 20px; margin-bottom: 15px; color: #27ae60;">
                            <i class="fas fa-money-bill-wave"></i> 支付信息
                        </div>
                        <div class="form-group">
                            <label>收款金额 <span style="color:red">*</span></label>
                            <input type="number" class="form-control" name="actualAmount" min="0" step="0.01" placeholder="请输入实际收款金额">
                        </div>
                        <div class="form-group">
                            <label>收款方式 <span style="color:red">*</span></label>
                            <select class="form-control" name="paymentMethod">
                                <option value="">请选择收款方式</option>
                                <option value="现金">现金</option>
                                <option value="微信支付">微信支付</option>
                                <option value="支付宝">支付宝</option>
                                <option value="银行转账">银行转账</option>
                                <option value="POS机刷卡">POS机刷卡</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>收款日期 <span style="color:red">*</span></label>
                            <input type="datetime-local" class="form-control" name="paymentDate">
                        </div>
                        <div class="form-group">
                            <label>操作人 <span style="color:red">*</span></label>
                            <input type="text" class="form-control" name="operator" placeholder="请输入操作人姓名">
                        </div>
                        <div class="form-group">
                            <label>收款备注</label>
                            <textarea class="form-control" name="remarks" rows="3" placeholder="请输入收款备注信息（可选）"></textarea>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>我方负责人 <span style="color:red">*</span></label>
                        <input type="text" class="form-control" name="manager" required>
                    </div>
                    <div class="form-group">
                        <label>纸质合同</label>
                        <input type="file" class="form-control" name="paperContract" accept=".pdf,.doc,.docx,.jpg,.png">
                        <small id="training-paper-contract-filename" style="margin-top: 5px; color: var(--gray); display: block;"></small>
                    </div>
                </div>
            </form>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline">取消</button>
            <button class="btn btn-primary">保存</button>
        </div>
    </div>

    <!-- 查看企业培训订单详情抽屉 -->
    <div id="training-detail-drawer" class="drawer">
        <div class="drawer-header">
            <span>企业培训订单详情</span>
            <button style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
        </div>
        <div class="drawer-body" id="training-detail-body">
            <!-- 详情内容由JS动态渲染 -->
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline">关闭</button>
            <button class="btn btn-primary">编辑</button>
        </div>
    </div>

    <!-- 新建/编辑个人培训订单抽屉 -->
    <div id="personal-order-drawer" class="drawer">
        <div class="drawer-header">
            <span id="personal-drawer-title">新建个人培训订单</span>
            <button style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
        </div>
        <div class="drawer-body">
            <form id="personal-order-form">
                <div class="section">
                    <div class="section-title">订单信息</div>
                    <div class="form-group">
                        <label>关联商机</label>
                        <select class="form-control" name="relatedOpportunity">
                            <option value="">请选择关联商机（可选）</option>
                            <option value="OPP202406009">OPP202406009 - 个人培训课程推广商机</option>
                            <option value="OPP202406010">OPP202406010 - 技能认证考试推广商机</option>
                            <option value="OPP202406011">OPP202406011 - 在线课程学习商机</option>
                            <option value="OPP202406012">OPP202406012 - 职业发展培训商机</option>
                        </select>
                        <small style="color: var(--gray); font-size: 12px;">选择关联商机可自动填充部分课程信息</small>
                    </div>
                    <div class="form-group">
                        <label>关联线索</label>
                        <select class="form-control" name="relatedLead">
                            <option value="">请选择关联线索（可选）</option>
                            <option value="LEAD202406009">LEAD202406009 - 孙女士 131****3456 个人培训咨询</option>
                            <option value="LEAD202406010">LEAD202406010 - 周先生 130****7890 认证考试咨询</option>
                            <option value="LEAD202406011">LEAD202406011 - 吴女士 129****1234 在线学习咨询</option>
                            <option value="LEAD202406012">LEAD202406012 - 郑先生 128****5678 职业发展咨询</option>
                        </select>
                        <small style="color: var(--gray); font-size: 12px;">选择关联线索可自动填充学员信息</small>
                    </div>
                    <div class="form-group">
                        <label>订单类型 <span style="color:red">*</span></label>
                        <div>
                            <input type="radio" id="type-training" name="orderType" value="个人培训" checked>
                            <label for="type-training" style="font-weight:normal; display:inline-block; margin-right: 15px;">个人培训</label>
                            <input type="radio" id="type-certification" name="orderType" value="考试认证">
                            <label for="type-certification" style="font-weight:normal; display:inline-block;">考试认证</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>学员姓名 <span style="color:red">*</span></label>
                        <input type="text" class="form-control" name="studentName" required>
                    </div>
                    <div class="form-group">
                        <label>课程/考试项目 <span style="color:red">*</span></label>
                        <select class="form-control" name="courseName" required>
                            <option value="项目管理PMP认证课程">项目管理PMP认证课程</option>
                            <option value="数据分析师认证课程">数据分析师认证课程</option>
                            <option value="高级母婴护理师认证">高级母婴护理师认证</option>
                            <option value="营养师资格认证">营养师资格认证</option>
                            <option value="Python编程基础课程">Python编程基础课程</option>
                            <option value="心理咨询师三级认证">心理咨询师三级认证</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>订单来源 <span style="color:red">*</span></label>
                        <select class="form-control" name="orderSource">
                            <option value="线上小程序">线上小程序</option>
                            <option value="App">App</option>
                            <option value="线下报名">线下报名</option>
                            <option value="代理推荐">代理推荐</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>订单金额 <span style="color:red">*</span></label>
                        <input type="number" class="form-control" name="amount" min="0" required>
                    </div>
                    <div class="form-group">
                        <label>支付状态 <span style="color:red">*</span></label>
                        <select class="form-control" name="paymentStatus" id="personal-payment-status" onchange="togglePaymentInfo('personal')">
                            <option value="pending">待支付</option>
                            <option value="completed">已支付</option>
                             <option value="refunded">已退款</option>
                        </select>
                    </div>
                    
                    <!-- 支付信息区域 -->
                    <div id="personal-payment-info" style="display: none;">
                        <div class="section-title" style="margin-top: 20px; margin-bottom: 15px; color: #27ae60;">
                            <i class="fas fa-money-bill-wave"></i> 支付信息
                        </div>
                        <div class="form-group">
                            <label>收款金额 <span style="color:red">*</span></label>
                            <input type="number" class="form-control" name="actualAmount" min="0" step="0.01" placeholder="请输入实际收款金额">
                        </div>
                        <div class="form-group">
                            <label>收款方式 <span style="color:red">*</span></label>
                            <select class="form-control" name="paymentMethod">
                                <option value="">请选择收款方式</option>
                                <option value="现金">现金</option>
                                <option value="微信支付">微信支付</option>
                                <option value="支付宝">支付宝</option>
                                <option value="银行转账">银行转账</option>
                                <option value="POS机刷卡">POS机刷卡</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>收款日期 <span style="color:red">*</span></label>
                            <input type="datetime-local" class="form-control" name="paymentDate">
                        </div>
                        <div class="form-group">
                            <label>操作人 <span style="color:red">*</span></label>
                            <input type="text" class="form-control" name="operator" placeholder="请输入操作人姓名">
                        </div>
                        <div class="form-group">
                            <label>收款备注</label>
                            <textarea class="form-control" name="remarks" rows="3" placeholder="请输入收款备注信息（可选）"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>学习/考试状态 <span style="color:red">*</span></label>
                        <select class="form-control" name="learningStatus">
                            <option value="pending">未开始</option>
                            <option value="processing">学习中/待考试</option>
                            <option value="completed">已完成/已通过</option>
                        </select>
                    </div>
                </div>
                
                <!-- 合同管理区域 -->
                <div class="section">
                    <div class="section-title">合同管理</div>
                    <div class="form-group">
                        <label>合同类型</label>
                        <div>
                            <input type="radio" id="contract-electronic" name="contractType" value="electronic" checked>
                            <label for="contract-electronic" style="font-weight:normal; display:inline-block; margin-right: 15px;">电子合同</label>
                            <input type="radio" id="contract-paper" name="contractType" value="paper">
                            <label for="contract-paper" style="font-weight:normal; display:inline-block;">纸质合同</label>
                        </div>
                    </div>
                    
                    <!-- 电子合同选项 -->
                    <div id="electronic-contract-options">
                        <div class="form-group">
                            <label>合同模板</label>
                            <select class="form-control" name="contractTemplate">
                                <option value="">请选择合同模板</option>
                                <option value="personal-training-agreement">个人培训协议模板</option>
                                <option value="certification-agreement">考试认证协议模板</option>
                                <option value="online-course-agreement">在线课程协议模板</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 纸质合同选项 -->
                    <div id="paper-contract-options" style="display: none;">
                        <div class="form-group">
                            <label>合同附件</label>
                            <input type="file" class="form-control" name="paperContract" accept=".pdf,.doc,.docx,.jpg,.png">
                            <small id="personal-paper-contract-filename" style="margin-top: 5px; color: var(--gray); display: block;"></small>
                        </div>
                        <div class="form-group">
                            <label>合同编号</label>
                            <input type="text" class="form-control" name="contractNumber" placeholder="请输入合同编号">
                        </div>
                        <div class="form-group">
                            <label>合同名称</label>
                            <input type="text" class="form-control" name="contractName" placeholder="请输入合同名称">
                        </div>
                        <div class="form-group">
                            <label>签署日期</label>
                            <input type="date" class="form-control" name="contractSignDate">
                        </div>
                        <div class="form-group">
                            <label>合同金额</label>
                            <input type="number" class="form-control" name="contractAmount" min="0" placeholder="请输入合同金额">
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline">取消</button>
            <button class="btn btn-primary">保存</button>
        </div>
    </div>

    <!-- 查看个人培训订单详情抽屉 -->
    <div id="personal-detail-drawer" class="drawer">
        <div class="drawer-header">
            <span>个人培训订单详情</span>
            <button style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
        </div>
        <div class="drawer-body" id="personal-detail-body">
            <!-- 详情内容由JS动态渲染 -->
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closePersonalDetailDrawer()">关闭</button>
            <button class="btn btn-primary" onclick="editPersonalFromDetailView()">编辑</button>
        </div>
    </div>

    <!-- 新建/编辑家政服务订单抽屉 -->
    <div id="domestic-order-drawer" class="drawer">
        <div class="drawer-header">
            <span id="domestic-drawer-title">新建家政服务订单</span>
            <button onclick="closeDomesticDrawer()" style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
        </div>
        <div class="drawer-body">
            <form id="domestic-order-form">
                <div class="section">
                    <div class="section-title">客户与服务信息</div>
                    <div class="form-group">
                        <label>关联商机</label>
                        <select class="form-control" name="relatedOpportunity">
                            <option value="">请选择关联商机（可选）</option>
                            <option value="OPP202406013">OPP202406013 - 家政服务推广商机</option>
                            <option value="OPP202406014">OPP202406014 - 月嫂服务需求商机</option>
                            <option value="OPP202406015">OPP202406015 - 保洁服务需求商机</option>
                            <option value="OPP202406016">OPP202406016 - 育儿嫂服务商机</option>
                        </select>
                        <small style="color: var(--gray); font-size: 12px;">选择关联商机可自动填充部分服务信息</small>
                    </div>
                    <div class="form-group">
                        <label>关联线索</label>
                        <select class="form-control" name="relatedLead">
                            <option value="">请选择关联线索（可选）</option>
                            <option value="LEAD202406013">LEAD202406013 - 林女士 127****9012 家政服务咨询</option>
                            <option value="LEAD202406014">LEAD202406014 - 黄先生 126****3456 月嫂服务咨询</option>
                            <option value="LEAD202406015">LEAD202406015 - 马女士 125****7890 保洁服务咨询</option>
                            <option value="LEAD202406016">LEAD202406016 - 朱先生 124****1234 育儿嫂咨询</option>
                        </select>
                        <small style="color: var(--gray); font-size: 12px;">选择关联线索可自动填充客户信息</small>
                    </div>
                    <div class="form-group">
                        <label>客户姓名 <span style="color:red">*</span></label>
                        <input type="text" class="form-control" name="customerName" required>
                    </div>
                    <div class="form-group">
                        <label>联系电话 <span style="color:red">*</span></label>
                        <input type="tel" class="form-control" name="customerPhone" required>
                    </div>
                    <div class="form-group">
                        <label>服务地址 <span style="color:red">*</span></label>
                        <input type="text" class="form-control" name="serviceAddress" required>
                    </div>
                    <div class="form-group">
                        <label>服务套餐 <span style="color:red">*</span></label>
                        <select class="form-control" name="servicePackage" id="service-package-select" required onchange="onPackageChange()">
                            <option value="">请选择服务套餐</option>
                            <option value="SP001" data-price="150.00" data-name="日常保洁-3小时">SP001 - 日常保洁-3小时 (¥150.00)</option>
                            <option value="SP002" data-price="188.00" data-name="油烟机深度清洗">SP002 - 油烟机深度清洗 (¥188.00)</option>
                            <option value="SP003" data-price="99.00" data-name="冰箱除菌清洁">SP003 - 冰箱除菌清洁 (¥99.00)</option>
                        </select>
                        <small style="color: var(--gray); font-size: 12px;">选择套餐后会自动填充服务类型和订单金额</small>
                    </div>
                    <div class="form-group">
                        <label>服务类型 <span style="color:red">*</span></label>
                        <select class="form-control" name="serviceType" id="service-type-select" required>
                            <option value="">请先选择服务套餐</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>预约上门时间 <span style="color:red">*</span></label>
                        <input type="datetime-local" class="form-control" name="appointmentTime" required>
                    </div>
                    <div class="form-group">
                        <label>指派服务人员 <span style="color:red">*</span></label>
                        <input type="text" class="form-control" name="servicePerson" required>
                    </div>
                    <div class="form-group">
                        <label>服务机构</label>
                        <select class="form-control" name="serviceAgency">
                            <option value="">请选择服务机构（可选）</option>
                            <option value="爱家月嫂服务中心">爱家月嫂服务中心</option>
                            <option value="清洁无忧家政公司">清洁无忧家政公司</option>
                            <option value="母婴护理专家机构">母婴护理专家机构</option>
                            <option value="汇成家政服务公司">汇成家政服务公司</option>
                            <option value="其他">其他</option>
                        </select>
                        <small style="color: var(--gray); font-size: 12px;">如果选择服务机构，服务人员将从该机构指派</small>
                    </div>
                    <div class="form-group">
                        <label>订单金额 <span style="color:red">*</span></label>
                        <input type="number" class="form-control" name="amount" min="0" required>
                    </div>
                    <div class="form-group">
                        <label>支付状态 <span style="color:red">*</span></label>
                        <select class="form-control" name="paymentStatus" id="domestic-payment-status" onchange="togglePaymentInfo('domestic')">
                            <option value="未支付">未支付</option>
                            <option value="已支付">已支付</option>
                            <option value="已退款">已退款</option>
                            <option value="已取消">已取消</option>
                        </select>
                    </div>
                    
                    <!-- 支付信息区域 -->
                    <div id="domestic-payment-info" style="display: none;">
                        <div class="section-title" style="margin-top: 20px; margin-bottom: 15px; color: #27ae60;">
                            <i class="fas fa-money-bill-wave"></i> 支付信息
                        </div>
                        <div class="form-group">
                            <label>收款金额 <span style="color:red">*</span></label>
                            <input type="number" class="form-control" name="actualAmount" min="0" step="0.01" placeholder="请输入实际收款金额">
                        </div>
                        <div class="form-group">
                            <label>收款方式 <span style="color:red">*</span></label>
                            <select class="form-control" name="paymentMethod">
                                <option value="">请选择收款方式</option>
                                <option value="现金">现金</option>
                                <option value="微信支付">微信支付</option>
                                <option value="支付宝">支付宝</option>
                                <option value="银行转账">银行转账</option>
                                <option value="POS机刷卡">POS机刷卡</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>收款日期 <span style="color:red">*</span></label>
                            <input type="datetime-local" class="form-control" name="paymentDate">
                        </div>
                        <div class="form-group">
                            <label>操作人 <span style="color:red">*</span></label>
                            <input type="text" class="form-control" name="operator" placeholder="请输入操作人姓名">
                        </div>
                        <div class="form-group">
                            <label>收款备注</label>
                            <textarea class="form-control" name="remarks" rows="3" placeholder="请输入收款备注信息（可选）"></textarea>
                        </div>
                    </div>
                </div>
                
                <!-- 合同管理区域 -->
                <div class="section" style="display: none;">
                    <div class="section-title">合同管理</div>
                    <div class="form-group">
                        <label>合同类型</label>
                        <div>
                            <input type="radio" id="domestic-contract-electronic" name="contractType" value="electronic" checked>
                            <label for="domestic-contract-electronic" style="font-weight:normal; display:inline-block; margin-right: 15px;">电子合同</label>
                            <input type="radio" id="domestic-contract-paper" name="contractType" value="paper">
                            <label for="domestic-contract-paper" style="font-weight:normal; display:inline-block;">纸质合同</label>
                        </div>
                    </div>
                    
                    <!-- 电子合同选项 -->
                    <div id="domestic-electronic-contract-options">
                        <div class="form-group">
                            <label>合同模板</label>
                            <select class="form-control" name="contractTemplate">
                                <option value="">请选择合同模板</option>
                                <option value="domestic-service-agreement">家政服务协议模板</option>
                                <option value="maternity-care-agreement">月嫂服务协议模板</option>
                                <option value="cleaning-service-agreement">保洁服务协议模板</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 纸质合同选项 -->
                    <div id="domestic-paper-contract-options" style="display: none;">
                        <div class="form-group">
                            <label>合同附件</label>
                            <input type="file" class="form-control" name="paperContract" accept=".pdf,.doc,.docx,.jpg,.png">
                            <small id="domestic-paper-contract-filename" style="margin-top: 5px; color: var(--gray); display: block;"></small>
                        </div>
                        <div class="form-group">
                            <label>合同编号</label>
                            <input type="text" class="form-control" name="contractNumber" placeholder="请输入合同编号">
                        </div>
                        <div class="form-group">
                            <label>合同周期 <span style="color:red">*</span></label>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <input type="date" class="form-control" name="contractStartDate" required>
                                <span style="font-weight: bold;">-</span>
                                <input type="date" class="form-control" name="contractEndDate" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>合同名称</label>
                            <input type="text" class="form-control" name="contractName" placeholder="请输入合同名称">
                        </div>
                        <div class="form-group">
                            <label>签署日期</label>
                            <input type="date" class="form-control" name="contractSignDate">
                        </div>
                        <div class="form-group">
                            <label>合同金额</label>
                            <input type="number" class="form-control" name="contractAmount" min="0" placeholder="请输入合同金额">
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDomesticDrawer()">取消</button>
            <button class="btn btn-primary" onclick="submitDomesticOrder()">保存</button>
        </div>
    </div>

    <!-- 查看家政服务订单详情抽屉 -->
    <div id="domestic-detail-drawer" class="drawer">
        <div class="drawer-header">
            <span>家政服务订单详情</span>
            <button onclick="closeDomesticDetailDrawer()" style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
        </div>
        <div class="drawer-body" id="domestic-detail-body">
            <!-- 详情内容由JS动态渲染 -->
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDomesticDetailDrawer()">关闭</button>
            <button class="btn btn-primary" onclick="editDomesticFromDetailView()">编辑</button>
        </div>
    </div>

    <!-- 售后工单抽屉 -->
    <div id="domestic-after-sales-drawer" class="drawer">
        <div class="drawer-header">
            <span id="after-sales-drawer-title">新建售后工单</span>
            <button onclick="closeDomesticAfterSalesDrawer()" style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
        </div>
        <div class="drawer-body">
            <form id="domestic-after-sales-form">
                <input type="hidden" name="ticketId">
                <div class="section">
                    <div class="section-title">售后信息</div>
                    <div class="form-group">
                        <label>工单类型 <span style="color:red">*</span></label>
                        <select class="form-control" name="type" required>
                            <option value="物品损坏">物品损坏</option>
                            <option value="服务质量投诉">服务质量投诉</option>
                            <option value="服务人员投诉">服务人员投诉</option>
                            <option value="申请返工">申请返工</option>
                            <option value="申请退款">申请退款</option>
                            <option value="其他咨询">其他咨询</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>问题描述 <span style="color:red">*</span></label>
                        <textarea class="form-control" name="description" rows="4" required></textarea>
                    </div>
                     <div class="form-group">
                        <label>工单状态 <span style="color:red">*</span></label>
                        <select class="form-control" name="status" required>
                            <option value="待处理">待处理</option>
                            <option value="处理中">处理中</option>
                            <option value="已解决">已解决</option>
                             <option value="已关闭">已关闭</option>
                        </select>
                    </div>
                     <div class="form-group">
                        <label>处理结果</label>
                        <textarea class="form-control" name="resolution" rows="4"></textarea>
                    </div>
                </div>
            </form>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDomesticAfterSalesDrawer()">取消</button>
            <button class="btn btn-primary" onclick="submitDomesticAfterSales()">保存</button>
        </div>
    </div>

    <!-- 订单评价抽屉 -->
    <div id="domestic-evaluation-drawer" class="drawer">
        <div class="drawer-header">
            <span>订单评价</span>
            <button onclick="closeDomesticEvaluationDrawer()" style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
        </div>
        <div class="drawer-body">
            <form id="domestic-evaluation-form">
                <input type="hidden" name="orderId">
                <div class="section">
                    <div class="section-title">服务评分</div>
                    <div class="form-group">
                        <label>总体满意度</label>
                        <div class="stars-input" style="font-size: 24px; color: var(--gray); cursor: pointer;">
                            <i class="fas fa-star" data-value="1"></i>
                            <i class="fas fa-star" data-value="2"></i>
                            <i class="fas fa-star" data-value="3"></i>
                            <i class="fas fa-star" data-value="4"></i>
                            <i class="fas fa-star" data-value="5"></i>
                        </div>
                        <input type="hidden" name="rating" required>
                    </div>
                    <div class="form-group">
                        <label>评价标签 (多个请用英文逗号,隔开)</label>
                        <input type="text" class="form-control" name="tags" placeholder="例如：非常专业,准时到达,服务态度好">
                    </div>
                    <div class="form-group">
                        <label>详细评价</label>
                        <textarea class="form-control" name="comment" rows="5" placeholder="请在此处填写您的详细评价..."></textarea>
                    </div>
                </div>
            </form>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDomesticEvaluationDrawer()">取消</button>
            <button class="btn btn-primary" onclick="submitDomesticEvaluation()">提交评价</button>
        </div>
    </div>

    <!-- 审批弹窗 -->
    <div id="approval-modal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h3 id="approval-modal-title">订单审批</h3>
                <button onclick="closeApprovalModal()" style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
            </div>
            <div class="modal-body">
                <form id="approval-form">
                    <input type="hidden" id="approval-order-id">
                    <input type="hidden" id="approval-order-type">
                    <div class="form-group">
                        <label>审批结果 <span style="color:red">*</span></label>
                        <div style="margin-top: 10px;">
                            <input type="radio" id="approval-approve" name="approvalResult" value="approve" checked>
                            <label for="approval-approve" style="font-weight:normal; display:inline-block; margin-right: 20px; color: var(--success);">
                                <i class="fas fa-check-circle"></i> 同意
                            </label>
                            <input type="radio" id="approval-reject" name="approvalResult" value="reject">
                            <label for="approval-reject" style="font-weight:normal; display:inline-block; color: var(--danger);">
                                <i class="fas fa-times-circle"></i> 拒绝
                            </label>
                        </div>
                    </div>
                    <div class="form-group" id="reject-reason-group" style="display: none;">
                        <label>拒绝原因 <span style="color:red">*</span></label>
                        <textarea class="form-control" id="reject-reason" rows="4" placeholder="请输入拒绝原因..." style="resize: vertical;"></textarea>
                    </div>
                    <div class="form-group">
                        <label>审批意见</label>
                        <textarea class="form-control" id="approval-comments" rows="3" placeholder="请输入审批意见（可选）..." style="resize: vertical;"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeApprovalModal()">取消</button>
                <button class="btn btn-primary" id="approval-submit-btn" onclick="submitApproval()">提交审批</button>
            </div>
        </div>
    </div>

    <!-- 收款确认弹窗 -->
    <div id="payment-modal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h3 id="payment-modal-title">收款确认</h3>
                <button onclick="closePaymentModal()" style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
            </div>
            <div class="modal-body">
                <form id="payment-form">
                    <input type="hidden" id="payment-order-id">
                    <input type="hidden" id="payment-order-type">
                    <div class="form-group">
                        <label>订单金额</label>
                        <div id="payment-order-amount" style="font-size: 18px; font-weight: bold; color: var(--primary); margin: 10px 0;"></div>
                    </div>
                    <div class="form-group">
                        <label>实际收款金额 <span style="color:red">*</span></label>
                        <input type="number" class="form-control" id="payment-actual-amount" step="0.01" min="0" required placeholder="请输入实际收款金额">
                    </div>
                    <div class="form-group">
                        <label>收款方式 <span style="color:red">*</span></label>
                        <select class="form-control" id="payment-method" required>
                            <option value="">请选择收款方式</option>
                            <option value="bank_transfer">银行转账</option>
                            <option value="alipay">支付宝</option>
                            <option value="wechat">微信支付</option>
                            <option value="cash">现金</option>
                            <option value="check">支票</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>收款日期 <span style="color:red">*</span></label>
                        <input type="date" class="form-control" id="payment-date" required>
                    </div>
                    <div class="form-group">
                        <label>收款备注</label>
                        <textarea class="form-control" id="payment-remarks" rows="3" placeholder="请输入收款备注（可选）..." style="resize: vertical;"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closePaymentModal()">取消</button>
                <button class="btn btn-success" onclick="confirmPayment()">确认收款</button>
            </div>
        </div>
    </div>

    <!-- 合同管理弹窗 -->
    <div id="contract-modal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3 id="contract-modal-title">合同管理</h3>
                <button onclick="closeContractModal()" style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
            </div>
            <div class="modal-body">
                <form id="contract-form">
                    <input type="hidden" id="contract-order-id">
                    <input type="hidden" id="contract-order-type">
                    <input type="hidden" id="contract-type">
                    
                    <!-- 电子合同表单 -->
                    <div id="electronic-contract-form" style="display: none;">
                        <div class="form-group">
                            <label>合同模板 <span style="color:red">*</span></label>
                            <select id="contract-template" class="form-control" required>
                                <option value="">请选择合同模板</option>
                                <option value="template1">高校实践三方协议模板</option>
                                <option value="template2">校企合作协议模板</option>
                                <option value="template3">实习实践协议模板</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>合同名称 <span style="color:red">*</span></label>
                            <input type="text" id="contract-name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>签署方信息</label>
                            <div style="background: #f8f9fa; padding: 10px; border-radius: 4px;">
                                <div><strong>平台方：</strong>川能投教育科技有限公司</div>
                                <div><strong>高校方：</strong><span id="university-party">待填写</span></div>
                                <div><strong>企业方：</strong><span id="enterprise-party">待填写</span></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 纸质合同表单 -->
                    <div id="paper-contract-form" style="display: none;">
                        <div class="form-group">
                            <label>合同附件 <span style="color:red">*</span></label>
                            <input type="file" id="contract-file" class="form-control" accept=".pdf,.doc,.docx,.jpg,.png" required>
                            <small style="color: var(--gray);">支持格式：PDF、Word、JPG、PNG，文件大小不超过10MB</small>
                        </div>
                        <div class="form-group">
                            <label>合同编号 <span style="color:red">*</span></label>
                            <input type="text" id="contract-number" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>合同名称 <span style="color:red">*</span></label>
                            <input type="text" id="paper-contract-name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>签署日期 <span style="color:red">*</span></label>
                            <input type="date" id="contract-sign-date" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>合同金额 <span style="color:red">*</span></label>
                            <input type="number" id="contract-amount" class="form-control" required>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeContractModal()">取消</button>
                <button class="btn btn-primary" onclick="submitContract()">提交</button>
            </div>
        </div>
    </div>

    <!-- 记一笔抽屉 -->
    <div id="financial-record-drawer" class="drawer">
        <div class="drawer-header">
            <span id="financial-record-drawer-title">记一笔</span>
            <button onclick="closeFinancialRecordDrawer()" style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
        </div>
        <div class="drawer-body">
            <form id="financial-record-form">
                <input type="hidden" name="orderId">
                <input type="hidden" name="recordId">
                <div class="section">
                    <div class="section-title">收支信息</div>
                    <div class="form-group">
                        <label>类型 <span style="color:red">*</span></label>
                        <select class="form-control" name="type" required>
                            <option value="income">额外收入</option>
                            <option value="expense">赔偿支出</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>金额 <span style="color:red">*</span></label>
                        <input type="number" class="form-control" name="amount" min="0" required>
                    </div>
                    <div class="form-group">
                        <label>发生日期 <span style="color:red">*</span></label>
                        <input type="date" class="form-control" name="date" required>
                    </div>
                    <div class="form-group">
                        <label>描述 <span style="color:red">*</span></label>
                        <textarea class="form-control" name="description" rows="4" required></textarea>
                    </div>
                </div>
            </form>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeFinancialRecordDrawer()">取消</button>
            <button class="btn btn-primary" onclick="submitFinancialRecord()">保存</button>
        </div>
    </div>

    <!-- 操作日志抽屉 -->
    <div id="operation-log-drawer" class="operation-log-drawer">
        <div class="drawer-header">
            <span id="operation-log-title">操作日志</span>
            <button onclick="closeOperationLog()" style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
        </div>
        <div class="drawer-body" id="operation-log-body">
            <!-- 操作日志内容由JS动态渲染 -->
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeOperationLog()">关闭</button>
        </div>
    </div>

    <!-- 派单抽屉 -->
    <div id="dispatch-drawer" class="dispatch-drawer drawer">
        <div class="drawer-header">
            <span id="dispatch-title">派单给服务人员</span>
            <button onclick="closeDispatchDrawer()" style="background:none;border:none;font-size:20px;cursor:pointer;">&times;</button>
        </div>
        
        <div class="dispatch-tabs">
            <div class="dispatch-tab active" onclick="switchDispatchTab('individual')">
                <i class="fas fa-user"></i> 个人阿姨
            </div>
            <div class="dispatch-tab" onclick="switchDispatchTab('agency')">
                <i class="fas fa-building"></i> 服务机构
            </div>
        </div>
        
        <div class="dispatch-content">
            <!-- 个人阿姨派单 -->
            <div id="individual-dispatch" class="dispatch-section active">
                <div class="dispatch-summary">
                    <h4 style="margin: 0 0 15px 0;">订单信息</h4>
                    <div class="summary-item">
                        <span class="summary-label">客户姓名:</span>
                        <span class="summary-value" id="dispatch-customer-name">-</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">服务类型:</span>
                        <span class="summary-value" id="dispatch-service-type">-</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">预约时间:</span>
                        <span class="summary-value" id="dispatch-appointment-time">-</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">服务地址:</span>
                        <span class="summary-value" id="dispatch-service-address">-</span>
                    </div>
                </div>
                
                <div class="search-filter">
                    <input type="text" id="individual-search" placeholder="搜索阿姨姓名、技能、评分..." onkeyup="filterIndividualProviders()">
                    <div class="filter-tags">
                        <span class="filter-tag active" data-skill="all" onclick="filterBySkill('all')">全部技能</span>
                        <span class="filter-tag" data-skill="月嫂" onclick="filterBySkill('月嫂')">月嫂</span>
                        <span class="filter-tag" data-skill="育儿嫂" onclick="filterBySkill('育儿嫂')">育儿嫂</span>
                        <span class="filter-tag" data-skill="家务保姆" onclick="filterBySkill('家务保姆')">家务保姆</span>
                        <span class="filter-tag" data-skill="小时工" onclick="filterBySkill('小时工')">小时工</span>
                        <span class="filter-tag" data-skill="深度保洁" onclick="filterBySkill('深度保洁')">深度保洁</span>
                    </div>
                </div>
                
                <div class="provider-list" id="individual-provider-list">
                    <!-- 个人阿姨列表由JS动态渲染 -->
                </div>
            </div>
            
            <!-- 服务机构派单 -->
            <div id="agency-dispatch" class="dispatch-section">
                <div class="dispatch-summary">
                    <h4 style="margin: 0 0 15px 0;">订单信息</h4>
                    <div class="summary-item">
                        <span class="summary-label">客户姓名:</span>
                        <span class="summary-value" id="dispatch-customer-name-agency">-</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">服务类型:</span>
                        <span class="summary-value" id="dispatch-service-type-agency">-</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">预约时间:</span>
                        <span class="summary-value" id="dispatch-appointment-time-agency">-</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">服务地址:</span>
                        <span class="summary-value" id="dispatch-service-address-agency">-</span>
                    </div>
                </div>
                
                <div class="search-filter">
                    <input type="text" id="agency-search" placeholder="搜索机构名称、服务范围..." onkeyup="filterAgencyProviders()">
                    <div class="filter-tags">
                        <span class="filter-tag active" data-service="all" onclick="filterByService('all')">全部服务</span>
                        <span class="filter-tag" data-service="月嫂服务" onclick="filterByService('月嫂服务')">月嫂服务</span>
                        <span class="filter-tag" data-service="育儿嫂服务" onclick="filterByService('育儿嫂服务')">育儿嫂服务</span>
                        <span class="filter-tag" data-service="家务保姆" onclick="filterByService('家务保姆')">家务保姆</span>
                        <span class="filter-tag" data-service="小时工" onclick="filterByService('小时工')">小时工</span>
                        <span class="filter-tag" data-service="深度保洁" onclick="filterByService('深度保洁')">深度保洁</span>
                    </div>
                </div>
                
                <div class="provider-list" id="agency-provider-list">
                    <!-- 服务机构列表由JS动态渲染 -->
                </div>
            </div>
        </div>
        
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDispatchDrawer()">取消</button>
            <button class="btn btn-primary" onclick="confirmDispatch()" id="confirm-dispatch-btn" disabled>确认派单</button>
        </div>
    </div>

    <!-- 阿姨选择弹窗 -->
    <div id="auntie-select-modal" class="modal" style="display: none;">
        <div class="modal-content" style="width: 600px; max-height: 80vh;">
            <div class="modal-header">
                <h3>选择阿姨</h3>
                <button onclick="closeAuntieSelectModal()" style="background: none; border: none; font-size: 20px; cursor: pointer;">&times;</button>
            </div>
            <div class="modal-body">
                <div style="margin-bottom: 20px;">
                    <input type="text" id="auntie-search" placeholder="搜索阿姨姓名..." style="width: 100%; padding: 10px; border: 1px solid var(--border); border-radius: 4px;" onkeyup="filterAunties()">
                </div>
                <div id="auntie-list" style="max-height: 400px; overflow-y: auto;">
                    <!-- 阿姨列表将通过JavaScript动态生成 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeAuntieSelectModal()">取消</button>
                <button class="btn btn-primary" id="confirm-auntie-select" onclick="confirmAuntieSelection()" disabled>确认选择</button>
            </div>
        </div>
    </div>

    <!-- 完成凭证图片查看弹窗 -->
    <div id="proof-image-modal" class="modal" style="display: none;">
        <div class="modal-content" style="width: 80%; max-width: 800px; max-height: 90vh;">
            <div class="modal-header">
                <h3>完成凭证</h3>
                <button onclick="closeProofImageModal()" style="background: none; border: none; font-size: 20px; cursor: pointer;">&times;</button>
            </div>
            <div class="modal-body" style="text-align: center; padding: 20px;">
                <div id="proof-image-container">
                    <!-- 图片将通过JavaScript动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeProofImageModal()">关闭</button>
            </div>
        </div>
    </div>

    <!-- 完成任务表单弹窗 -->
    <div id="complete-task-modal" class="modal" style="display: none;">
        <div class="modal-content" style="width: 600px; max-height: 90vh;">
            <div class="modal-header">
                <h3>完成任务</h3>
                <button onclick="closeCompleteTaskModal()" style="background: none; border: none; font-size: 20px; cursor: pointer;">&times;</button>
            </div>
            <div class="modal-body">
                <form id="complete-task-form">
                    <input type="hidden" id="complete-task-id">
                    <div class="form-group">
                        <label>最终完成人员 <span style="color:red">*</span></label>
                        <select class="form-control" id="complete-auntie-select" required>
                            <option value="">请选择完成人员</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>完成时间 <span style="color:red">*</span></label>
                        <input type="datetime-local" class="form-control" id="complete-time" required>
                    </div>
                    <div class="form-group">
                        <label>打卡地点 <span style="color:red">*</span></label>
                        <input type="text" class="form-control" id="complete-location" placeholder="请输入打卡地点（如：客户地址、具体位置）" required>
                    </div>
                    <div class="form-group">
                        <label>完成凭证 <span style="color:red">*</span></label>
                        <input type="file" class="form-control" id="complete-proof" accept="image/*" required>
                        <small style="color: var(--gray);">支持格式：JPG、PNG、GIF，文件大小不超过5MB</small>
                    </div>
                    <div class="form-group">
                        <label>备注</label>
                        <textarea class="form-control" id="complete-remarks" rows="3" placeholder="请输入备注信息（可选）..." style="resize: vertical;"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeCompleteTaskModal()">取消</button>
                <button class="btn btn-primary" onclick="submitCompleteTask()">确认完成</button>
            </div>
        </div>
    </div>

     <!-- 页面遮罩 -->
     <div class="overlay"></div>

    <script>
        console.log("DEBUG: Script tag started.");
        // --- 全局变量 ---
        let currentPracticeProjectId = null;
        let currentTrainingOrderId = null;
        let currentPersonalOrderId = null;
        let currentDomesticOrderId = null;
        let currentAfterSalesTicketId = null;
        let currentEvaluationOrderId = null;
        let currentFinancialRecordId = null;
        let currentOperationLogType = null;
        let currentOperationLogId = null;
        let currentDispatchOrderId = null;
        let currentDispatchType = 'individual'; // individual 或 agency
        let selectedProviderId = null;

        // 统一的订单状态体系
        const ORDER_STATUS = {
            // 草稿状态
            DRAFT: '草稿',
            
            // 审批阶段
            PENDING_APPROVAL: '待审批',
            APPROVING: '审批中',
            APPROVED: '已批准',
            REJECTED: '已拒绝',
            
            // 支付阶段
            PENDING_PAYMENT: '待支付',
            PAID: '已支付',
            PARTIAL_PAID: '部分支付',
            REFUNDED: '已退款',
            
            // 执行阶段
            PENDING_EXECUTION: '待执行',
            EXECUTING: '执行中',
            COMPLETED: '已完成',
            CANCELLED: '已取消',
            
            // 家政服务特有状态
            PENDING_DISPATCH: '待派单',
            DISPATCHED: '已派单',
            IN_SERVICE: '服务中'
        };

        // 支付状态体系
        const PAYMENT_STATUS = {
            PENDING: '待支付',
            PAID: '已支付',
            PARTIAL_PAID: '部分支付',
            REFUNDED: '已退款',
            CANCELLED: '已取消'
        };

        // 订单状态样式映射
        const statusClassMap = {
            // 草稿状态
            [ORDER_STATUS.DRAFT]: 'status-draft',
            
            // 审批阶段
            [ORDER_STATUS.PENDING_APPROVAL]: 'status-approval-pending',
            [ORDER_STATUS.APPROVING]: 'status-processing',
            [ORDER_STATUS.APPROVED]: 'status-approved',
            [ORDER_STATUS.REJECTED]: 'status-rejected',
            
            // 支付阶段
            [ORDER_STATUS.PENDING_PAYMENT]: 'status-warning',
            [ORDER_STATUS.PAID]: 'status-success',
            [ORDER_STATUS.PARTIAL_PAID]: 'status-warning',
            [ORDER_STATUS.REFUNDED]: 'status-cancelled',
            
            // 执行阶段
            [ORDER_STATUS.PENDING_EXECUTION]: 'status-pending',
            [ORDER_STATUS.EXECUTING]: 'status-processing',
            [ORDER_STATUS.COMPLETED]: 'status-completed',
            [ORDER_STATUS.CANCELLED]: 'status-cancelled',
            
            // 家政服务特有状态
            [ORDER_STATUS.PENDING_DISPATCH]: 'status-pending',
            [ORDER_STATUS.DISPATCHED]: 'status-approved',
            [ORDER_STATUS.IN_SERVICE]: 'status-processing',
            
            // 兼容旧状态
            '待审批': 'status-approval-pending',
            '审批中': 'status-processing',
            '已批准': 'status-approved',
            '已拒绝': 'status-rejected',
            '待开始': 'status-pending',
            '进行中': 'status-processing',
            '已完成': 'status-completed',
            '已关闭': 'status-cancelled',
            '待派单': 'status-pending',
            '待服务': 'status-warning',
            '服务中': 'status-processing',
            '已取消': 'status-cancelled',
            '待处理': 'status-warning',
            '处理中': 'status-processing',
            '已解决': 'status-completed'
        };

        // 支付状态样式映射
        const paymentStatusClassMap = {
            [PAYMENT_STATUS.PENDING]: 'status-pending',
            [PAYMENT_STATUS.PAID]: 'status-success',
            [PAYMENT_STATUS.PARTIAL_PAID]: 'status-warning',
            [PAYMENT_STATUS.REFUNDED]: 'status-cancelled',
            [PAYMENT_STATUS.CANCELLED]: 'status-cancelled'
        };

        // 个人培训状态映射（兼容旧版本）
        const personalStatusClassMap = {
            payment: {
                'pending': 'status-pending',
                'completed': 'status-completed',
                'refunded': 'status-cancelled'
            },
            learning: {
                'pending': 'status-pending',
                'processing': 'status-processing',
                'completed': 'status-completed'
            }
        };

        // --- 导航与视图切换 ---
        function switchView(targetId, business = null) {
            // 如果点击的是旧的实践中心入口，则重定向到新的订单中心
            if (targetId === 'practice-center') {
                targetId = 'order-center';
            }
            
            // 隐藏所有dashboard
            document.querySelectorAll('.dashboard').forEach(dashboard => {
                dashboard.style.display = 'none';
            });
            
            // 显示目标dashboard
            const targetDashboard = document.getElementById(targetId);
            if (targetDashboard) {
                targetDashboard.style.display = 'block';
            }
            
            // 如果是订单中心，则进行初始化
            if (targetId === 'order-center') {
                initOrderCenter();
                // 根据传入的参数切换业务视图，如果无参数则默认显示第一个
                switchBusinessView(business || 'practice');
            }
        }

        function switchBusinessView(business) {
            document.querySelectorAll('.business-content').forEach(c => c.style.display = 'none');
            const targetContent = document.getElementById(business + '-orders');
            if (targetContent) {
                targetContent.style.display = 'block';
            }
        }

        function setupNavigation() {
            // --- 侧边栏主导航 ---
            document.querySelectorAll('.sidebar .nav-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 如果是父菜单，则只负责展开/收起
                    if (this.classList.contains('has-submenu')) {
                        this.classList.toggle('open');
                        const submenu = this.nextElementSibling;
                        if (submenu.style.maxHeight) {
                            submenu.style.maxHeight = null;
                        } else {
                            submenu.style.maxHeight = submenu.scrollHeight + "px";
                        }
                        return;
                    }

                    // --- 处理所有可点击链接的公共逻辑 ---

                    // 1. 更新激活状态
                    document.querySelectorAll('.sidebar .nav-link.active').forEach(active => active.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 如果点击的是子菜单，则同时激活其父菜单
                    const parentSubmenu = this.closest('.submenu');
                    if (parentSubmenu) {
                        parentSubmenu.previousElementSibling.classList.add('active');
                    }

                    // 2. 切换视图
                    const business = this.dataset.business;
                    const view = this.dataset.view;

                    if (business) {
                        // 如果是订单中心的子菜单链接
                        switchView('order-center', business);
                    } else if (view) {
                        // 如果是其他模块的链接
                        switchView(view);
                    } else {
                        // 默认返回工作台
                        switchView('dashboard');
                    }
                });
            });
        }
        
        // --- 订单中心相关功能 ---
        function initOrderCenter() {
            console.log("DEBUG: initOrderCenter called.");
            renderPracticeProjects();
            renderTrainingOrders(); 
            renderPersonalOrders();
            renderDomesticOrders();
            renderOrderStats();
        }

        function setupOrderCenterEventListeners() {
            console.log("DEBUG: setupOrderCenterEventListeners called.");
            // 业务线标签页切换
            const businessTabsContainer = document.querySelector('.business-nav');
            if(businessTabsContainer) {
                businessTabsContainer.addEventListener('click', (e) => {
                    const tab = e.target.closest('.business-tab');
                    if (!tab) return;
                    
                    document.querySelectorAll('.business-tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.business-content').forEach(c => c.style.display = 'none');

                    tab.classList.add('active');
                    const business = tab.dataset.business;
                    const targetContent = document.getElementById(business + '-orders');
                    if (targetContent) {
                        targetContent.style.display = 'block';
                    }
                });
            }

            // 新建个人培训订单按钮
            const newPersonalOrderBtn = document.querySelector('#personal-orders .btn-new');
            if (newPersonalOrderBtn) {
                 newPersonalOrderBtn.addEventListener('click', () => openPersonalDrawer('new'));
            }

            // 个人培训订单抽屉事件
            const personalDrawer = document.getElementById('personal-order-drawer');
            if (personalDrawer) {
                personalDrawer.querySelector('.drawer-header button').addEventListener('click', closePersonalDrawer);
                personalDrawer.querySelector('.drawer-footer .btn-outline').addEventListener('click', closePersonalDrawer);
                personalDrawer.querySelector('.drawer-footer .btn-primary').addEventListener('click', submitPersonalOrder);
            }
            
            // 个人培训详情抽屉事件
            const personalDetailDrawer = document.getElementById('personal-detail-drawer');
            if (personalDetailDrawer) {
                personalDetailDrawer.querySelector('.drawer-header button').addEventListener('click', closePersonalDetailDrawer);
                personalDetailDrawer.querySelector('.drawer-footer .btn-outline').addEventListener('click', closePersonalDetailDrawer);
                personalDetailDrawer.querySelector('.drawer-footer .btn-primary').addEventListener('click', () => {
                     if (currentPersonalOrderId) {
                        closePersonalDetailDrawer();
                        openPersonalDrawer('edit', currentPersonalOrderId);
                    }
                });
            }
        }

        function renderPracticeProjects() {
            console.log("DEBUG: renderPracticeProjects (table view) called.");
            const tbody = document.getElementById('practice-orders-tbody');
            if (!tbody) {
                console.error("DEBUG: practice-orders-tbody container not found!");
                return;
            }
            tbody.innerHTML = '';

            if (practiceOrdersData.length === 0) {
                tbody.innerHTML = `<tr><td colspan="7" style="text-align:center; padding: 20px;">暂无高校实践订单</td></tr>`;
                return;
            }

            practiceOrdersData.forEach(project => {
                const statusClass = statusClassMap[project.status] || 'status-pending';
                const paymentStatusClass = paymentStatusClassMap[project.paymentStatus] || 'status-pending';
                const createTime = project.createTime || '2024-06-15'; // 默认创建时间
                const rowHtml = `
                <tr data-id="${project.id}">
                    <td><span class="order-id">${project.id}</span></td>
                    <td>
                        <div style="font-weight: 500;">${project.projectName}</div>
                        <div style="font-size: 12px; color: var(--gray);">${project.university}</div>
                    </td>
                    <td>${project.enterpriseName}</td>
                    <td>${project.period}</td>
                    <td>${project.manager}</td>
                    <td>¥${Number(project.amount).toLocaleString()}</td>
                    <td><span class="status-badge ${statusClass}">${project.status}</span></td>
                    <td><span class="status-badge ${paymentStatusClass}">${project.paymentStatus}</span></td>
                    <td>${createTime}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-action btn-view" data-id="${project.id}">查看</button>
                            <button class="btn-action btn-edit" data-id="${project.id}">编辑</button>
                            <button class="btn-action btn-delete" data-id="${project.id}">删除</button>
                            <button class="btn-action" style="background: var(--gray); color: white;" onclick="openOperationLog('practice', '${project.id}')">操作日志</button>
                        </div>
                    </td>
                </tr>`;
                tbody.insertAdjacentHTML('beforeend', rowHtml);
            });
            console.log(`DEBUG: Rendered ${practiceOrdersData.length} project rows.`);
        }
        
        function openPracticeDrawer(mode = 'new', projectId = null) {
            const drawer = document.getElementById('practice-order-drawer');
            const title = document.getElementById('practice-drawer-title');
            const form = document.getElementById('practice-order-form');
            
            form.reset();
            currentPracticeProjectId = null;
            const fileNameDisplay = document.getElementById('paper-contract-filename');
            fileNameDisplay.textContent = '';

            if (mode === 'edit' && projectId) {
                title.textContent = '编辑高校实践订单';
                const project = practiceOrdersData.find(p => p.id === projectId);
                if (project) {
                    currentPracticeProjectId = projectId;
                    form.projectName.value = project.projectName;
                    form.university.value = project.university;
                    form.enterpriseName.value = project.enterpriseName;
                    if(project.period) {
                        const [start, end] = project.period.split(' - ');
                        form.startDate.value = start ? start.replace(/\./g, '-') : '';
                        form.endDate.value = end ? end.replace(/\./g, '-') : '';
                    }
                    form.manager.value = project.manager;
                    form.amount.value = project.amount;
                    form.paymentStatus.value = project.paymentStatus || 'pending';
                    form.relatedOpportunity.value = project.relatedOpportunity || '';
                    form.relatedLead.value = project.relatedLead || '';
                    form.contractType.value = project.contractType || 'electronic';
                    
                    // 填充支付信息
                    if (project.paymentInfo) {
                        form.actualAmount.value = project.paymentInfo.actualAmount || '';
                        form.paymentMethod.value = project.paymentInfo.paymentMethod || '';
                        form.paymentDate.value = project.paymentInfo.paymentDate || '';
                        form.operator.value = project.paymentInfo.operator || '';
                        form.remarks.value = project.paymentInfo.remarks || '';
                    }
                    
                    // 触发支付状态变化事件，显示/隐藏支付信息区域
                    togglePaymentInfo('practice');

                    if (project.paperContractFile) {
                        fileNameDisplay.textContent = `当前文件: ${project.paperContractFile}`;
                    }
                }
            } else {
                title.textContent = '新建高校实践订单';
            }

            drawer.classList.add('open');
            document.querySelector('.overlay').classList.add('active');
            
            // 设置关联商机和线索选择事件
            setupOpportunitySelection();
            setupLeadSelection();
        }

        function closePracticeDrawer() {
            document.getElementById('practice-order-drawer').classList.remove('open');
            if (!document.querySelector('.drawer.open')) {
                document.querySelector('.overlay').classList.remove('active');
            }
            
            // 清除商机提示信息
            const infoDiv = document.getElementById('opportunity-info');
            if (infoDiv) {
                infoDiv.remove();
            }
        }

        function submitPracticeOrder() {
            const form = document.getElementById('practice-order-form');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const projectDataFromForm = {
                projectName: form.projectName.value,
                university: form.university.value,
                enterpriseName: form.enterpriseName.value,
                period: `${form.startDate.value.replace(/-/g, '.')} - ${form.endDate.value.replace(/-/g, '.')}`,
                manager: form.manager.value,
                amount: form.amount.value,
                paymentStatus: form.paymentStatus.value,
                relatedOpportunity: form.relatedOpportunity.value, // 添加关联商机字段
                relatedLead: form.relatedLead.value, // 添加关联线索字段
                contractType: form.contractType.value, // 添加合同类型字段
                // 支付信息
                paymentInfo: form.paymentStatus.value === 'completed' ? {
                    actualAmount: form.actualAmount.value,
                    paymentMethod: form.paymentMethod.value,
                    paymentDate: form.paymentDate.value,
                    operator: form.operator.value,
                    remarks: form.remarks.value
                } : null
            };

            const fileInput = form.paperContract;
            if (fileInput.files.length > 0) {
                projectDataFromForm.paperContractFile = fileInput.files[0].name;
            }
            
            if (currentPracticeProjectId) {
                // 编辑模式 - 记录变更
                const index = practiceOrdersData.findIndex(p => p.id === currentPracticeProjectId);
                if (index !== -1) {
                    const oldData = practiceOrdersData[index];
                    if (!projectDataFromForm.paperContractFile) {
                        projectDataFromForm.paperContractFile = oldData.paperContractFile;
                    }
                    
                    // 比较变更
                    const fieldMap = {
                        projectName: '项目名称',
                        university: '合作高校',
                        enterpriseName: '合作企业',
                        period: '项目周期',
                        manager: '项目负责人',
                        amount: '订单金额',
                        relatedOpportunity: '关联商机',
                        relatedLead: '关联线索'
                    };
                    
                    const changes = compareObjects(oldData, projectDataFromForm, fieldMap);
                    
                    practiceOrdersData[index] = { ...oldData, ...projectDataFromForm };
                    
                    // 记录操作日志
                    if (changes.length > 0) {
                        addOperationLog('practice', currentPracticeProjectId, 'edit', changes, '编辑订单信息');
                    }
                }
            } else {
                // 新建模式
                projectDataFromForm.id = 'HP' + new Date().getTime();
                projectDataFromForm.paperContractFile = projectDataFromForm.paperContractFile || null;
                projectDataFromForm.status = ORDER_STATUS.PENDING_APPROVAL;
                projectDataFromForm.paymentStatus = PAYMENT_STATUS.PENDING;
                projectDataFromForm.createTime = new Date().toISOString().split('T')[0];
                projectDataFromForm.approvalHistory = [];
                projectDataFromForm.contractInfo = {
                    contractNumber: null,
                    contractName: null,
                    signDate: null,
                    contractAmount: null,
                    contractType: projectDataFromForm.contractType,
                    contractStatus: 'unsigned'
                };
                projectDataFromForm.signatures = {
                    platform: 'unsigned',
                    university: 'unsigned',
                    enterprise: 'unsigned'
                };
                projectDataFromForm.operationLogs = [];
                practiceOrdersData.unshift(projectDataFromForm);
                
                // 记录创建日志
                addOperationLog('practice', projectDataFromForm.id, 'create', [], '创建高校实践订单');
            }
            
            renderPracticeProjects();
            closePracticeDrawer();
        }

        // 设置关联商机选择功能
        function setupOpportunitySelection() {
            const opportunitySelect = document.querySelector('#practice-order-form select[name="relatedOpportunity"]');
            if (!opportunitySelect) return;

            opportunitySelect.addEventListener('change', function() {
                const selectedOpportunityId = this.value;
                if (!selectedOpportunityId) {
                    // 如果选择空值，清空相关字段
                    clearOpportunityFields();
                    return;
                }

                const opportunity = opportunityData.find(opp => opp.id === selectedOpportunityId);
                if (opportunity) {
                    fillOpportunityFields(opportunity);
                }
            });
        }

        // 设置关联线索选择功能
        function setupLeadSelection() {
            const leadSelect = document.querySelector('#practice-order-form select[name="relatedLead"]');
            if (!leadSelect) return;

            leadSelect.addEventListener('change', function() {
                const selectedLeadId = this.value;
                if (!selectedLeadId) {
                    // 如果选择空值，清空相关字段
                    clearLeadFields();
                    return;
                }

                const lead = leadData.find(l => l.id === selectedLeadId);
                if (lead) {
                    fillLeadFields(lead);
                }
            });
        }

        // 填充商机信息到表单
        function fillOpportunityFields(opportunity) {
            const form = document.getElementById('practice-order-form');
            if (!form) return;

            // 自动填充相关字段
            form.projectName.value = opportunity.projectName || '';
            form.university.value = opportunity.university || '';
            form.enterpriseName.value = opportunity.enterpriseName || '';
            form.manager.value = opportunity.manager || '';
            form.amount.value = opportunity.estimatedAmount || '';

            // 显示提示信息
            showOpportunityInfo(opportunity);
        }

        // 清空商机相关字段
        function clearOpportunityFields() {
            const form = document.getElementById('practice-order-form');
            if (!form) return;

            // 清空相关字段（保留用户已输入的内容）
            // 这里可以根据需要决定是否清空字段
            
            // 清除提示信息
            const infoDiv = document.getElementById('opportunity-info');
            if (infoDiv) {
                infoDiv.remove();
            }
        }

        // 显示商机信息提示
        function showOpportunityInfo(opportunity) {
            // 创建或更新提示信息
            let infoDiv = document.getElementById('opportunity-info');
            if (!infoDiv) {
                infoDiv = document.createElement('div');
                infoDiv.id = 'opportunity-info';
                infoDiv.style.cssText = 'background: #e3f2fd; border: 1px solid #2196f3; border-radius: 4px; padding: 10px; margin: 10px 0; font-size: 13px;';
                
                const form = document.getElementById('practice-order-form');
                const opportunitySelect = form.querySelector('select[name="relatedOpportunity"]');
                opportunitySelect.parentNode.insertBefore(infoDiv, opportunitySelect.nextSibling);
            }

            infoDiv.innerHTML = `
                <div style="font-weight: 500; color: #1976d2; margin-bottom: 5px;">
                    <i class="fas fa-info-circle"></i> 商机信息已自动填充
                </div>
                <div style="color: #333; line-height: 1.4;">
                    <strong>商机描述：</strong>${opportunity.description}<br>
                    <strong>预估金额：</strong>¥${opportunity.estimatedAmount?.toLocaleString() || '未设置'}<br>
                    <strong>创建时间：</strong>${opportunity.createTime}<br>
                    <strong>商机状态：</strong><span style="color: #2196f3;">${opportunity.status}</span>
                </div>
            `;
        }

        // 填充线索信息到表单
        function fillLeadFields(lead) {
            const form = document.getElementById('practice-order-form');
            if (!form) return;

            // 根据线索类型自动填充相关字段
            if (lead.type === '高校实践') {
                // 高校实践线索 - 可以填充项目负责人等信息
                form.manager.value = lead.name || '';
            }

            // 显示提示信息
            showLeadInfo(lead);
        }

        // 清空线索相关字段
        function clearLeadFields() {
            const form = document.getElementById('practice-order-form');
            if (!form) return;

            // 清除提示信息
            const infoDiv = document.getElementById('lead-info');
            if (infoDiv) {
                infoDiv.remove();
            }
        }

        // 显示线索信息提示
        function showLeadInfo(lead) {
            // 创建或更新提示信息
            let infoDiv = document.getElementById('lead-info');
            if (!infoDiv) {
                infoDiv = document.createElement('div');
                infoDiv.id = 'lead-info';
                infoDiv.style.cssText = 'background: #f3e5f5; border: 1px solid #9c27b0; border-radius: 4px; padding: 10px; margin: 10px 0; font-size: 13px;';
                
                const form = document.getElementById('practice-order-form');
                const leadSelect = form.querySelector('select[name="relatedLead"]');
                leadSelect.parentNode.insertBefore(infoDiv, leadSelect.nextSibling);
            }

            infoDiv.innerHTML = `
                <div style="font-weight: 500; color: #7b1fa2; margin-bottom: 5px;">
                    <i class="fas fa-user-circle"></i> 线索信息已自动填充
                </div>
                <div style="color: #333; line-height: 1.4;">
                    <strong>联系人：</strong>${lead.name}<br>
                    <strong>联系电话：</strong>${lead.phone}<br>
                    <strong>线索来源：</strong>${lead.source}<br>
                    <strong>线索描述：</strong>${lead.description}<br>
                    <strong>线索状态：</strong><span style="color: #9c27b0;">${lead.status}</span>
                </div>
            `;
        }

        // 企业培训订单 - 设置关联商机选择功能
        function setupTrainingOpportunitySelection() {
            const opportunitySelect = document.querySelector('#training-order-form select[name="relatedOpportunity"]');
            if (!opportunitySelect) return;

            opportunitySelect.addEventListener('change', function() {
                const selectedOpportunityId = this.value;
                if (!selectedOpportunityId) {
                    clearTrainingOpportunityFields();
                    return;
                }

                const opportunity = opportunityData.find(opp => opp.id === selectedOpportunityId);
                if (opportunity) {
                    fillTrainingOpportunityFields(opportunity);
                }
            });
        }

        // 企业培训订单 - 设置关联线索选择功能
        function setupTrainingLeadSelection() {
            const leadSelect = document.querySelector('#training-order-form select[name="relatedLead"]');
            if (!leadSelect) return;

            leadSelect.addEventListener('change', function() {
                const selectedLeadId = this.value;
                if (!selectedLeadId) {
                    clearTrainingLeadFields();
                    return;
                }

                const lead = leadData.find(l => l.id === selectedLeadId);
                if (lead) {
                    fillTrainingLeadFields(lead);
                }
            });
        }

        // 企业培训订单 - 填充商机信息到表单
        function fillTrainingOpportunityFields(opportunity) {
            const form = document.getElementById('training-order-form');
            if (!form) return;

            // 自动填充相关字段
            form.enterpriseName.value = opportunity.enterpriseName || '';
            form.trainingProjectName.value = opportunity.trainingProjectName || '';
            form.manager.value = opportunity.manager || '';
            form.amount.value = opportunity.estimatedAmount || '';

            // 显示提示信息
            showTrainingOpportunityInfo(opportunity);
        }

        // 企业培训订单 - 清空商机相关字段
        function clearTrainingOpportunityFields() {
            const form = document.getElementById('training-order-form');
            if (!form) return;

            // 清除提示信息
            const infoDiv = document.getElementById('training-opportunity-info');
            if (infoDiv) {
                infoDiv.remove();
            }
        }

        // 企业培训订单 - 显示商机信息提示
        function showTrainingOpportunityInfo(opportunity) {
            let infoDiv = document.getElementById('training-opportunity-info');
            if (!infoDiv) {
                infoDiv = document.createElement('div');
                infoDiv.id = 'training-opportunity-info';
                infoDiv.style.cssText = 'background: #e3f2fd; border: 1px solid #2196f3; border-radius: 4px; padding: 10px; margin: 10px 0; font-size: 13px;';
                
                const form = document.getElementById('training-order-form');
                const opportunitySelect = form.querySelector('select[name="relatedOpportunity"]');
                opportunitySelect.parentNode.insertBefore(infoDiv, opportunitySelect.nextSibling);
            }

            infoDiv.innerHTML = `
                <div style="font-weight: 500; color: #1976d2; margin-bottom: 5px;">
                    <i class="fas fa-info-circle"></i> 商机信息已自动填充
                </div>
                <div style="color: #333; line-height: 1.4;">
                    <strong>商机描述：</strong>${opportunity.description}<br>
                    <strong>预估金额：</strong>¥${opportunity.estimatedAmount?.toLocaleString() || '未设置'}<br>
                    <strong>创建时间：</strong>${opportunity.createTime}<br>
                    <strong>商机状态：</strong><span style="color: #2196f3;">${opportunity.status}</span>
                </div>
            `;
        }

        // 企业培训订单 - 填充线索信息到表单
        function fillTrainingLeadFields(lead) {
            const form = document.getElementById('training-order-form');
            if (!form) return;

            // 根据线索类型自动填充相关字段
            if (lead.type === '企业培训') {
                form.manager.value = lead.name || '';
            }

            // 显示提示信息
            showTrainingLeadInfo(lead);
        }

        // 企业培训订单 - 清空线索相关字段
        function clearTrainingLeadFields() {
            const form = document.getElementById('training-order-form');
            if (!form) return;

            // 清除提示信息
            const infoDiv = document.getElementById('training-lead-info');
            if (infoDiv) {
                infoDiv.remove();
            }
        }

        // 企业培训订单 - 显示线索信息提示
        function showTrainingLeadInfo(lead) {
            let infoDiv = document.getElementById('training-lead-info');
            if (!infoDiv) {
                infoDiv = document.createElement('div');
                infoDiv.id = 'training-lead-info';
                infoDiv.style.cssText = 'background: #f3e5f5; border: 1px solid #9c27b0; border-radius: 4px; padding: 10px; margin: 10px 0; font-size: 13px;';
                
                const form = document.getElementById('training-order-form');
                const leadSelect = form.querySelector('select[name="relatedLead"]');
                leadSelect.parentNode.insertBefore(infoDiv, leadSelect.nextSibling);
            }

            infoDiv.innerHTML = `
                <div style="font-weight: 500; color: #7b1fa2; margin-bottom: 5px;">
                    <i class="fas fa-user-circle"></i> 线索信息已自动填充
                </div>
                <div style="color: #333; line-height: 1.4;">
                    <strong>联系人：</strong>${lead.name}<br>
                    <strong>联系电话：</strong>${lead.phone}<br>
                    <strong>线索来源：</strong>${lead.source}<br>
                    <strong>线索描述：</strong>${lead.description}<br>
                    <strong>线索状态：</strong><span style="color: #9c27b0;">${lead.status}</span>
                </div>
            `;
        }

        function openPracticeDetailDrawer(projectId) {
            currentPracticeProjectId = projectId;
            const project = practiceOrdersData.find(p => p.id === projectId);
            if (!project) {
                return;
            }
            
            const detailBody = document.getElementById('practice-detail-body');

            const signatureStatusMap = {
                unsigned: { text: '未发起', badge: 'status-unsigned' },
                pending: { text: '待签署', badge: 'status-pending' },
                signed: { text: '已签署', badge: 'status-completed' }
            };

            const getSignatureStatusHtml = (party, status) => {
                const statusInfo = signatureStatusMap[status] || signatureStatusMap['unsigned'];
                const partyInfo = {
                    platform: { name: '平台方', icon: 'fa-server' },
                    university: { name: '高校方', icon: 'fa-university' },
                    enterprise: { name: '企业方', icon: 'fa-building' }
                };
                return `
                    <li class="signature-item">
                        <div class="signature-party">
                            <i class="fas ${partyInfo[party].icon}"></i> ${partyInfo[party].name}
                        </div>
                        <div class="signature-status">
                            <span class="status-badge ${statusInfo.badge}">${statusInfo.text}</span>
                        </div>
                    </li>
                `;
            };

            // 合同信息显示
            let contractHtml = '';
            // 为订单数据添加默认合同类型
            const contractType = project.contractType || 'paper';
            
            if (contractType === 'electronic') {
                contractHtml = `
                    <div class="meta-item">
                        <div class="meta-label">合同类型</div>
                        <div class="meta-value">电子合同</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">合同模板</div>
                        <div class="meta-value">${project.contractTemplate || '家政服务协议模板'}</div>
                        </div>
                    <div class="meta-item">
                        <div class="meta-label">合同状态</div>
                        <div class="meta-value">
                            <span class="status-badge status-completed">已签署</span>
                    </div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">签署时间</div>
                        <div class="meta-value">${project.contractSignDate || '2024-11-14'}</div>
                </div>
                `;
            } else {
                // 默认显示纸质合同信息
                contractHtml = `
                    <div class="meta-item">
                        <div class="meta-label">合同类型</div>
                        <div class="meta-value">纸质合同</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">合同编号</div>
                        <div class="meta-value">${project.contractNumber || 'HT-202406001'}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">合同名称</div>
                        <div class="meta-value">${project.contractName || '家政服务合同'}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">合同周期</div>
                        <div class="meta-value">${project.contractStartDate || '2024-6-1'} - ${project.contractEndDate || '2024-6-30'}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">签署日期</div>
                        <div class="meta-value">${project.contractSignDate || '2024-6-1'}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">合同金额</div>
                        <div class="meta-value">¥${project.contractAmount ? Number(project.contractAmount).toLocaleString() : Number(project.amount).toLocaleString()}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">合同附件</div>
                        <div class="meta-value">
                            ${project.paperContractFile ? 
                                `<div class="attachment-item">
                                    <i class="fas fa-file-contract"></i>
                                    <span class="attachment-name">${project.paperContractFile}</span>
                                    <button class="btn btn-outline" style="font-size:12px; padding: 5px 10px;">下载</button>
                                </div>` : 
                                `<div class="attachment-item">
                                    <i class="fas fa-file-contract"></i>
                                    <span class="attachment-name">家政服务合同.pdf</span>
                                    <button class="btn btn-outline" style="font-size:12px; padding: 5px 10px;">下载</button>
                                </div>`
                            }
                        </div>
                    </div>
                `;
            }

            const signaturesHtml = project.signatures ? `
                <div class="section-title">三方协议签署状态</div>
                <ul class="signature-list">
                    ${getSignatureStatusHtml('platform', project.signatures.platform)}
                    ${getSignatureStatusHtml('university', project.signatures.university)}
                    ${getSignatureStatusHtml('enterprise', project.signatures.enterprise)}
                </ul>
            ` : '';

            const paperContractHtml = project.paperContractFile ? `
                <div class="attachment-item">
                    <i class="fas fa-file-contract"></i>
                    <span class="attachment-name">${project.paperContractFile}</span>
                    <button class="btn btn-outline" style="font-size:12px; padding: 5px 10px;" onclick="alert('下载功能开发中...')">下载</button>
                </div>
            ` : `<div style="padding: 15px 0; color: var(--gray);">暂未上传纸质合同</div>`;

            const approvalHistoryHtml = project.approvalHistory && project.approvalHistory.length > 0
                ? project.approvalHistory.map(item => `
                    <li class="timeline-item">
                        <div class="timeline-time">${item.time}</div>
                        <div class="timeline-content">
                            <strong>${item.user}</strong> ${item.action === '批准' ? '批准了订单' : '驳回了订单'}。
                            ${item.comments ? `<div style="margin-top:5px; font-style: italic;">备注: ${item.comments}</div>` : ''}
                        </div>
                    </li>
                `).join('')
                : '<li>无审批记录</li>';
            
            // 审批和收款操作按钮
            let approvalActionsHtml = '';
            if (project.status === ORDER_STATUS.PENDING_APPROVAL) {
                approvalActionsHtml = `
                    <div style="text-align: right; margin-top: 15px;">
                        <button class="btn btn-primary" style="font-size:12px; padding: 5px 10px;" onclick="initiateApproval('practice', '${project.id}')">
                            <i class="fas fa-gavel"></i> 发起审批
                        </button>
                    </div>
                `;
            } else if (project.status === ORDER_STATUS.APPROVING) {
                approvalActionsHtml = `
                    <div style="text-align: right; margin-top: 15px;">
                        <button class="btn btn-success" style="margin-right: 10px; background:var(--success); color:white; font-size:12px; padding: 5px 10px;" onclick="approveOrder('practice', '${project.id}')">
                            <i class="fas fa-check"></i> 同意
                        </button>
                        <button class="btn btn-danger" style="background:var(--danger); color:white; font-size:12px; padding: 5px 10px;" onclick="rejectOrder('practice', '${project.id}')">
                            <i class="fas fa-times"></i> 拒绝
                        </button>
                    </div>
                `;
            } else if (project.status === ORDER_STATUS.PENDING_PAYMENT) {
                approvalActionsHtml = `
                    <div style="text-align: right; margin-top: 15px;">
                        <button class="btn btn-success" style="background:var(--success); color:white; font-size:12px; padding: 5px 10px;" onclick="openPaymentModal('practice', '${project.id}')">
                            <i class="fas fa-money-bill-wave"></i> 确认收款
                        </button>
                    </div>
                `;
            }

            // 收款信息显示
            const paymentInfoHtml = project.paymentInfo ? `
                <div class="section">
                    <div class="section-title">
                        <i class="fas fa-money-bill-wave" style="color: #27ae60; margin-right: 8px;"></i>
                        收款信息
                    </div>
                    <div class="project-card payment-info-card" style="box-shadow:none; border: 1px solid #27ae60;">
                        <div class="project-meta">
                            <div class="meta-item"><div class="meta-label">收款金额</div><div class="meta-value" style="color: #27ae60; font-weight: bold;">¥${Number(project.paymentInfo.actualAmount).toLocaleString()}</div></div>
                            <div class="meta-item"><div class="meta-label">收款方式</div><div class="meta-value">${getPaymentMethodText(project.paymentInfo.paymentMethod)}</div></div>
                            <div class="meta-item"><div class="meta-label">收款日期</div><div class="meta-value">${project.paymentInfo.paymentDate}</div></div>
                            <div class="meta-item"><div class="meta-label">操作人</div><div class="meta-value">${project.paymentInfo.operator}</div></div>
                            ${project.paymentInfo.remarks ? `<div class="meta-item" style="grid-column: 1 / -1;"><div class="meta-label">收款备注</div><div class="meta-value">${project.paymentInfo.remarks}</div></div>` : ''}
                        </div>
                    </div>
                </div>
            ` : '';

            // 获取关联商机信息
            const relatedOpportunity = project.relatedOpportunity ? opportunityData.find(opp => opp.id === project.relatedOpportunity) : null;
            const opportunityInfoHtml = relatedOpportunity ? `
                <div class="meta-item" style="grid-column: 1 / -1;">
                    <div class="meta-label">关联商机</div>
                    <div class="meta-value">
                        <span style="color: var(--primary); font-weight: 500;">${relatedOpportunity.id}</span> - ${relatedOpportunity.title}
                        <br><small style="color: var(--gray);">${relatedOpportunity.description}</small>
                    </div>
                </div>
            ` : '';

            detailBody.innerHTML = `
                <div class="project-card" style="box-shadow:none; border: 1px solid var(--border);">
                    <div class="project-header">
                        <div class="project-title">${project.projectName}</div>
                        <div class="project-status status-badge ${statusClassMap[project.status] || 'status-pending'}">${project.status}</div>
                    </div>
                    <div class="project-meta">
                        <div class="meta-item"><div class="meta-label">合作高校</div><div class="meta-value">${project.university}</div></div>
                        <div class="meta-item"><div class="meta-label">合作企业</div><div class="meta-value">${project.enterpriseName}</div></div>
                        <div class="meta-item"><div class="meta-label">项目周期</div><div class="meta-value">${project.period}</div></div>
                        <div class="meta-item"><div class="meta-label">订单金额</div><div class="meta-value">¥${Number(project.amount).toLocaleString()}</div></div>
                        <div class="meta-item"><div class="meta-label">项目负责人</div><div class="meta-value">${project.manager}</div></div>
                        ${opportunityInfoHtml}
                    </div>
                </div>
                <div class="section">
                    <div class="section-title">
                        <i class="fas fa-file-contract" style="color: #3498db; margin-right: 8px;"></i>
                        合同信息
                    </div>
                    <div class="project-card" style="box-shadow:none; border: 1px solid var(--border);">
                        <div class="project-meta">
                            ${contractHtml}
                        </div>
                    </div>
                    <div style="text-align: right; margin-top: 15px;">
                        ${project.contractType === 'electronic' ? `
                            <button class="btn btn-outline" style="margin-right: 10px; font-size:12px; padding: 5px 10px;" onclick="alert('查看合同功能开发中...')">
                                <i class="fas fa-eye"></i> 查看合同
                            </button>
                            <button class="btn btn-outline" style="font-size:12px; padding: 5px 10px;" onclick="alert('下载合同功能开发中...')">
                                <i class="fas fa-download"></i> 下载合同
                            </button>
                        ` : `
                            <button class="btn btn-outline" style="margin-right: 10px; font-size:12px; padding: 5px 10px;" onclick="openContractModal('practice', '${project.id}', 'electronic')">
                                <i class="fas fa-file-signature"></i> 发起电子合同
                            </button>
                            <button class="btn btn-outline" style="font-size:12px; padding: 5px 10px;" onclick="openContractModal('practice', '${project.id}', 'paper')">
                                <i class="fas fa-upload"></i> 上传纸质合同
                            </button>
                        `}
                    </div>
                </div>
                <div class="section">
                    ${signaturesHtml}
                </div>
                <div class="section">
                    <div class="section-title">纸质合同附件</div>
                    ${paperContractHtml}
                </div>
                ${paymentInfoHtml}
                <div class="section">
                    <div class="section-title">审批流程</div>
                    <ul class="timeline">
                        ${approvalHistoryHtml}
                    </ul>
                    ${approvalActionsHtml}
                </div>
            `;
            
            document.getElementById('practice-detail-drawer').classList.add('open');
            document.querySelector('.overlay').classList.add('active');
        }

        function closePracticeDetailDrawer() {
            document.getElementById('practice-detail-drawer').classList.remove('open');
            if (!document.querySelector('.drawer.open')) {
                document.querySelector('.overlay').classList.remove('active');
            }
        }

        function editFromDetailView() {
            if (currentPracticeProjectId) {
                closePracticeDetailDrawer();
                openPracticeDrawer('edit', currentPracticeProjectId);
            }
        }

        function deletePracticeOrder(projectId) {
            if (confirm('您确定要删除此项目订单吗？此操作不可恢复。')) {
                const project = practiceOrdersData.find(p => p.id === projectId);
                if (project) {
                    // 记录删除日志（在实际系统中，删除前应该先记录日志）
                    addOperationLog('practice', projectId, 'delete', [], '删除高校实践订单');
                    
                const index = practiceOrdersData.findIndex(p => p.id === projectId);
                if (index !== -1) {
                    practiceOrdersData.splice(index, 1);
                    renderPracticeProjects();
                    console.log(`DEBUG: Deleted project ${projectId} and re-rendered.`);
                    }
                }
            }
        }
        
        function showNewOrderModal() {
            alert('新建订单功能开发中...\n\n可选择的订单类型：\n- 高校实践订单\n- 企业培训订单\n- 个人培训与认证订单\n- 家政服务订单');
        }

        // 审批相关函数
        function initiateApproval(orderType, orderId) {
            // 设置审批弹窗信息
            document.getElementById('approval-order-id').value = orderId;
            document.getElementById('approval-order-type').value = orderType;
            document.getElementById('approval-modal-title').textContent = '发起审批';
            
            // 隐藏审批结果选择，只显示审批意见
            document.querySelector('.form-group:first-child').style.display = 'none';
            document.getElementById('reject-reason-group').style.display = 'none';
            
            // 重置表单
            document.getElementById('approval-comments').value = '';
            document.getElementById('approval-comments').placeholder = '请输入发起审批的说明（可选）...';
            
            // 更新按钮文本和事件
            document.getElementById('approval-submit-btn').textContent = '发起审批';
            document.getElementById('approval-submit-btn').onclick = submitInitiateApproval;
            
            // 显示弹窗
            document.getElementById('approval-modal').style.display = 'flex';
        }

        function submitInitiateApproval() {
            const orderId = document.getElementById('approval-order-id').value;
            const orderType = document.getElementById('approval-order-type').value;
            const comments = document.getElementById('approval-comments').value;

            let orderData, orderIndex;
            
            // 根据订单类型获取数据
            switch (orderType) {
                case 'practice':
                    orderData = practiceOrdersData;
                    orderIndex = practiceOrdersData.findIndex(p => p.id === orderId);
                    break;
                case 'training':
                    orderData = trainingOrdersData;
                    orderIndex = trainingOrdersData.findIndex(o => o.id === orderId);
                    break;
                case 'personal':
                    orderData = personalOrdersData;
                    orderIndex = personalOrdersData.findIndex(o => o.id === orderId);
                    break;
                case 'domestic':
                    orderData = domesticOrdersData;
                    orderIndex = domesticOrdersData.findIndex(o => o.id === orderId);
                    break;
                default:
                    alert('未知的订单类型');
                    return;
            }

            if (orderIndex === -1) {
                alert('订单不存在');
                return;
            }

            const order = orderData[orderIndex];
            const currentTime = new Date().toLocaleString('zh-CN');
            const currentUser = '当前用户';

            // 更新订单状态为审批中
            order.status = ORDER_STATUS.APPROVING;
            order.approvalHistory.push({
                action: '发起审批',
                user: currentUser,
                time: currentTime,
                comments: comments
            });
            
            // 记录操作日志
            addOperationLog(orderType, orderId, 'initiate_approval', [
                { field: '订单状态', oldValue: ORDER_STATUS.PENDING_APPROVAL, newValue: ORDER_STATUS.APPROVING }
            ], '发起审批流程');
            
            // 重新渲染对应的订单列表
            switch (orderType) {
                case 'practice':
                    renderPracticeProjects();
                    break;
                case 'training':
                    renderTrainingOrders();
                    break;
                case 'personal':
                    renderPersonalOrders();
                    break;
                case 'domestic':
                    renderDomesticOrders();
                    break;
            }

            alert('审批流程已发起！');
            closeApprovalModal();
        }

        function approveOrder(orderType, orderId) {
            // 直接同意审批
            submitApprovalResult(orderType, orderId, 'approve', '', '');
        }

        function rejectOrder(orderType, orderId) {
            // 设置审批弹窗信息
            document.getElementById('approval-order-id').value = orderId;
            document.getElementById('approval-order-type').value = orderType;
            document.getElementById('approval-modal-title').textContent = '拒绝审批';
            
            // 显示审批结果选择
            document.querySelector('.form-group:first-child').style.display = 'block';
            document.getElementById('reject-reason-group').style.display = 'block';
            
            // 设置默认选择拒绝
            document.getElementById('approval-reject').checked = true;
            document.getElementById('reject-reason').required = true;
            
            // 重置表单
            document.getElementById('approval-comments').value = '';
            document.getElementById('approval-comments').placeholder = '请输入审批意见（可选）...';
            
            // 更新按钮文本和事件
            document.getElementById('approval-submit-btn').textContent = '提交审批';
            document.getElementById('approval-submit-btn').onclick = submitApproval;
            
            // 显示弹窗
            document.getElementById('approval-modal').style.display = 'flex';
        }

        function closeApprovalModal() {
            document.getElementById('approval-modal').style.display = 'none';
            
            // 重置弹窗状态
            document.querySelector('.form-group:first-child').style.display = 'block';
            document.getElementById('reject-reason-group').style.display = 'none';
            document.getElementById('approval-comments').placeholder = '请输入审批意见（可选）...';
            document.getElementById('approval-submit-btn').textContent = '提交审批';
            document.getElementById('approval-submit-btn').onclick = submitApproval;
        }

        function submitApproval() {
            const orderId = document.getElementById('approval-order-id').value;
            const orderType = document.getElementById('approval-order-type').value;
            const approvalResult = document.querySelector('input[name="approvalResult"]:checked').value;
            const rejectReason = document.getElementById('reject-reason').value;
            const comments = document.getElementById('approval-comments').value;

            // 验证拒绝原因
            if (approvalResult === 'reject' && !rejectReason.trim()) {
                alert('请输入拒绝原因');
                document.getElementById('reject-reason').focus();
                return;
            }

            // 提交审批结果
            submitApprovalResult(orderType, orderId, approvalResult, rejectReason, comments);
            
            // 关闭弹窗
            closeApprovalModal();
        }

        function submitApprovalResult(orderType, orderId, result, rejectReason, comments) {
            let orderData, orderIndex;
            
            // 根据订单类型获取数据
            switch (orderType) {
                case 'practice':
                    orderData = practiceOrdersData;
                    orderIndex = practiceOrdersData.findIndex(p => p.id === orderId);
                    break;
                case 'training':
                    orderData = trainingOrdersData;
                    orderIndex = trainingOrdersData.findIndex(o => o.id === orderId);
                    break;
                case 'personal':
                    orderData = personalOrdersData;
                    orderIndex = personalOrdersData.findIndex(o => o.id === orderId);
                    break;
                case 'domestic':
                    orderData = domesticOrdersData;
                    orderIndex = domesticOrdersData.findIndex(o => o.id === orderId);
                    break;
                default:
                    alert('未知的订单类型');
                    return;
            }

            if (orderIndex === -1) {
                alert('订单不存在');
                return;
            }

            const order = orderData[orderIndex];
            const currentTime = new Date().toLocaleString('zh-CN');
            const currentUser = '当前用户'; // 实际系统中应该从登录信息获取

            // 更新订单状态
            if (result === 'approve') {
                // 家政服务审批通过后状态变为待派单，其他订单类型变为待收款
                const newStatus = orderType === 'domestic' ? ORDER_STATUS.PENDING_DISPATCH : ORDER_STATUS.PENDING_PAYMENT;
                order.status = newStatus;
                order.approvalHistory.push({
                    action: '批准',
                    user: currentUser,
                    time: currentTime,
                    comments: comments
                });
                
                // 记录操作日志
                addOperationLog(orderType, orderId, 'approve', [
                    { field: '订单状态', oldValue: ORDER_STATUS.APPROVING, newValue: newStatus }
                ], '审批通过');
                
                const statusText = orderType === 'domestic' ? '待派单' : '待收款';
                alert(`审批通过成功！订单状态已更新为${statusText}。`);
            } else {
                order.status = ORDER_STATUS.REJECTED;
                order.approvalHistory.push({
                    action: '拒绝',
                    user: currentUser,
                    time: currentTime,
                    comments: rejectReason
                });
                
                // 记录操作日志
                addOperationLog(orderType, orderId, 'reject', [
                    { field: '订单状态', oldValue: ORDER_STATUS.APPROVING, newValue: ORDER_STATUS.REJECTED }
                ], `审批拒绝：${rejectReason}`);
                
                alert('审批拒绝成功！');
            }

            // 重新渲染对应的订单列表
            switch (orderType) {
                case 'practice':
                    renderPracticeProjects();
                    break;
                case 'training':
                    renderTrainingOrders();
                    break;
                case 'personal':
                    renderPersonalOrders();
                    break;
                case 'domestic':
                    renderDomesticOrders();
                    break;
            }

            // 如果当前正在查看该订单详情，刷新详情页面
            if (currentPracticeProjectId === orderId || currentTrainingOrderId === orderId || currentPersonalOrderId === orderId || currentDomesticOrderId === orderId) {
                // 这里可以添加刷新详情页面的逻辑
                // 暂时关闭详情抽屉，用户需要重新点击查看
                closePracticeDetailDrawer();
                closeTrainingDetailDrawer();
                closePersonalDetailDrawer();
                closeDomesticDetailDrawer();
            }
        }

        // 监听审批结果选择变化
        document.addEventListener('DOMContentLoaded', function() {
            const approvalRadios = document.querySelectorAll('input[name="approvalResult"]');
            const rejectReasonGroup = document.getElementById('reject-reason-group');
            
            approvalRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'reject') {
                        rejectReasonGroup.style.display = 'block';
                        document.getElementById('reject-reason').required = true;
                    } else {
                        rejectReasonGroup.style.display = 'none';
                        document.getElementById('reject-reason').required = false;
                    }
                });
            });

            // 点击遮罩层关闭审批弹窗
            const approvalModal = document.getElementById('approval-modal');
            if (approvalModal) {
                approvalModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeApprovalModal();
                    }
                });
            }

            // 点击遮罩层关闭收款弹窗
            const paymentModal = document.getElementById('payment-modal');
            if (paymentModal) {
                paymentModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closePaymentModal();
                    }
                });
            }

            // 设置收款日期默认值为今天
            const paymentDateInput = document.getElementById('payment-date');
            if (paymentDateInput) {
                paymentDateInput.value = new Date().toISOString().split('T')[0];
            }
        });

        // 收款相关函数
        function openPaymentModal(orderType, orderId) {
            let orderData, order;
            
            // 根据订单类型获取数据
            switch (orderType) {
                case 'practice':
                    orderData = practiceOrdersData;
                    order = practiceOrdersData.find(p => p.id === orderId);
                    break;
                case 'training':
                    orderData = trainingOrdersData;
                    order = trainingOrdersData.find(o => o.id === orderId);
                    break;
                case 'personal':
                    orderData = personalOrdersData;
                    order = personalOrdersData.find(o => o.id === orderId);
                    break;
                case 'domestic':
                    orderData = domesticOrdersData;
                    order = domesticOrdersData.find(o => o.id === orderId);
                    break;
                default:
                    alert('未知的订单类型');
                    return;
            }

            if (!order) {
                alert('订单不存在');
                return;
            }

            // 设置弹窗信息
            document.getElementById('payment-order-id').value = orderId;
            document.getElementById('payment-order-type').value = orderType;
            document.getElementById('payment-order-amount').textContent = `¥${Number(order.amount).toLocaleString()}`;
            
            // 重置表单
            document.getElementById('payment-form').reset();
            document.getElementById('payment-actual-amount').value = order.amount;
            document.getElementById('payment-date').value = new Date().toISOString().split('T')[0];
            
            // 显示弹窗
            document.getElementById('payment-modal').style.display = 'flex';
        }

        function closePaymentModal() {
            document.getElementById('payment-modal').style.display = 'none';
        }

        // 合同管理相关函数
        function openContractModal(orderType, orderId, contractType) {
            document.getElementById('contract-order-id').value = orderId;
            document.getElementById('contract-order-type').value = orderType;
            document.getElementById('contract-type').value = contractType;
            
            // 根据合同类型显示不同的表单
            if (contractType === 'electronic') {
                document.getElementById('contract-modal-title').textContent = '发起电子合同';
                document.getElementById('electronic-contract-form').style.display = 'block';
                document.getElementById('paper-contract-form').style.display = 'none';
                
                // 填充签署方信息
                let orderData;
                switch (orderType) {
                    case 'practice':
                        orderData = practiceOrdersData.find(p => p.id === orderId);
                        break;
                    case 'training':
                        orderData = trainingOrdersData.find(o => o.id === orderId);
                        break;
                    default:
                        orderData = null;
                }
                
                if (orderData) {
                    document.getElementById('university-party').textContent = orderData.university || orderData.enterpriseName || '待填写';
                    document.getElementById('enterprise-party').textContent = orderData.enterpriseName || '待填写';
                }
            } else {
                document.getElementById('contract-modal-title').textContent = '上传纸质合同';
                document.getElementById('electronic-contract-form').style.display = 'none';
                document.getElementById('paper-contract-form').style.display = 'block';
            }
            
            document.getElementById('contract-modal').style.display = 'flex';
        }

        function closeContractModal() {
            document.getElementById('contract-modal').style.display = 'none';
            document.getElementById('contract-form').reset();
        }

        function submitContract() {
            const orderId = document.getElementById('contract-order-id').value;
            const orderType = document.getElementById('contract-order-type').value;
            const contractType = document.getElementById('contract-type').value;
            
            if (contractType === 'electronic') {
                // 电子合同提交逻辑
                const template = document.getElementById('contract-template').value;
                const contractName = document.getElementById('contract-name').value;
                
                if (!template || !contractName) {
                    alert('请填写完整的合同信息');
                    return;
                }
                
                // 更新订单的合同信息
                let orderData, orderIndex;
                switch (orderType) {
                    case 'practice':
                        orderData = practiceOrdersData;
                        orderIndex = practiceOrdersData.findIndex(p => p.id === orderId);
                        break;
                    case 'training':
                        orderData = trainingOrdersData;
                        orderIndex = trainingOrdersData.findIndex(o => o.id === orderId);
                        break;
                    default:
                        alert('未知的订单类型');
                        return;
                }
                
                if (orderIndex !== -1) {
                    const order = orderData[orderIndex];
                    order.contractInfo = {
                        contractNumber: 'HT' + new Date().getTime(),
                        contractName: contractName,
                        signDate: new Date().toISOString().split('T')[0],
                        contractAmount: order.amount,
                        contractType: 'electronic',
                        contractStatus: 'pending'
                    };
                    
                    // 记录操作日志
                    addOperationLog(orderType, orderId, 'contract_initiate', [
                        { field: '合同类型', oldValue: '未设置', newValue: '电子合同' },
                        { field: '合同状态', oldValue: '未发起', newValue: '签署中' }
                    ], '发起电子合同');
                    
                    // 重新渲染
                    switch (orderType) {
                        case 'practice':
                            renderPracticeProjects();
                            break;
                        case 'training':
                            renderTrainingOrders();
                            break;
                    }
                }
                
            } else {
                // 纸质合同提交逻辑
                const contractFile = document.getElementById('contract-file').files[0];
                const contractNumber = document.getElementById('contract-number').value;
                const contractName = document.getElementById('paper-contract-name').value;
                const signDate = document.getElementById('contract-sign-date').value;
                const contractAmount = document.getElementById('contract-amount').value;
                
                if (!contractFile || !contractNumber || !contractName || !signDate || !contractAmount) {
                    alert('请填写完整的合同信息');
                    return;
                }
                
                // 更新订单的合同信息
                let orderData, orderIndex;
                switch (orderType) {
                    case 'practice':
                        orderData = practiceOrdersData;
                        orderIndex = practiceOrdersData.findIndex(p => p.id === orderId);
                        break;
                    case 'training':
                        orderData = trainingOrdersData;
                        orderIndex = trainingOrdersData.findIndex(o => o.id === orderId);
                        break;
                    default:
                        alert('未知的订单类型');
                        return;
                }
                
                if (orderIndex !== -1) {
                    const order = orderData[orderIndex];
                    order.contractInfo = {
                        contractNumber: contractNumber,
                        contractName: contractName,
                        signDate: signDate,
                        contractAmount: parseFloat(contractAmount),
                        contractType: 'paper',
                        contractStatus: 'archived'
                    };
                    order.paperContractFile = contractFile.name;
                    
                    // 记录操作日志
                    addOperationLog(orderType, orderId, 'contract_upload', [
                        { field: '合同类型', oldValue: '未设置', newValue: '纸质合同' },
                        { field: '合同状态', oldValue: '未发起', newValue: '已归档' }
                    ], '上传纸质合同');
                    
                    // 重新渲染
                    switch (orderType) {
                        case 'practice':
                            renderPracticeProjects();
                            break;
                        case 'training':
                            renderTrainingOrders();
                            break;
                    }
                }
            }
            
            alert('合同信息已保存！');
            closeContractModal();
        }

        function confirmPayment() {
            const form = document.getElementById('payment-form');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const orderId = document.getElementById('payment-order-id').value;
            const orderType = document.getElementById('payment-order-type').value;
            const actualAmount = parseFloat(document.getElementById('payment-actual-amount').value);
            const paymentMethod = document.getElementById('payment-method').value;
            const paymentDate = document.getElementById('payment-date').value;
            const remarks = document.getElementById('payment-remarks').value;

            let orderData, orderIndex;
            
            // 根据订单类型获取数据
            switch (orderType) {
                case 'practice':
                    orderData = practiceOrdersData;
                    orderIndex = practiceOrdersData.findIndex(p => p.id === orderId);
                    break;
                case 'training':
                    orderData = trainingOrdersData;
                    orderIndex = trainingOrdersData.findIndex(o => o.id === orderId);
                    break;
                case 'personal':
                    orderData = personalOrdersData;
                    orderIndex = personalOrdersData.findIndex(o => o.id === orderId);
                    break;
                case 'domestic':
                    orderData = domesticOrdersData;
                    orderIndex = domesticOrdersData.findIndex(o => o.id === orderId);
                    break;
                default:
                    alert('未知的订单类型');
                    return;
            }

            if (orderIndex === -1) {
                alert('订单不存在');
                return;
            }

            const order = orderData[orderIndex];
            const currentTime = new Date().toLocaleString('zh-CN');
            const currentUser = '当前用户';

            // 更新订单状态和收款信息
            order.status = ORDER_STATUS.PAID;
            order.paymentInfo = {
                actualAmount: actualAmount,
                paymentMethod: paymentMethod,
                paymentDate: paymentDate,
                remarks: remarks,
                operator: currentUser,
                operateTime: currentTime
            };

            // 记录操作日志
            addOperationLog(orderType, orderId, 'payment_confirm', [
                { field: '订单状态', oldValue: ORDER_STATUS.PENDING_PAYMENT, newValue: ORDER_STATUS.PAID },
                { field: '收款金额', oldValue: '-', newValue: `¥${actualAmount.toLocaleString()}` },
                { field: '收款方式', oldValue: '-', newValue: getPaymentMethodText(paymentMethod) }
            ], `确认收款：¥${actualAmount.toLocaleString()}`);

            // 重新渲染对应的订单列表
            switch (orderType) {
                case 'practice':
                    renderPracticeProjects();
                    break;
                case 'training':
                    renderTrainingOrders();
                    break;
                case 'personal':
                    renderPersonalOrders();
                    break;
                case 'domestic':
                    renderDomesticOrders();
                    break;
            }

            alert('收款确认成功！');
            closePaymentModal();

            // 如果当前正在查看该订单详情，刷新详情页面
            if (currentPracticeProjectId === orderId || currentTrainingOrderId === orderId || 
                currentPersonalOrderId === orderId || currentDomesticOrderId === orderId) {
                // 重新打开详情页面以刷新内容
                setTimeout(() => {
                    switch (orderType) {
                        case 'practice':
                            openPracticeDetailDrawer(orderId);
                            break;
                        case 'training':
                            openTrainingDetailDrawer(orderId);
                            break;
                        case 'personal':
                            openPersonalDetailDrawer(orderId);
                            break;
                        case 'domestic':
                            openDomesticDetailDrawer(orderId);
                            break;
                    }
                }, 100);
            }
        }

        function getPaymentMethodText(method) {
            const methodMap = {
                'bank_transfer': '银行转账',
                'alipay': '支付宝',
                'wechat': '微信支付',
                'cash': '现金',
                'check': '支票',
                'other': '其他'
            };
            return methodMap[method] || method;
        }

        // --- 数据模拟 ---
        // 商机数据
        const opportunityData = [
            {
                id: 'OPP202406001',
                title: 'XX大学暑期实践项目商机',
                university: 'XX大学经济管理学院',
                enterpriseName: 'ABC科技有限公司',
                projectName: '2024年暑期社会实践项目',
                estimatedAmount: 580000,
                manager: '张三',
                description: '为XX大学经济管理学院学生提供暑期社会实践机会，涉及数字化转型、数据分析等领域。',
                status: '跟进中',
                createTime: '2024-06-01'
            },
            {
                id: 'OPP202406002',
                title: 'YY大学春季实习商机',
                university: 'YY大学商学院',
                enterpriseName: 'XYZ制造集团',
                projectName: '春季实习实践项目',
                estimatedAmount: 420000,
                manager: '李四',
                description: '为YY大学商学院学生提供春季实习机会，涉及供应链管理、生产运营等领域。',
                status: '跟进中',
                createTime: '2024-05-15'
            },
            {
                id: 'OPP202406003',
                title: 'ZZ科技大学AI产业认知项目商机',
                university: 'ZZ科技大学',
                enterpriseName: '创新科技集团',
                projectName: 'AI+数字人产业认知项目',
                estimatedAmount: 350000,
                manager: '王五',
                description: '为ZZ科技大学学生提供AI产业认知实践机会，涉及人工智能、数字人技术等领域。',
                status: '跟进中',
                createTime: '2024-06-10'
            },
            {
                id: 'OPP202406004',
                title: 'AA外国语大学新媒体运营项目商机',
                university: 'AA外国语大学',
                enterpriseName: '星火传媒',
                projectName: '新媒体运营短学期实践',
                estimatedAmount: 120000,
                manager: '赵六',
                description: '为AA外国语大学学生提供新媒体运营实践机会，涉及内容创作、社交媒体运营等领域。',
                status: '跟进中',
                createTime: '2024-06-05'
            },
            // 企业培训商机
            {
                id: 'OPP202406005',
                title: 'XX企业员工培训项目商机',
                enterpriseName: 'XX企业集团',
                trainingProjectName: '员工技能提升培训',
                estimatedAmount: 150000,
                manager: '陈经理',
                description: '为XX企业集团提供员工技能提升培训服务，包括管理技能、专业技能等。',
                status: '跟进中',
                createTime: '2024-06-08'
            },
            {
                id: 'OPP202406006',
                title: 'YY公司技能提升培训商机',
                enterpriseName: 'YY科技有限公司',
                trainingProjectName: '技术技能提升培训',
                estimatedAmount: 200000,
                manager: '刘总监',
                description: '为YY科技有限公司提供技术技能提升培训，包括编程、数据分析等。',
                status: '跟进中',
                createTime: '2024-06-12'
            },
            {
                id: 'OPP202406007',
                title: 'ZZ集团管理培训商机',
                enterpriseName: 'ZZ集团',
                trainingProjectName: '中高层管理培训',
                estimatedAmount: 300000,
                manager: '张总',
                description: '为ZZ集团提供中高层管理培训，包括领导力、战略管理等。',
                status: '跟进中',
                createTime: '2024-06-15'
            },
            {
                id: 'OPP202406008',
                title: 'AA科技公司技术培训商机',
                enterpriseName: 'AA科技公司',
                trainingProjectName: '新技术应用培训',
                estimatedAmount: 180000,
                manager: '王经理',
                description: '为AA科技公司提供新技术应用培训，包括AI、云计算等。',
                status: '跟进中',
                createTime: '2024-06-18'
            },
            // 个人培训商机
            {
                id: 'OPP202406009',
                title: '个人培训课程推广商机',
                courseName: '项目管理PMP认证课程',
                estimatedAmount: 5000,
                manager: '孙女士',
                description: '个人培训课程推广，包括项目管理、数据分析等认证课程。',
                status: '跟进中',
                createTime: '2024-06-20'
            },
            {
                id: 'OPP202406010',
                title: '技能认证考试推广商机',
                courseName: '高级母婴护理师认证',
                estimatedAmount: 3000,
                manager: '周先生',
                description: '技能认证考试推广，包括母婴护理、营养师等认证。',
                status: '跟进中',
                createTime: '2024-06-22'
            },
            {
                id: 'OPP202406011',
                title: '在线课程学习商机',
                courseName: 'Python编程基础课程',
                estimatedAmount: 2000,
                manager: '吴女士',
                description: '在线课程学习推广，包括编程、设计等在线课程。',
                status: '跟进中',
                createTime: '2024-06-25'
            },
            {
                id: 'OPP202406012',
                title: '职业发展培训商机',
                courseName: '心理咨询师三级认证',
                estimatedAmount: 4000,
                manager: '郑先生',
                description: '职业发展培训推广，包括心理咨询、职业规划等。',
                status: '跟进中',
                createTime: '2024-06-28'
            },
            // 家政服务商机
            {
                id: 'OPP202406013',
                title: '家政服务推广商机',
                serviceType: '月嫂服务',
                estimatedAmount: 8000,
                manager: '林女士',
                description: '家政服务推广，包括月嫂、育儿嫂、保洁等服务。',
                status: '跟进中',
                createTime: '2024-06-30'
            },
            {
                id: 'OPP202406014',
                title: '月嫂服务需求商机',
                serviceType: '月嫂服务',
                estimatedAmount: 12000,
                manager: '黄先生',
                description: '月嫂服务需求，为新生儿家庭提供专业月嫂服务。',
                status: '跟进中',
                createTime: '2024-07-01'
            },
            {
                id: 'OPP202406015',
                title: '保洁服务需求商机',
                serviceType: '深度保洁',
                estimatedAmount: 500,
                manager: '马女士',
                description: '保洁服务需求，为家庭和企业提供专业保洁服务。',
                status: '跟进中',
                createTime: '2024-07-02'
            },
            {
                id: 'OPP202406016',
                title: '育儿嫂服务商机',
                serviceType: '育儿嫂服务',
                estimatedAmount: 6000,
                manager: '朱先生',
                description: '育儿嫂服务需求，为有婴幼儿的家庭提供专业育儿嫂服务。',
                status: '跟进中',
                createTime: '2024-07-03'
            }
        ];

        // 线索数据
        const leadData = [
            // 高校实践线索
            {
                id: 'LEAD202406001',
                name: '张三',
                phone: '138****1234',
                source: '官网注册',
                type: '高校实践',
                description: '对暑期社会实践项目感兴趣',
                status: '已转化',
                createTime: '2024-06-01'
            },
            {
                id: 'LEAD202406002',
                name: '李四',
                phone: '139****5678',
                source: '电话咨询',
                type: '高校实践',
                description: '咨询春季实习项目',
                status: '已转化',
                createTime: '2024-06-02'
            },
            {
                id: 'LEAD202406003',
                name: '王五',
                phone: '137****9012',
                source: '微信咨询',
                type: '高校实践',
                description: '对AI产业认知项目感兴趣',
                status: '跟进中',
                createTime: '2024-06-03'
            },
            {
                id: 'LEAD202406004',
                name: '赵六',
                phone: '136****3456',
                source: '线下活动',
                type: '高校实践',
                description: '参加校园招聘会了解项目',
                status: '已转化',
                createTime: '2024-06-04'
            },
            // 企业培训线索
            {
                id: 'LEAD202406005',
                name: '陈经理',
                phone: '135****7890',
                source: '企业咨询',
                type: '企业培训',
                description: '咨询员工培训服务',
                status: '已转化',
                createTime: '2024-06-05'
            },
            {
                id: 'LEAD202406006',
                name: '刘总监',
                phone: '134****1234',
                source: '培训需求',
                type: '企业培训',
                description: '需要技术技能提升培训',
                status: '跟进中',
                createTime: '2024-06-06'
            },
            {
                id: 'LEAD202406007',
                name: '张总',
                phone: '133****5678',
                source: '合作洽谈',
                type: '企业培训',
                description: '洽谈管理培训合作',
                status: '已转化',
                createTime: '2024-06-07'
            },
            {
                id: 'LEAD202406008',
                name: '王经理',
                phone: '132****9012',
                source: '技能培训',
                type: '企业培训',
                description: '咨询新技术应用培训',
                status: '跟进中',
                createTime: '2024-06-08'
            },
            // 个人培训线索
            {
                id: 'LEAD202406009',
                name: '孙女士',
                phone: '131****3456',
                source: '个人培训咨询',
                type: '个人培训',
                description: '咨询项目管理认证课程',
                status: '已转化',
                createTime: '2024-06-09'
            },
            {
                id: 'LEAD202406010',
                name: '周先生',
                phone: '130****7890',
                source: '认证考试咨询',
                type: '个人培训',
                description: '咨询母婴护理师认证',
                status: '跟进中',
                createTime: '2024-06-10'
            },
            {
                id: 'LEAD202406011',
                name: '吴女士',
                phone: '129****1234',
                source: '在线学习咨询',
                type: '个人培训',
                description: '咨询Python编程课程',
                status: '已转化',
                createTime: '2024-06-11'
            },
            {
                id: 'LEAD202406012',
                name: '郑先生',
                phone: '128****5678',
                source: '职业发展咨询',
                type: '个人培训',
                description: '咨询心理咨询师认证',
                status: '跟进中',
                createTime: '2024-06-12'
            },
            // 家政服务线索
            {
                id: 'LEAD202406013',
                name: '林女士',
                phone: '127****9012',
                source: '家政服务咨询',
                type: '家政服务',
                description: '咨询月嫂服务',
                status: '已转化',
                createTime: '2024-06-13'
            },
            {
                id: 'LEAD202406014',
                name: '黄先生',
                phone: '126****3456',
                source: '月嫂服务咨询',
                type: '家政服务',
                description: '需要专业月嫂服务',
                status: '跟进中',
                createTime: '2024-06-14'
            },
            {
                id: 'LEAD202406015',
                name: '马女士',
                phone: '125****7890',
                source: '保洁服务咨询',
                type: '家政服务',
                description: '咨询深度保洁服务',
                status: '已转化',
                createTime: '2024-06-15'
            },
            {
                id: 'LEAD202406016',
                name: '朱先生',
                phone: '124****1234',
                source: '育儿嫂咨询',
                type: '家政服务',
                description: '需要育儿嫂服务',
                status: '跟进中',
                createTime: '2024-06-16'
            }
        ];

        const practiceOrdersData = [
            {
                id: 'HP202406001',
                projectName: '2024年暑期社会实践项目',
                university: 'XX大学经济管理学院',
                enterpriseName: 'ABC科技有限公司',
                period: '2024.07.01 - 2024.08.30',
                amount: 580000,
                manager: '张三',
                status: ORDER_STATUS.EXECUTING,
                paymentStatus: PAYMENT_STATUS.PAID,
                createTime: '2024-06-15',
                paperContractFile: '合同_XX大学暑期实践项目.pdf',
                relatedOpportunity: 'OPP202406001',
                relatedLead: 'LEAD202406001',
                contractInfo: {
                    contractNumber: 'HT202406001',
                    contractName: 'XX大学暑期社会实践项目合作协议',
                    signDate: '2024-06-20',
                    contractAmount: 580000,
                    contractType: 'electronic', // electronic 或 paper
                    contractStatus: 'signed' // unsigned, pending, signed, archived
                },
                approvalHistory: [
                    { action: '批准', user: '张三 (管理员)', time: '2024/6/20 10:05:12', comments: '已确认合作意向，批准立项。'}
                ],
                paymentInfo: {
                    actualAmount: 580000,
                    paymentMethod: 'bank_transfer',
                    paymentDate: '2024-06-20',
                    operator: '张三 (管理员)',
                    remarks: '银行转账'
                },
                signatures: {
                    platform: 'signed',
                    university: 'signed',
                    enterprise: 'pending'
                },
                operationLogs: [
                    {
                        id: 'LOG001',
                        action: 'create',
                        operator: '张三 (管理员)',
                        time: '2024/6/15 14:30:00',
                        changes: [],
                        comment: '创建高校实践订单'
                    },
                    {
                        id: 'LOG002',
                        action: 'edit',
                        operator: '张三 (管理员)',
                        time: '2024/6/16 09:15:00',
                        changes: [
                            { field: '项目名称', oldValue: '暑期社会实践项目', newValue: '2024年暑期社会实践项目' },
                            { field: '订单金额', oldValue: '550000', newValue: '580000' }
                        ],
                        comment: '更新项目名称和金额'
                    },
                    {
                        id: 'LOG003',
                        action: 'approve',
                        operator: '张三 (管理员)',
                        time: '2024/6/20 10:05:12',
                        changes: [
                            { field: '订单状态', oldValue: ORDER_STATUS.PENDING_APPROVAL, newValue: ORDER_STATUS.PENDING_PAYMENT }
                        ],
                        comment: '已确认合作意向，批准立项'
                    },
                    {
                        id: 'LOG004',
                        action: 'payment_confirm',
                        operator: '张三 (管理员)',
                        time: '2024/6/20 15:30:00',
                        changes: [
                            { field: '支付状态', oldValue: PAYMENT_STATUS.PENDING, newValue: PAYMENT_STATUS.PAID },
                            { field: '订单状态', oldValue: ORDER_STATUS.PENDING_PAYMENT, newValue: ORDER_STATUS.EXECUTING }
                        ],
                        comment: '确认收款完成'
                    }
                ]
            },
            {
                id: 'HP202403001',
                projectName: '春季实习实践项目',
                university: 'YY大学商学院',
                enterpriseName: 'XYZ制造集团',
                period: '2024.03.01 - 2024.05.30',
                amount: 420000,
                manager: '李四',
                status: ORDER_STATUS.COMPLETED,
                paymentStatus: PAYMENT_STATUS.PAID,
                createTime: '2024-03-01',
                paperContractFile: '合同_YY大学春季实习.pdf',
                relatedOpportunity: 'OPP202406002',
                relatedLead: 'LEAD202406002',
                contractInfo: {
                    contractNumber: 'HT202403001',
                    contractName: 'YY大学春季实习项目合作协议',
                    signDate: '2024-03-01',
                    contractAmount: 420000,
                    contractType: 'paper',
                    contractStatus: 'archived'
                },
                approvalHistory: [
                    { action: '批准', user: '张三 (管理员)', time: '2024/3/1 09:15:30', comments: '审批通过'}
                ],
                paymentInfo: {
                    actualAmount: 420000,
                    paymentMethod: 'bank_transfer',
                    paymentDate: '2024-03-01',
                    operator: '李四 (管理员)',
                    remarks: '银行转账'
                },
                signatures: {
                    platform: 'signed',
                    university: 'signed',
                    enterprise: 'signed'
                },
                operationLogs: [
                    {
                        id: 'LOG005',
                        action: 'create',
                        operator: '李四 (管理员)',
                        time: '2024/3/1 09:15:30',
                        changes: [],
                        comment: '创建春季实习项目订单'
                    },
                    {
                        id: 'LOG006',
                        action: 'approve',
                        operator: '张三 (管理员)',
                        time: '2024/3/1 09:15:30',
                        changes: [
                            { field: '订单状态', oldValue: ORDER_STATUS.PENDING_APPROVAL, newValue: ORDER_STATUS.PENDING_PAYMENT }
                        ],
                        comment: '审批通过'
                    },
                    {
                        id: 'LOG007',
                        action: 'payment_confirm',
                        operator: '李四 (管理员)',
                        time: '2024/3/1 14:20:00',
                        changes: [
                            { field: '支付状态', oldValue: PAYMENT_STATUS.PENDING, newValue: PAYMENT_STATUS.PAID },
                            { field: '订单状态', oldValue: ORDER_STATUS.PENDING_PAYMENT, newValue: ORDER_STATUS.EXECUTING }
                        ],
                        comment: '确认收款完成'
                    },
                    {
                        id: 'LOG008',
                        action: 'complete',
                        operator: '系统自动',
                        time: '2024/5/30 18:00:00',
                        changes: [
                            { field: '订单状态', oldValue: ORDER_STATUS.EXECUTING, newValue: ORDER_STATUS.COMPLETED }
                        ],
                        comment: '项目执行完成'
                    }
                ]
            },
            {
                id: 'HP202406002',
                projectName: 'AI+数字人产业认知项目',
                university: 'ZZ科技大学',
                enterpriseName: '创新科技集团',
                period: '2024.09.01 - 2024.09.30',
                amount: 350000,
                manager: '王五',
                status: ORDER_STATUS.PENDING_APPROVAL,
                paymentStatus: PAYMENT_STATUS.PENDING,
                createTime: '2024-06-18',
                paperContractFile: null,
                relatedOpportunity: 'OPP202406003',
                relatedLead: 'LEAD202406003',
                contractInfo: {
                    contractNumber: null,
                    contractName: null,
                    signDate: null,
                    contractAmount: null,
                    contractType: null,
                    contractStatus: 'unsigned'
                },
                approvalHistory: [],
                paymentInfo: null,
                signatures: {
                    platform: 'unsigned',
                    university: 'unsigned',
                    enterprise: 'unsigned'
                },
                operationLogs: [
                    {
                        id: 'LOG009',
                        action: 'create',
                        operator: '王五 (管理员)',
                        time: '2024/6/18 16:20:00',
                        changes: [],
                        comment: '创建AI产业认知项目订单'
                    }
                ]
            },
            {
                id: 'HP202406003',
                projectName: '新媒体运营短学期实践',
                university: 'AA外国语大学',
                enterpriseName: '星火传媒',
                period: '2024.07.15 - 2024.07.30',
                amount: 120000,
                manager: '赵六',
                status: ORDER_STATUS.REJECTED,
                paymentStatus: PAYMENT_STATUS.CANCELLED,
                createTime: '2024-06-19',
                paperContractFile: null,
                relatedOpportunity: 'OPP202406004',
                relatedLead: 'LEAD202406004',
                contractInfo: {
                    contractNumber: null,
                    contractName: null,
                    signDate: null,
                    contractAmount: null,
                    contractType: null,
                    contractStatus: 'unsigned'
                },
                approvalHistory: [
                    { action: '驳回', user: '张三 (管理员)', time: '2024/6/19 14:20:00', comments: '合作企业资质不符合要求，请重新审核。'}
                ],
                paymentInfo: null,
                signatures: {
                    platform: 'unsigned',
                    university: 'unsigned',
                    enterprise: 'unsigned'
                },
                operationLogs: [
                    {
                        id: 'LOG010',
                        action: 'create',
                        operator: '赵六 (管理员)',
                        time: '2024/6/19 10:30:00',
                        changes: [],
                        comment: '创建新媒体运营项目订单'
                    },
                    {
                        id: 'LOG011',
                        action: 'reject',
                        operator: '张三 (管理员)',
                        time: '2024/6/19 14:20:00',
                        changes: [
                            { field: '订单状态', oldValue: ORDER_STATUS.PENDING_APPROVAL, newValue: ORDER_STATUS.REJECTED },
                            { field: '支付状态', oldValue: PAYMENT_STATUS.PENDING, newValue: PAYMENT_STATUS.CANCELLED }
                        ],
                        comment: '合作企业资质不符合要求，请重新审核'
                    }
                ]
            }
        ];

        let trainingOrdersData = [
            { 
                id: 'ET202406001', 
                enterpriseName: 'ABC科技有限公司', 
                trainingProjectName: '数字化转型管理培训', 
                participantsCount: 25, 
                amount: 125000, 
                status: ORDER_STATUS.EXECUTING,
                paymentStatus: PAYMENT_STATUS.PAID,
                createTime: '2024-06-15', 
                period: '2024.07.01 - 2024.07.15', 
                manager: '李四', 
                paperContractFile: 'ABC科技培训合同.pdf',
                relatedOpportunity: 'OPP202406005',
                relatedLead: 'LEAD202406005',
                approvalHistory: [{ action: '批准', user: '张三 (管理员)', time: '2024/6/14 11:00:00', comments: '审批通过' }],
                paymentInfo: {
                    actualAmount: 125000,
                    paymentMethod: 'bank_transfer',
                    paymentDate: '2024-06-14',
                    remarks: '银行转账'
                },
                signatures: { platform: 'signed', enterprise: 'pending' },
                operationLogs: [
                    {
                        id: 'LOG012',
                        action: 'create',
                        operator: '李四 (管理员)',
                        time: '2024/6/14 10:30:00',
                        changes: [],
                        comment: '创建企业培训订单'
                    },
                    {
                        id: 'LOG013',
                        action: 'approve',
                        operator: '张三 (管理员)',
                        time: '2024/6/14 11:00:00',
                        changes: [
                            { field: '订单状态', oldValue: ORDER_STATUS.PENDING_APPROVAL, newValue: ORDER_STATUS.PENDING_PAYMENT }
                        ],
                        comment: '审批通过'
                    },
                    {
                        id: 'LOG014',
                        action: 'payment_confirm',
                        operator: '李四 (管理员)',
                        time: '2024/6/14 16:45:00',
                        changes: [
                            { field: '支付状态', oldValue: PAYMENT_STATUS.PENDING, newValue: PAYMENT_STATUS.PAID },
                            { field: '订单状态', oldValue: ORDER_STATUS.PENDING_PAYMENT, newValue: ORDER_STATUS.EXECUTING }
                        ],
                        comment: '确认收款完成'
                    }
                ]
            },
            { 
                id: 'ET202406002', 
                enterpriseName: 'XYZ制造集团', 
                trainingProjectName: '精益生产管理培训', 
                participantsCount: 40, 
                amount: 180000, 
                status: ORDER_STATUS.PENDING_EXECUTION,
                paymentStatus: PAYMENT_STATUS.PAID,
                createTime: '2024-06-18', 
                period: '2024.07.20 - 2024.08.10', 
                manager: '李四', 
                paperContractFile: null,
                relatedOpportunity: 'OPP202406006',
                relatedLead: 'LEAD202406006',
                approvalHistory: [{ action: '批准', user: '张三 (管理员)', time: '2024/6/17 16:30:00', comments: '审批通过' }],
                paymentInfo: {
                    actualAmount: 180000,
                    paymentMethod: 'bank_transfer',
                    paymentDate: '2024-06-17',
                    remarks: '银行转账'
                },
                signatures: { platform: 'signed', enterprise: 'signed' },
                operationLogs: [
                    {
                        id: 'LOG015',
                        action: 'create',
                        operator: '李四 (管理员)',
                        time: '2024/6/17 15:45:00',
                        changes: [],
                        comment: '创建精益生产管理培训订单'
                    },
                    {
                        id: 'LOG016',
                        action: 'approve',
                        operator: '张三 (管理员)',
                        time: '2024/6/17 16:30:00',
                        changes: [
                            { field: '订单状态', oldValue: ORDER_STATUS.PENDING_APPROVAL, newValue: ORDER_STATUS.PENDING_PAYMENT }
                        ],
                        comment: '审批通过'
                    },
                    {
                        id: 'LOG017',
                        action: 'payment_confirm',
                        operator: '李四 (管理员)',
                        time: '2024/6/17 18:20:00',
                        changes: [
                            { field: '支付状态', oldValue: PAYMENT_STATUS.PENDING, newValue: PAYMENT_STATUS.PAID },
                            { field: '订单状态', oldValue: ORDER_STATUS.PENDING_PAYMENT, newValue: ORDER_STATUS.PENDING_EXECUTION }
                        ],
                        comment: '确认收款完成'
                    }
                ]
            },
            { 
                id: 'ET202406003', 
                enterpriseName: '创新科技集团', 
                trainingProjectName: '团队协作与沟通技巧培训', 
                participantsCount: 60, 
                amount: 240000, 
                status: ORDER_STATUS.PENDING_APPROVAL,
                paymentStatus: PAYMENT_STATUS.PENDING,
                createTime: '2024-06-20', 
                period: '2024.08.01 - 2024.08.05', 
                manager: '王五', 
                paperContractFile: null,
                relatedOpportunity: 'OPP202406007',
                relatedLead: 'LEAD202406007',
                approvalHistory: [],
                paymentInfo: null,
                signatures: { platform: 'unsigned', enterprise: 'unsigned' },
                operationLogs: [
                    {
                        id: 'LOG018',
                        action: 'create',
                        operator: '王五 (管理员)',
                        time: '2024/6/20 14:20:00',
                        changes: [],
                        comment: '创建团队协作培训订单'
                    }
                ]
            },
            { 
                id: 'ET202406004', 
                enterpriseName: '星火传媒', 
                trainingProjectName: '新媒体运营进阶', 
                participantsCount: 20, 
                amount: 80000, 
                status: ORDER_STATUS.REJECTED,
                paymentStatus: PAYMENT_STATUS.CANCELLED,
                createTime: '2024-06-21', 
                period: '2024.07.10 - 2024.07.12', 
                manager: '王五', 
                paperContractFile: '星火传媒合同草案.docx',
                relatedOpportunity: 'OPP202406008',
                relatedLead: 'LEAD202406008',
                approvalHistory: [{ action: '驳回', user: '张三 (管理员)', time: '2024/6/22 10:00:00', comments: '预算超标，请重新提报。' }],
                paymentInfo: null,
                signatures: { platform: 'unsigned', enterprise: 'unsigned' },
                operationLogs: [
                    {
                        id: 'LOG019',
                        action: 'create',
                        operator: '王五 (管理员)',
                        time: '2024/6/21 11:30:00',
                        changes: [],
                        comment: '创建新媒体运营培训订单'
                    },
                    {
                        id: 'LOG020',
                        action: 'reject',
                        operator: '张三 (管理员)',
                        time: '2024/6/22 10:00:00',
                        changes: [
                            { field: '订单状态', oldValue: ORDER_STATUS.PENDING_APPROVAL, newValue: ORDER_STATUS.REJECTED },
                            { field: '支付状态', oldValue: PAYMENT_STATUS.PENDING, newValue: PAYMENT_STATUS.CANCELLED }
                        ],
                        comment: '预算超标，请重新提报'
                    }
                ]
            }
        ];

        let personalOrdersData = [
            { 
                id: 'PT202406001', 
                studentName: '王小明', 
                courseName: '项目管理PMP认证课程', 
                orderType: '个人培训', 
                amount: 4500, 
                status: ORDER_STATUS.EXECUTING,
                paymentStatus: PAYMENT_STATUS.PAID,
                learningStatus: 'processing', 
                learningStatusText: '学习中', 
                registerTime: '2024-06-10',
                relatedOpportunity: 'OPP202406009',
                relatedLead: 'LEAD202406009',
                paymentInfo: {
                    actualAmount: 4500,
                    paymentMethod: 'alipay',
                    paymentDate: '2024-06-10',
                    remarks: '支付宝支付'
                },
                operationLogs: [
                    {
                        id: 'LOG021',
                        action: 'create',
                        operator: '系统自动',
                        time: '2024/6/10 09:15:00',
                        changes: [],
                        comment: '学员通过小程序报名'
                    },
                    {
                        id: 'LOG022',
                        action: 'payment_confirm',
                        operator: '李美丽 (客服)',
                        time: '2024/6/10 14:30:00',
                        changes: [
                            { field: '支付状态', oldValue: PAYMENT_STATUS.PENDING, newValue: PAYMENT_STATUS.PAID },
                            { field: '订单状态', oldValue: ORDER_STATUS.PENDING_PAYMENT, newValue: ORDER_STATUS.EXECUTING }
                        ],
                        comment: '确认支付完成'
                    }
                ]
            },
            { 
                id: 'PT202406003', 
                studentName: '孙同学', 
                courseName: 'Python编程基础课程', 
                orderType: '个人培训', 
                amount: 2800, 
                status: ORDER_STATUS.EXECUTING,
                paymentStatus: PAYMENT_STATUS.PAID,
                learningStatus: 'processing', 
                learningStatusText: '学习中', 
                registerTime: '2024-06-15',
                relatedOpportunity: 'OPP202406011',
                relatedLead: 'LEAD202406011',
                paymentInfo: {
                    actualAmount: 2800,
                    paymentMethod: 'wechat',
                    paymentDate: '2024-06-15',
                    remarks: '微信支付'
                },
                operationLogs: [
                    {
                        id: 'LOG023',
                        action: 'create',
                        operator: '系统自动',
                        time: '2024/6/15 16:20:00',
                        changes: [],
                        comment: '学员通过App报名'
                    },
                    {
                        id: 'LOG024',
                        action: 'payment_confirm',
                        operator: '系统自动',
                        time: '2024/6/15 16:25:00',
                        changes: [
                            { field: '支付状态', oldValue: PAYMENT_STATUS.PENDING, newValue: PAYMENT_STATUS.PAID },
                            { field: '订单状态', oldValue: ORDER_STATUS.PENDING_PAYMENT, newValue: ORDER_STATUS.EXECUTING }
                        ],
                        comment: '自动确认支付完成'
                    }
                ]
            },
            { 
                id: 'CE202406001', 
                studentName: '赵同学', 
                courseName: '高级母婴护理师认证', 
                orderType: '考试认证', 
                amount: 1200, 
                status: ORDER_STATUS.PENDING_EXECUTION,
                paymentStatus: PAYMENT_STATUS.PAID,
                learningStatus: 'processing', 
                learningStatusText: '待考试', 
                registerTime: '2024-06-12',
                relatedOpportunity: 'OPP202406010',
                relatedLead: 'LEAD202406010',
                paymentInfo: {
                    actualAmount: 1200,
                    paymentMethod: 'cash',
                    paymentDate: '2024-06-12',
                    remarks: '现金支付'
                },
                operationLogs: [
                    {
                        id: 'LOG025',
                        action: 'create',
                        operator: '系统自动',
                        time: '2024/6/12 10:45:00',
                        changes: [],
                        comment: '学员通过线下报名'
                    },
                    {
                        id: 'LOG026',
                        action: 'payment_confirm',
                        operator: '王老师 (线下)',
                        time: '2024/6/12 11:00:00',
                        changes: [
                            { field: '支付状态', oldValue: PAYMENT_STATUS.PENDING, newValue: PAYMENT_STATUS.PAID },
                            { field: '订单状态', oldValue: ORDER_STATUS.PENDING_PAYMENT, newValue: ORDER_STATUS.PENDING_EXECUTION }
                        ],
                        comment: '线下现金支付确认'
                    }
                ]
            },
            { 
                id: 'CE202406002', 
                studentName: '钱同学', 
                courseName: '营养师资格认证', 
                orderType: '考试认证', 
                amount: 1500, 
                status: ORDER_STATUS.PENDING_PAYMENT,
                paymentStatus: PAYMENT_STATUS.PENDING,
                learningStatus: 'pending', 
                learningStatusText: '待确认', 
                registerTime: '2024-06-18',
                relatedOpportunity: 'OPP202406012',
                relatedLead: 'LEAD202406012',
                paymentInfo: null,
                operationLogs: [
                    {
                        id: 'LOG027',
                        action: 'create',
                        operator: '系统自动',
                        time: '2024/6/18 13:30:00',
                        changes: [],
                        comment: '学员通过代理推荐报名'
                    }
                ]
            },
            { 
                id: 'CE202406003', 
                studentName: '周同学', 
                courseName: '心理咨询师三级认证', 
                orderType: '考试认证', 
                amount: 2000, 
                status: ORDER_STATUS.COMPLETED,
                paymentStatus: PAYMENT_STATUS.PAID,
                learningStatus: 'completed', 
                learningStatusText: '已通过', 
                registerTime: '2024-05-20',
                relatedOpportunity: 'OPP202406009',
                relatedLead: 'LEAD202406009',
                paymentInfo: {
                    actualAmount: 2000,
                    paymentMethod: 'alipay',
                    paymentDate: '2024-05-20',
                    remarks: '支付宝支付'
                },
                operationLogs: [
                    {
                        id: 'LOG028',
                        action: 'create',
                        operator: '系统自动',
                        time: '2024/5/20 09:00:00',
                        changes: [],
                        comment: '学员通过小程序报名'
                    },
                    {
                        id: 'LOG029',
                        action: 'payment_confirm',
                        operator: '系统自动',
                        time: '2024/5/20 09:05:00',
                        changes: [
                            { field: '支付状态', oldValue: PAYMENT_STATUS.PENDING, newValue: PAYMENT_STATUS.PAID },
                            { field: '订单状态', oldValue: ORDER_STATUS.PENDING_PAYMENT, newValue: ORDER_STATUS.EXECUTING }
                        ],
                        comment: '自动确认支付完成'
                    },
                    {
                        id: 'LOG030',
                        action: 'complete',
                        operator: '系统自动',
                        time: '2024/6/15 14:00:00',
                        changes: [
                            { field: '订单状态', oldValue: ORDER_STATUS.EXECUTING, newValue: ORDER_STATUS.COMPLETED },
                            { field: '学习状态', oldValue: 'processing', newValue: 'completed' }
                        ],
                        comment: '考试通过，状态更新为已完成'
                    }
                ]
            },
            { 
                id: 'PT202406002', 
                studentName: '李美丽', 
                courseName: '数据分析师认证课程', 
                orderType: '个人培训', 
                amount: 3800, 
                status: ORDER_STATUS.COMPLETED,
                paymentStatus: PAYMENT_STATUS.PAID,
                learningStatus: 'completed', 
                learningStatusText: '已完成', 
                registerTime: '2024-05-15',
                relatedOpportunity: 'OPP202406011',
                relatedLead: 'LEAD202406011',
                paymentInfo: {
                    actualAmount: 3800,
                    paymentMethod: 'wechat',
                    paymentDate: '2024-05-15',
                    remarks: '微信支付'
                },
                operationLogs: [
                    {
                        id: 'LOG031',
                        action: 'create',
                        operator: '系统自动',
                        time: '2024/5/15 11:20:00',
                        changes: [],
                        comment: '学员通过App报名'
                    },
                    {
                        id: 'LOG032',
                        action: 'payment_confirm',
                        operator: '系统自动',
                        time: '2024/5/15 11:25:00',
                        changes: [
                            { field: '支付状态', oldValue: PAYMENT_STATUS.PENDING, newValue: PAYMENT_STATUS.PAID },
                            { field: '订单状态', oldValue: ORDER_STATUS.PENDING_PAYMENT, newValue: ORDER_STATUS.EXECUTING }
                        ],
                        comment: '自动确认支付完成'
                    },
                    {
                        id: 'LOG033',
                        action: 'complete',
                        operator: '系统自动',
                        time: '2024/6/10 16:30:00',
                        changes: [
                            { field: '订单状态', oldValue: ORDER_STATUS.EXECUTING, newValue: ORDER_STATUS.COMPLETED },
                            { field: '学习状态', oldValue: 'processing', newValue: 'completed' }
                        ],
                        comment: '课程学习完成'
                    }
                ]
            }
        ];

        let domesticOrdersData = [
            { 
                id: 'DS202406001', 
                customerName: '李女士', 
                customerPhone: '13812345678', 
                serviceAddress: '上海市浦东新区世纪大道1号', 
                serviceType: '月嫂服务', 
                servicePerson: '张阿姨', 
                serviceAgency: '爱家月嫂服务中心',
                amount: 12800, 
                status: ORDER_STATUS.IN_SERVICE,
                paymentStatus: PAYMENT_STATUS.PAID, 
                appointmentTime: '2024-06-01T09:00', 
                creationDate: '2024-05-28', 
                contractType: 'paper',
                contractNumber: 'HT-202406001',
                contractName: '月嫂服务合同',
                contractStartDate: '2024-6-1',
                contractEndDate: '2024-6-30',
                contractSignDate: '2024-6-1',
                paperContractFile: '李女士月嫂服务合同.pdf', 
                taskProgress: { completed: 15, total: 30 }, // 月嫂服务30天，已完成15天
                paymentInfo: {
                    actualAmount: 12800,
                    paymentMethod: '银行转账',
                    paymentDate: '2024-05-30',
                    operator: '李小明 (财务)',
                    remarks: '客户通过银行转账支付，已确认到账'
                }, 
                approvalHistory: [],
                processRecords: [
                    { time: '2024-06-01 09:05', content: '服务人员张阿姨已到达，开始服务。' },
                    { time: '2024-06-15 10:00', content: '进行了中期客户回访，客户表示满意。' }
                ],
                personnelChanges: [
                    {
                        changeTime: '2024-06-10 08:30',
                        originalPerson: '张阿姨',
                        newPerson: '王阿姨',
                        reason: '张阿姨因家中有急事请假，安排王阿姨顶岗',
                        type: '请假顶岗',
                        operator: '运营专员-李小明'
                    }
                ],
                evaluation: null,
                afterSales: [],
                financialRecords: [
                    {
                        recordId: 'FR20240610001',
                        type: 'income',
                        amount: 50,
                        date: '2024-06-10',
                        description: '额外帮助客户采购婴儿用品，客户支付的跑腿费。'
                    }
                ],
                signature: 'pending',
                operationLogs: [
                    {
                        id: 'LOG025',
                        action: 'create',
                        operator: '李小明 (客服)',
                        time: '2024/5/28 15:30:00',
                        changes: [],
                        comment: '客户通过电话预约月嫂服务'
            },
                    {
                        id: 'LOG026',
                        action: 'edit',
                        operator: '李小明 (客服)',
                        time: '2024/6/1 08:45:00',
                        changes: [
                            { field: '订单状态', oldValue: '待派单', newValue: '服务中' }
                        ],
                        comment: '服务人员已派单，开始服务'
                    },
                    {
                        id: 'LOG027',
                        action: 'edit',
                        operator: '王小红 (运营)',
                        time: '2024/6/10 08:30:00',
                        changes: [
                            { field: '服务人员', oldValue: '张阿姨', newValue: '王阿姨' }
                        ],
                        comment: '张阿姨因家中有急事请假，安排王阿姨顶岗'
                    }
                ]
            },
            { id: 'DS202406002', customerName: '王先生', customerPhone: '13987654321', serviceAddress: '北京市朝阳区建国路88号', serviceType: '深度保洁', servicePerson: '李师傅团队', serviceAgency: '清洁无忧家政公司', amount: 1500, status: '已完成', appointmentTime: '2024-06-15T14:00', creationDate: '2024-06-12', contractType: 'paper', contractNumber: 'HT-202406002', contractName: '深度保洁服务合同', contractStartDate: '2024-6-15', contractEndDate: '2024-6-15', contractSignDate: '2024-6-12', paperContractFile: null, taskProgress: { completed: 1, total: 1 }, // 深度保洁1次服务，已完成1次
                paymentInfo: {
                    actualAmount: 1500,
                    paymentMethod: '微信支付',
                    paymentDate: '2024-06-12',
                    operator: '王小红 (客服)',
                    remarks: '客户通过微信支付，服务完成后确认收款'
                }, approvalHistory: [],
                processRecords: [
                    { time: '2024-06-15 13:50', content: '服务团队已到达指定地点。' },
                    { time: '2024-06-15 14:00', content: '开始深度保洁服务。' },
                    { time: '2024-06-15 17:30', content: '保洁服务完成，客户现场验收。' }
                ],
                personnelChanges: [
                    {
                        changeTime: '2024-06-15 13:45',
                        originalPerson: '张师傅',
                        newPerson: '李师傅',
                        reason: '客户投诉张师傅服务态度不佳，更换为李师傅',
                        type: '服务质量问题',
                        operator: '运营专员-王小红'
                    }
                ],
                evaluation: {
                    rating: 5,
                    tags: ['非常专业', '准时到达', '清洁彻底'],
                    comment: '非常满意的一次服务！团队很专业，干活利索，边边角角都处理得很干净，下次有需要还会再来。'
                },
                afterSales: [
                    {
                        ticketId: 'AS20240620001',
                        type: '物品损坏',
                        description: '客户反映在深度保洁过程中，花瓶有轻微磕碰，要求赔偿。',
                        status: '已解决',
                        creationDate: '2024-06-20',
                        resolution: '与客户协商，赔偿300元，客户满意。',
                        resolutionDate: '2024-06-21'
                    }
                ],
                financialRecords: [
                    {
                        recordId: 'FR20240621001',
                        type: 'expense',
                        amount: 300,
                        date: '2024-06-21',
                        description: '因服务过程中损坏客户花瓶，进行赔偿。'
                    }
                ],
                signature: 'signed',
                operationLogs: [
                    {
                        id: 'LOG028',
                        action: 'create',
                        operator: '王小红 (客服)',
                        time: '2024/6/12 10:20:00',
                        changes: [],
                        comment: '客户通过App预约深度保洁服务'
            },
                    {
                        id: 'LOG029',
                        action: 'edit',
                        operator: '王小红 (运营)',
                        time: '2024/6/15 13:45:00',
                        changes: [
                            { field: '服务人员', oldValue: '张师傅', newValue: '李师傅' }
                        ],
                        comment: '客户投诉张师傅服务态度不佳，更换为李师傅'
                    },
                    {
                        id: 'LOG030',
                        action: 'edit',
                        operator: '系统自动',
                        time: '2024/6/15 17:30:00',
                        changes: [
                            { field: '订单状态', oldValue: '服务中', newValue: '已完成' }
                        ],
                        comment: '保洁服务完成，客户现场验收'
                    }
                ]
            },
            { id: 'DS202406003', customerName: '赵小姐', customerPhone: '13711112222', serviceAddress: '深圳市南山区科技园1路', serviceType: '小时工', servicePerson: '未指派', serviceAgency: '', amount: 320, status: '待派单', appointmentTime: '2024-06-28T10:00', creationDate: '2024-06-25', contractType: 'electronic', contractTemplate: '小时工服务协议模板', contractSignDate: '2024-6-25', paperContractFile: null, taskProgress: { completed: 0, total: 4 }, // 小时工4小时服务，未开始
                paymentInfo: {
                    actualAmount: 320,
                    paymentMethod: '支付宝',
                    paymentDate: '2024-06-25',
                    operator: '系统自动',
                    remarks: '客户通过支付宝在线支付，已自动确认'
                }, approvalHistory: [],
                processRecords: [],
                personnelChanges: [],
                evaluation: null,
                afterSales: [],
                financialRecords: [],
                signature: 'unsigned',
                operationLogs: [
                    {
                        id: 'LOG031',
                        action: 'create',
                        operator: '系统自动',
                        time: '2024/6/25 14:15:00',
                        changes: [],
                        comment: '客户通过小程序预约小时工服务'
                    }
                ]
            },
            { 
                id: 'DS202406004', 
                customerName: '陈女士', 
                customerPhone: '13655556666', 
                serviceAddress: '广州市天河区珠江新城', 
                serviceType: '育儿嫂服务', 
                servicePerson: '王阿姨', 
                serviceAgency: '母婴护理专家机构',
                amount: 9800, 
                status: '已派单', 
                appointmentTime: '2024-07-01T08:00', 
                creationDate: '2024-06-28', 
                contractType: 'paper',
                contractNumber: 'HT-202406004',
                contractName: '育儿嫂服务合同',
                contractStartDate: '2024-7-1',
                contractEndDate: '2024-7-31',
                contractSignDate: '2024-6-28',
                paperContractFile: null, 
                taskProgress: { completed: 0, total: 31 }, // 育儿嫂服务31天，未开始
                paymentInfo: {
                    actualAmount: 9800,
                    paymentMethod: '现金',
                    paymentDate: '2024-06-28',
                    operator: '赵小华 (财务)',
                    remarks: '客户到店现金支付，已开具收据'
                }, 
                approvalHistory: [],
                processRecords: [],
                personnelChanges: [],
                evaluation: null,
                afterSales: [],
                financialRecords: [],
                signature: 'unsigned',
                assignedProvider: {
                    id: 'AG003',
                    name: '母婴护理专家机构',
                    type: 'agency',
                    phone: '021-11112222',
                    assignedTime: '2024-06-28 16:30'
                },
                operationLogs: [
                    {
                        id: 'LOG032',
                        action: 'create',
                        operator: '系统自动',
                        time: '2024/6/28 14:20:00',
                        changes: [],
                        comment: '客户通过App预约育儿嫂服务'
                    },
                    {
                        id: 'LOG033',
                        type: '派单',
                        operator: '系统管理员',
                        time: '2024/6/28 16:30:00',
                        changes: {
                            status: {
                                before: '待派单',
                                after: '已派单'
                            },
                            assignedProvider: {
                                before: null,
                                after: '母婴护理专家机构 (服务机构)'
                            }
                        },
                        comment: '派单给服务机构: 母婴护理专家机构'
                    }
                ]
            }
        ];

        // 个人阿姨数据
        const individualProvidersData = [
            {
                id: 'IP001',
                name: '张阿姨',
                phone: '13812345678',
                avatar: '张',
                age: 45,
                experience: 8,
                rating: 4.8,
                completedOrders: 156,
                skills: ['月嫂', '育儿嫂'],
                tags: ['金牌月嫂', '经验丰富'],
                status: 'available',
                hourlyRate: 80,
                location: '上海市浦东新区',
                introduction: '从事月嫂工作8年，经验丰富，服务态度好，深受客户好评。',
                certificates: ['高级母婴护理师证', '育婴师证'],
                availableTime: '全天候',
                lastActive: '2024-06-19 14:30'
            },
            {
                id: 'IP002',
                name: '李阿姨',
                phone: '13987654321',
                avatar: '李',
                age: 42,
                experience: 6,
                rating: 4.6,
                completedOrders: 98,
                skills: ['育儿嫂', '家务保姆'],
                tags: ['专业育儿', '细心耐心'],
                status: 'available',
                hourlyRate: 70,
                location: '上海市浦东新区',
                introduction: '专业育儿嫂，有6年育儿经验，擅长照顾0-3岁婴幼儿。',
                certificates: ['育婴师证', '家政服务员证'],
                availableTime: '全天候',
                lastActive: '2024-06-19 15:20'
            },
            {
                id: 'IP003',
                name: '王阿姨',
                phone: '13711112222',
                avatar: '王',
                age: 48,
                experience: 10,
                rating: 4.9,
                completedOrders: 203,
                skills: ['月嫂', '育儿嫂', '家务保姆'],
                tags: ['金牌月嫂', '全能型', '平台自营'],
                status: 'available',
                hourlyRate: 90,
                location: '上海市浦东新区',
                introduction: '金牌月嫂，10年经验，服务过200多个家庭，客户满意度极高。',
                certificates: ['高级母婴护理师证', '育婴师证', '营养师证'],
                availableTime: '全天候',
                lastActive: '2024-06-19 16:00'
            },
            {
                id: 'IP004',
                name: '赵阿姨',
                phone: '13622223333',
                avatar: '赵',
                age: 38,
                experience: 4,
                rating: 4.4,
                completedOrders: 67,
                skills: ['小时工', '深度保洁'],
                tags: ['年轻活力', '效率高'],
                status: 'available',
                hourlyRate: 60,
                location: '上海市浦东新区',
                introduction: '年轻有活力，工作效率高，擅长深度保洁和小时工服务。',
                certificates: ['家政服务员证', '保洁员证'],
                availableTime: '全天候',
                lastActive: '2024-06-19 13:45'
            },
            {
                id: 'IP005',
                name: '陈阿姨',
                phone: '13533334444',
                avatar: '陈',
                age: 50,
                experience: 12,
                rating: 4.7,
                completedOrders: 178,
                skills: ['月嫂', '育儿嫂'],
                tags: ['资深月嫂', '专业护理'],
                status: 'busy',
                hourlyRate: 85,
                location: '上海市浦东新区',
                introduction: '资深月嫂，12年经验，专业护理技能强，服务态度好。',
                certificates: ['高级母婴护理师证', '育婴师证', '催乳师证'],
                availableTime: '全天候',
                lastActive: '2024-06-19 12:30'
            }
        ];

        // 服务机构数据
        const agencyProvidersData = [
            {
                id: 'AG001',
                name: '爱家月嫂服务中心',
                phone: '021-12345678',
                avatar: '爱',
                rating: 4.7,
                completedOrders: 1250,
                services: ['月嫂服务', '育儿嫂服务'],
                tags: ['专业机构', '服务保障'],
                status: 'available',
                staffCount: 45,
                location: '上海市浦东新区',
                introduction: '专业月嫂服务机构，拥有45名专业月嫂，服务保障完善。',
                certificates: ['营业执照', '劳务派遣许可证'],
                availableTime: '7:00-22:00',
                lastActive: '2024-06-19 14:00',
                priceRange: '8000-15000元/月'
            },
            {
                id: 'AG002',
                name: '清洁无忧家政公司',
                phone: '021-87654321',
                avatar: '清',
                rating: 4.5,
                completedOrders: 890,
                services: ['家务保姆', '小时工', '深度保洁'],
                tags: ['专业保洁', '价格实惠'],
                status: 'available',
                staffCount: 32,
                location: '上海市浦东新区',
                introduction: '专业家政服务公司，提供全方位的家政服务，价格实惠。',
                certificates: ['营业执照', '家政服务许可证'],
                availableTime: '6:00-21:00',
                lastActive: '2024-06-19 15:30',
                priceRange: '50-200元/小时'
            },
            {
                id: 'AG003',
                name: '母婴护理专家机构',
                phone: '021-11112222',
                avatar: '母',
                rating: 4.8,
                completedOrders: 1560,
                services: ['月嫂服务', '育儿嫂服务'],
                tags: ['专业母婴', '高端服务'],
                status: 'available',
                staffCount: 68,
                location: '上海市浦东新区',
                introduction: '专业母婴护理机构，提供高端月嫂和育儿嫂服务。',
                certificates: ['营业执照', '母婴护理许可证'],
                availableTime: '全天候',
                lastActive: '2024-06-19 16:15',
                priceRange: '12000-20000元/月'
            },
            {
                id: 'AG004',
                name: '汇成家政服务公司',
                phone: '021-22223333',
                avatar: '汇',
                rating: 4.6,
                completedOrders: 980,
                services: ['月嫂服务', '育儿嫂服务', '家务保姆', '小时工'],
                tags: ['综合服务', '平台自营'],
                status: 'available',
                staffCount: 55,
                location: '上海市浦东新区',
                introduction: '汇成平台自营家政服务公司，提供综合家政服务。',
                certificates: ['营业执照', '家政服务许可证'],
                availableTime: '全天候',
                lastActive: '2024-06-19 17:00',
                priceRange: '60-180元/小时'
            }
        ];
        
        // --- 渲染与计算 ---
        function renderOrderStats() {
            console.log("DEBUG: renderOrderStats called.");
            const totalOrders = trainingOrdersData.length + personalOrdersData.length + domesticOrdersData.length + practiceOrdersData.length;
            const pendingOrders = trainingOrdersData.filter(order => order.status === 'pending').length + 
                                  personalOrdersData.filter(order => order.paymentStatus === 'pending').length;
            
            const totalAmount = [...trainingOrdersData, ...personalOrdersData, ...domesticOrdersData, ...practiceOrdersData].reduce((sum, order) => sum + (order.amount || 0), 0);

            const statCards = document.querySelectorAll('#order-center .stat-card .stat-number');
            if (statCards.length >= 4) {
                statCards[0].textContent = totalOrders;
                statCards[1].textContent = pendingOrders;
                statCards[2].textContent = '¥' + totalAmount.toLocaleString();
                statCards[3].textContent = '92.5%';
            }
        }

        // --- 页面加载完成后的主执行函数 ---
        document.addEventListener('DOMContentLoaded', () => {
            console.log("DEBUG: DOMContentLoaded event fired. Initializing page.");
            // 设置导航
            setupNavigation();
            
            // 设置订单中心事件监听
            setupOrderCenterEventListeners();

            // 设置高校实践项目表格区域的事件委托
            const practiceTbody = document.getElementById('practice-orders-tbody');
            if (practiceTbody) {
                console.log("DEBUG: Setting up event listener for practice-orders-tbody.");
                practiceTbody.addEventListener('click', (e) => {
                    const button = e.target.closest('button.btn-action');
                    if (!button) return;

                    const id = button.dataset.id;
                    console.log(`DEBUG: Project table button clicked. Action: ${button.className}, ID: ${id}`);
                    if (button.classList.contains('btn-view')) {
                        openPracticeDetailDrawer(id);
                    } else if (button.classList.contains('btn-edit')) {
                        openPracticeDrawer('edit', id);
                    } else if (button.classList.contains('btn-delete')) {
                        deletePracticeOrder(id);
                    }
                });
            } else {
                console.error("DEBUG: practice-orders-tbody element not found for event delegation!");
            }

            // 设置企业培训订单表格区域的事件委托
            const trainingTbody = document.getElementById('training-orders-tbody');
            if (trainingTbody) {
                console.log("DEBUG: Setting up event listener for training-orders-tbody.");
                trainingTbody.addEventListener('click', (e) => {
                    const button = e.target.closest('button.btn-action');
                    if (!button) return;

                    const id = button.dataset.id;
                    console.log(`DEBUG: Training table button clicked. Action: ${button.className}, ID: ${id}`);
                    if (button.classList.contains('btn-view')) {
                        openTrainingDetailDrawer(id);
                    } else if (button.classList.contains('btn-edit')) {
                        openTrainingDrawer('edit', id);
                    } else if (button.classList.contains('btn-delete')) {
                        deleteTrainingOrder(id);
                    }
                });
            } else {
                console.error("DEBUG: training-orders-tbody element not found for event delegation!");
            }
            
            // 总的"新建订单"按钮
            const mainNewOrderBtn = document.querySelector('#order-center .order-header .btn-new');
            if(mainNewOrderBtn) {
                 console.log("DEBUG: Setting up event listener for main new order button.");
                mainNewOrderBtn.addEventListener('click', showNewOrderModal);
            }
            
            // 高校实践工具栏的"新建"按钮
            const newPracticeOrderBtn = document.querySelector('#practice-orders .btn-new');
            if(newPracticeOrderBtn) {
                console.log("DEBUG: Setting up event listener for new practice order button.");
                newPracticeOrderBtn.addEventListener('click', () => openPracticeDrawer('new'));
            }

            // 企业培训工具栏的"新建"按钮
            const newTrainingOrderBtn = document.querySelector('#training-orders .btn-new');
            if(newTrainingOrderBtn) {
                console.log("DEBUG: Setting up event listener for new training order button.");
                newTrainingOrderBtn.addEventListener('click', () => openTrainingDrawer('new'));
            }

            // 新建/编辑高校实践订单抽屉内的事件
            const practiceDrawer = document.getElementById('practice-order-drawer');
            if(practiceDrawer) {
                console.log("DEBUG: Setting up event listeners for practice drawer.");
                // 保存按钮
                practiceDrawer.querySelector('.btn-primary').addEventListener('click', submitPracticeOrder);
                // 取消按钮
                practiceDrawer.querySelector('.btn-outline').addEventListener('click', closePracticeDrawer);
                // 右上角关闭按钮
                practiceDrawer.querySelector('.drawer-header button').addEventListener('click', closePracticeDrawer);
            }

            // 新建/编辑企业培训订单抽屉内的事件
            const trainingDrawer = document.getElementById('training-order-drawer');
            if(trainingDrawer) {
                console.log("DEBUG: Setting up event listeners for training drawer.");
                trainingDrawer.querySelector('.btn-primary').addEventListener('click', submitTrainingOrder);
                trainingDrawer.querySelector('.btn-outline').addEventListener('click', closeTrainingDrawer);
                trainingDrawer.querySelector('.drawer-header button').addEventListener('click', closeTrainingDrawer);
            }

            // 查看详情抽屉内的事件
            const detailDrawer = document.getElementById('practice-detail-drawer');
            if(detailDrawer) {
                console.log("DEBUG: Setting up event listeners for detail drawer.");
                // 编辑按钮
                detailDrawer.querySelector('.btn-primary').addEventListener('click', editFromDetailView);
                // 关闭按钮
                detailDrawer.querySelector('.btn-outline').addEventListener('click', closePracticeDetailDrawer);
                // 右上角关闭按钮
                detailDrawer.querySelector('.drawer-header button').addEventListener('click', closePracticeDetailDrawer);
            }

            // 查看企业培训详情抽屉内的事件
            const trainingDetailDrawer = document.getElementById('training-detail-drawer');
            if(trainingDetailDrawer) {
                console.log("DEBUG: Setting up event listeners for training detail drawer.");
                trainingDetailDrawer.querySelector('.btn-primary').addEventListener('click', editTrainingFromDetailView);
                trainingDetailDrawer.querySelector('.btn-outline').addEventListener('click', closeTrainingDetailDrawer);
                trainingDetailDrawer.querySelector('.drawer-header button').addEventListener('click', closeTrainingDetailDrawer);
            }
            
            // 企业培训订单筛选功能
            const trainingStatusFilter = document.getElementById('training-status-filter');
            const trainingPaymentFilter = document.getElementById('training-payment-filter');
            const trainingSearch = document.getElementById('training-search');
            
            if (trainingStatusFilter) {
                trainingStatusFilter.addEventListener('change', filterTrainingOrders);
            }
            if (trainingPaymentFilter) {
                trainingPaymentFilter.addEventListener('change', filterTrainingOrders);
            }
            if (trainingSearch) {
                trainingSearch.addEventListener('input', filterTrainingOrders);
            }
            
            // 个人培训订单筛选功能
            const personalStatusFilter = document.getElementById('personal-status-filter');
            const personalPaymentFilter = document.getElementById('personal-payment-filter');
            const personalTypeFilter = document.getElementById('personal-type-filter');
            const personalSearch = document.getElementById('personal-search');
            
            if (personalStatusFilter) {
                personalStatusFilter.addEventListener('change', filterPersonalOrders);
            }
            if (personalPaymentFilter) {
                personalPaymentFilter.addEventListener('change', filterPersonalOrders);
            }
            if (personalTypeFilter) {
                personalTypeFilter.addEventListener('change', filterPersonalOrders);
            }
            if (personalSearch) {
                personalSearch.addEventListener('input', filterPersonalOrders);
            }
            
            // 个人培训订单按钮事件监听
            const personalOrdersContainer = document.getElementById('personal-orders');
            if (personalOrdersContainer) {
                personalOrdersContainer.addEventListener('click', function(e) {
                if (e.target.classList.contains('btn-view') && e.target.dataset.id) {
                    openPersonalDetailDrawer(e.target.dataset.id);
                } else if (e.target.classList.contains('btn-edit') && e.target.dataset.id) {
                    openPersonalDrawer('edit', e.target.dataset.id);
                } else if (e.target.classList.contains('btn-delete') && e.target.dataset.id) {
                    deletePersonalOrder(e.target.dataset.id);
                }
            });
            }
            
            // 个人培训订单合同类型切换
            document.addEventListener('change', function(e) {
                if (e.target.name === 'contractType') {
                    const electronicOptions = document.getElementById('electronic-contract-options');
                    const paperOptions = document.getElementById('paper-contract-options');
                    
                    if (e.target.value === 'electronic') {
                        electronicOptions.style.display = 'block';
                        paperOptions.style.display = 'none';
                    } else {
                        electronicOptions.style.display = 'none';
                        paperOptions.style.display = 'block';
                    }
                }
            });
            
            // 个人培训订单纸质合同文件上传
            document.addEventListener('change', function(e) {
                if (e.target.name === 'paperContract') {
                    const filename = e.target.files[0]?.name;
                    const filenameElement = document.getElementById('personal-paper-contract-filename');
                    if (filenameElement) {
                        filenameElement.textContent = filename || '';
                    }
                }
            });
            
            // 家政服务订单筛选功能
            const domesticStatusFilter = document.getElementById('domestic-status-filter');
            const domesticPaymentFilter = document.getElementById('domestic-payment-filter');
            const domesticTypeFilter = document.getElementById('domestic-type-filter');
            const domesticSearch = document.getElementById('domestic-search');
            
            if (domesticStatusFilter) {
                domesticStatusFilter.addEventListener('change', filterDomesticOrders);
            }
            if (domesticPaymentFilter) {
                domesticPaymentFilter.addEventListener('change', filterDomesticOrders);
            }
            if (domesticTypeFilter) {
                domesticTypeFilter.addEventListener('change', filterDomesticOrders);
            }
            if (domesticSearch) {
                domesticSearch.addEventListener('input', filterDomesticOrders);
            }
            
            // 家政服务订单按钮事件监听
            const domesticOrdersContainer = document.getElementById('domestic-orders');
            if (domesticOrdersContainer) {
                domesticOrdersContainer.addEventListener('click', function(e) {
                    console.log('DEBUG: Domestic orders container clicked:', e.target);
                if (e.target.classList.contains('btn-view') && e.target.dataset.id) {
                        console.log('DEBUG: View button clicked for order:', e.target.dataset.id);
                    openDomesticDetailDrawer(e.target.dataset.id);
                } else if (e.target.classList.contains('btn-edit') && e.target.dataset.id) {
                        console.log('DEBUG: Edit button clicked for order:', e.target.dataset.id);
                    openDomesticDrawer('edit', e.target.dataset.id);
                } else if (e.target.classList.contains('btn-delete') && e.target.dataset.id) {
                        console.log('DEBUG: Delete button clicked for order:', e.target.dataset.id);
                    deleteDomesticOrder(e.target.dataset.id);
                }
            });
            } else {
                console.error('DEBUG: domestic-orders container not found!');
            }
            
            // 家政服务订单合同类型切换
            document.addEventListener('change', function(e) {
                if (e.target.name === 'contractType') {
                    const electronicOptions = document.getElementById('domestic-electronic-contract-options');
                    const paperOptions = document.getElementById('domestic-paper-contract-options');
                    
                    if (e.target.value === 'electronic') {
                        electronicOptions.style.display = 'block';
                        paperOptions.style.display = 'none';
                    } else {
                        electronicOptions.style.display = 'none';
                        paperOptions.style.display = 'block';
                    }
                }
            });
            
            // 家政服务订单纸质合同文件上传
            document.addEventListener('change', function(e) {
                if (e.target.name === 'paperContract' && e.target.closest('#domestic-order-drawer')) {
                    const filename = e.target.files[0]?.name;
                    const filenameElement = document.getElementById('domestic-paper-contract-filename');
                    if (filenameElement) {
                        filenameElement.textContent = filename || '';
                    }
                }
            });

            // 设置初始视图
            console.log("DEBUG: Setting initial view to 'dashboard'.");
            switchView('dashboard');
            setupStarRating();
            console.log("DEBUG: Page initialization complete.");
        });

        function approvePracticeOrder(projectId) {
            const project = practiceOrdersData.find(p => p.id === projectId);
            if(project) {
                const oldStatus = project.status;
                project.status = '待开始';
                project.approvalHistory.push({
                    action: '批准',
                    user: '张三 (管理员)',
                    time: new Date().toLocaleString('zh-CN'),
                    comments: '符合项目要求，审批通过。'
                });
                
                // 记录操作日志
                addOperationLog('practice', projectId, 'approve', [
                    { field: '订单状态', oldValue: oldStatus, newValue: '待开始' }
                ], '符合项目要求，审批通过');
                
                closePracticeDetailDrawer();
                renderPracticeProjects();
            }
        }

        function rejectPracticeOrder(projectId) {
            const reason = prompt('请输入驳回理由：');
            if (reason) {
                const project = practiceOrdersData.find(p => p.id === projectId);
                if(project) {
                    const oldStatus = project.status;
                    project.status = '审批驳回';
                    project.approvalHistory.push({
                        action: '驳回',
                        user: '张三 (管理员)',
                        time: new Date().toLocaleString('zh-CN'),
                        comments: reason
                    });
                    
                    // 记录操作日志
                    addOperationLog('practice', projectId, 'reject', [
                        { field: '订单状态', oldValue: oldStatus, newValue: '审批驳回' }
                    ], reason);
                    
                    closePracticeDetailDrawer();
                    renderPracticeProjects();
                }
            }
        }

        // --- 企业培训订单相关功能 ---

        function renderTrainingOrders() {
            console.log("DEBUG: renderTrainingOrders called.");
            const tbody = document.getElementById('training-orders-tbody');
            if (!tbody) {
                console.error("DEBUG: training-orders-tbody container not found!");
                return;
            }
            tbody.innerHTML = '';

            if (trainingOrdersData.length === 0) {
                tbody.innerHTML = `<tr><td colspan="10" style="text-align:center; padding: 20px;">暂无企业培训订单</td></tr>`;
                return;
            }

            trainingOrdersData.forEach(order => {
                const statusClass = statusClassMap[order.status] || 'status-pending';
                const paymentStatusClass = paymentStatusClassMap[order.paymentStatus] || 'status-pending';
                const rowHtml = `
                <tr data-id="${order.id}">
                    <td><span class="order-id" style="cursor:default;">${order.id}</span></td>
                    <td>${order.enterpriseName}</td>
                    <td>${order.trainingProjectName}</td>
                    <td>${order.participantsCount}人</td>
                    <td>${order.period || '-'}</td>
                    <td>¥${Number(order.amount).toLocaleString()}</td>
                    <td><span class="status-badge ${statusClass}">${order.status}</span></td>
                    <td><span class="status-badge ${paymentStatusClass}">${order.paymentStatus}</span></td>
                    <td>${order.createTime}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-action btn-view" data-id="${order.id}">查看</button>
                            <button class="btn-action btn-edit" data-id="${order.id}">编辑</button>
                            <button class="btn-action btn-delete" data-id="${order.id}">删除</button>
                            <button class="btn-action" style="background: var(--gray); color: white;" onclick="openOperationLog('training', '${order.id}')">操作日志</button>
                        </div>
                    </td>
                </tr>`;
                tbody.insertAdjacentHTML('beforeend', rowHtml);
            });
            console.log(`DEBUG: Rendered ${trainingOrdersData.length} training order rows.`);
        }

        function openTrainingDrawer(mode = 'new', orderId = null) {
            const drawer = document.getElementById('training-order-drawer');
            const title = document.getElementById('training-drawer-title');
            const form = document.getElementById('training-order-form');
            
            form.reset();
            currentTrainingOrderId = null;
            const fileNameDisplay = document.getElementById('training-paper-contract-filename');
            fileNameDisplay.textContent = '';

            if (mode === 'edit' && orderId) {
                title.textContent = '编辑企业培训订单';
                const order = trainingOrdersData.find(o => o.id === orderId);
                if (order) {
                    currentTrainingOrderId = orderId;
                    form.enterpriseName.value = order.enterpriseName;
                    form.trainingProjectName.value = order.trainingProjectName;
                    if(order.period) {
                        const [start, end] = order.period.split(' - ');
                        form.startDate.value = start ? start.replace(/\./g, '-') : '';
                        form.endDate.value = end ? end.replace(/\./g, '-') : '';
                    }
                    form.participantsCount.value = order.participantsCount;
                    form.amount.value = order.amount;
                    form.paymentStatus.value = order.paymentStatus || 'pending';
                    form.manager.value = order.manager;
                    
                    // 填充支付信息
                    if (order.paymentInfo) {
                        form.actualAmount.value = order.paymentInfo.actualAmount || '';
                        form.paymentMethod.value = order.paymentInfo.paymentMethod || '';
                        form.paymentDate.value = order.paymentInfo.paymentDate || '';
                        form.operator.value = order.paymentInfo.operator || '';
                        form.remarks.value = order.paymentInfo.remarks || '';
                    }
                    
                    // 触发支付状态变化事件，显示/隐藏支付信息区域
                    togglePaymentInfo('training');

                    if (order.paperContractFile) {
                        fileNameDisplay.textContent = `当前文件: ${order.paperContractFile}`;
                    }
                }
            } else {
                title.textContent = '新建企业培训订单';
            }

            drawer.classList.add('open');
            document.querySelector('.overlay').classList.add('active');
            
            // 设置关联商机和线索选择事件
            setupTrainingOpportunitySelection();
            setupTrainingLeadSelection();
        }

        function closeTrainingDrawer() {
            document.getElementById('training-order-drawer').classList.remove('open');
            if (!document.querySelector('.drawer.open')) {
                document.querySelector('.overlay').classList.remove('active');
            }
        }

        function submitTrainingOrder() {
            const form = document.getElementById('training-order-form');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const orderDataFromForm = {
                enterpriseName: form.enterpriseName.value,
                trainingProjectName: form.trainingProjectName.value,
                period: `${form.startDate.value.replace(/-/g, '.')} - ${form.endDate.value.replace(/-/g, '.')}`,
                participantsCount: form.participantsCount.value,
                amount: form.amount.value,
                paymentStatus: form.paymentStatus.value,
                manager: form.manager.value,
                relatedOpportunity: form.relatedOpportunity.value, // 添加关联商机字段
                relatedLead: form.relatedLead.value, // 添加关联线索字段
                // 支付信息
                paymentInfo: form.paymentStatus.value === 'completed' ? {
                    actualAmount: form.actualAmount.value,
                    paymentMethod: form.paymentMethod.value,
                    paymentDate: form.paymentDate.value,
                    operator: form.operator.value,
                    remarks: form.remarks.value
                } : null
            };
            
            const fileInput = form.paperContract;
            if (fileInput.files.length > 0) {
                orderDataFromForm.paperContractFile = fileInput.files[0].name;
            }

            if (currentTrainingOrderId) {
                // 编辑模式 - 记录变更
                const index = trainingOrdersData.findIndex(o => o.id === currentTrainingOrderId);
                if (index !== -1) {
                    const oldData = trainingOrdersData[index];
                    if (!orderDataFromForm.paperContractFile) {
                        orderDataFromForm.paperContractFile = oldData.paperContractFile;
                    }
                    
                    // 比较变更
                    const fieldMap = {
                        enterpriseName: '企业名称',
                        trainingProjectName: '培训项目',
                        period: '培训周期',
                        participantsCount: '培训人数',
                        amount: '订单金额',
                        manager: '我方负责人',
                        relatedOpportunity: '关联商机',
                        relatedLead: '关联线索'
                    };
                    
                    const changes = compareObjects(oldData, orderDataFromForm, fieldMap);
                    
                    trainingOrdersData[index] = { ...oldData, ...orderDataFromForm };
                    
                    // 记录操作日志
                    if (changes.length > 0) {
                        addOperationLog('training', currentTrainingOrderId, 'edit', changes, '编辑订单信息');
                    }
                }
            } else {
                // 新建模式
                orderDataFromForm.id = 'ET' + new Date().getTime();
                orderDataFromForm.paperContractFile = orderDataFromForm.paperContractFile || null;
                orderDataFromForm.status = ORDER_STATUS.PENDING_APPROVAL;
                orderDataFromForm.approvalHistory = [];
                orderDataFromForm.signatures = {
                    platform: 'unsigned',
                    enterprise: 'unsigned'
                };
                orderDataFromForm.createTime = new Date().toLocaleDateString('zh-CN').replace(/\//g, '-');
                orderDataFromForm.operationLogs = [];
                trainingOrdersData.unshift(orderDataFromForm);
                
                // 记录创建日志
                addOperationLog('training', orderDataFromForm.id, 'create', [], '创建企业培训订单');
            }
            
            renderTrainingOrders();
            closeTrainingDrawer();
        }

        function openTrainingDetailDrawer(orderId) {
            currentTrainingOrderId = orderId;
            const order = trainingOrdersData.find(o => o.id === orderId);
            if (!order) return;
            
            const detailBody = document.getElementById('training-detail-body');

            const signatureStatusMap = {
                unsigned: { text: '未发起', badge: 'status-unsigned' },
                pending: { text: '待签署', badge: 'status-pending' },
                signed: { text: '已签署', badge: 'status-completed' }
            };

            const getSignatureStatusHtml = (party, status) => {
                const statusInfo = signatureStatusMap[status] || signatureStatusMap['unsigned'];
                const partyInfo = {
                    platform: { name: '平台方', icon: 'fa-server' },
                    enterprise: { name: '企业方', icon: 'fa-building' }
                };
                return `
                    <li class="signature-item">
                        <div class="signature-party">
                            <i class="fas ${partyInfo[party].icon}"></i> ${partyInfo[party].name}
                        </div>
                        <div class="signature-status">
                            <span class="status-badge ${statusInfo.badge}">${statusInfo.text}</span>
                        </div>
                    </li>
                `;
            };

            const signaturesHtml = order.signatures ? `
                <div class="section-title">电子签约状态</div>
                <ul class="signature-list">
                    ${getSignatureStatusHtml('platform', order.signatures.platform)}
                    ${getSignatureStatusHtml('enterprise', order.signatures.enterprise)}
                </ul>
                <div style="text-align: right; margin-top: 15px;">
                    <button class="btn btn-outline" style="margin-right: 10px; font-size:12px; padding: 5px 10px;" onclick="alert('查看协议功能开发中...')">查看协议</button>
                    <button class="btn btn-primary" style="font-size:12px; padding: 5px 10px;" onclick="alert('发起签约功能开发中...')">发起签约</button>
                </div>
            ` : '';

            const paperContractHtml = order.paperContractFile ? `
                <div class="attachment-item">
                    <i class="fas fa-file-contract"></i>
                    <span class="attachment-name">${order.paperContractFile}</span>
                    <button class="btn btn-outline" style="font-size:12px; padding: 5px 10px;" onclick="alert('下载功能开发中...')">下载</button>
                </div>
                ${order.paperContractInfo ? `
                    <div class="contract-info" style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <div class="section-title" style="margin-bottom: 10px;">合同信息</div>
                        <div class="project-meta">
                            <div class="meta-item"><div class="meta-label">合同名称</div><div class="meta-value">${order.paperContractInfo.contractName}</div></div>
                            <div class="meta-item"><div class="meta-label">合同编号</div><div class="meta-value">${order.paperContractInfo.contractNumber}</div></div>
                            <div class="meta-item"><div class="meta-label">签署日期</div><div class="meta-value">${order.paperContractInfo.signDate}</div></div>
                            <div class="meta-item"><div class="meta-label">合同金额</div><div class="meta-value">¥${Number(order.paperContractInfo.contractAmount).toLocaleString()}</div></div>
                            ${order.paperContractInfo.remarks ? `<div class="meta-item" style="grid-column: 1 / -1;"><div class="meta-label">备注</div><div class="meta-value">${order.paperContractInfo.remarks}</div></div>` : ''}
                        </div>
                    </div>
                ` : ''}
            ` : `<div style="padding: 15px 0; color: var(--gray);">暂未上传纸质合同</div>`;

            const approvalHistoryHtml = order.approvalHistory && order.approvalHistory.length > 0
                ? order.approvalHistory.map(item => `
                    <li class="timeline-item">
                        <div class="timeline-time">${item.time}</div>
                        <div class="timeline-content">
                            <strong>${item.user}</strong> ${item.action === '批准' ? '批准了订单' : '驳回了订单'}。
                            ${item.comments ? `<div style="margin-top:5px; font-style: italic;">备注: ${item.comments}</div>` : ''}
                        </div>
                    </li>
                `).join('')
                : '<li>无审批记录</li>';
            
            // 审批和收款操作按钮
            let approvalActionsHtml = '';
            if (order.status === ORDER_STATUS.PENDING_APPROVAL) {
                approvalActionsHtml = `
                    <div style="text-align: right; margin-top: 15px;">
                        <button class="btn btn-primary" style="font-size:12px; padding: 5px 10px;" onclick="initiateApproval('training', '${order.id}')">
                            <i class="fas fa-gavel"></i> 发起审批
                        </button>
                    </div>
                `;
            } else if (order.status === ORDER_STATUS.APPROVING) {
                approvalActionsHtml = `
                    <div style="text-align: right; margin-top: 15px;">
                        <button class="btn btn-success" style="margin-right: 10px; background:var(--success); color:white; font-size:12px; padding: 5px 10px;" onclick="approveTrainingOrder('${order.id}')">
                            <i class="fas fa-check"></i> 同意
                        </button>
                        <button class="btn btn-danger" style="background:var(--danger); color:white; font-size:12px; padding: 5px 10px;" onclick="rejectTrainingOrder('${order.id}')">
                            <i class="fas fa-times"></i> 拒绝
                        </button>
                    </div>
                `;
            } else if (order.status === ORDER_STATUS.PENDING_PAYMENT) {
                approvalActionsHtml = `
                    <div style="text-align: right; margin-top: 15px;">
                        <button class="btn btn-success" style="background:var(--success); color:white; font-size:12px; padding: 5px 10px;" onclick="openPaymentModal('training', '${order.id}')">
                            <i class="fas fa-money-bill-wave"></i> 确认收款
                        </button>
                    </div>
                `;
            }

            // 收款信息显示
            const paymentInfoHtml = order.paymentInfo ? `
                <div class="section">
                    <div class="section-title">
                        <i class="fas fa-money-bill-wave" style="color: #27ae60; margin-right: 8px;"></i>
                        收款信息
                    </div>
                    <div class="project-card payment-info-card" style="box-shadow:none; border: 1px solid #27ae60;">
                        <div class="project-meta">
                            <div class="meta-item"><div class="meta-label">收款金额</div><div class="meta-value" style="color: #27ae60; font-weight: bold;">¥${Number(order.paymentInfo.actualAmount).toLocaleString()}</div></div>
                            <div class="meta-item"><div class="meta-label">收款方式</div><div class="meta-value">${getPaymentMethodText(order.paymentInfo.paymentMethod)}</div></div>
                            <div class="meta-item"><div class="meta-label">收款日期</div><div class="meta-value">${order.paymentInfo.paymentDate}</div></div>
                            <div class="meta-item"><div class="meta-label">操作人</div><div class="meta-value">${order.paymentInfo.operator}</div></div>
                            ${order.paymentInfo.remarks ? `<div class="meta-item" style="grid-column: 1 / -1;"><div class="meta-label">收款备注</div><div class="meta-value">${order.paymentInfo.remarks}</div></div>` : ''}
                        </div>
                    </div>
                </div>
            ` : '';

            detailBody.innerHTML = `
                <div class="project-card" style="box-shadow:none; border: 1px solid var(--border);">
                    <div class="project-header">
                        <div class="project-title">${order.trainingProjectName}</div>
                        <div class="project-status status-badge ${statusClassMap[order.status] || 'status-pending'}">${order.status}</div>
                    </div>
                    <div class="project-meta">
                        <div class="meta-item"><div class="meta-label">订单号</div><div class="meta-value">${order.id}</div></div>
                        <div class="meta-item"><div class="meta-label">合作企业</div><div class="meta-value">${order.enterpriseName}</div></div>
                        <div class="meta-item"><div class="meta-label">培训周期</div><div class="meta-value">${order.period}</div></div>
                        <div class="meta-item"><div class="meta-label">培训人数</div><div class="meta-value">${order.participantsCount}人</div></div>
                        <div class="meta-item"><div class="meta-label">订单金额</div><div class="meta-value">¥${Number(order.amount).toLocaleString()}</div></div>
                        <div class="meta-item"><div class="meta-label">支付状态</div><div class="meta-value"><span class="status-badge ${paymentStatusClassMap[order.paymentStatus] || 'status-pending'}">${order.paymentStatus}</span></div></div>
                        <div class="meta-item"><div class="meta-label">我方负责人</div><div class="meta-value">${order.manager}</div></div>
                        <div class="meta-item"><div class="meta-label">创建时间</div><div class="meta-value">${order.createTime}</div></div>
                        ${order.relatedOpportunity ? `<div class="meta-item"><div class="meta-label">关联商机</div><div class="meta-value">${order.relatedOpportunity}</div></div>` : ''}
                        ${order.relatedLead ? `<div class="meta-item"><div class="meta-label">关联线索</div><div class="meta-value">${order.relatedLead}</div></div>` : ''}
                    </div>
                </div>
                
                <!-- 合同管理区域 -->
                <div class="section">
                    <div class="section-title">
                        <i class="fas fa-file-contract" style="color: #3498db; margin-right: 8px;"></i>
                        合同管理
                    </div>
                    <div class="contract-management">
                        <div class="contract-tabs">
                            <button class="contract-tab active" onclick="switchContractTab('electronic')">电子合同</button>
                            <button class="contract-tab" onclick="switchContractTab('paper')">纸质合同</button>
                        </div>
                        <div id="electronic-contract-content" class="contract-content">
                            ${signaturesHtml}
                        </div>
                        <div id="paper-contract-content" class="contract-content" style="display: none;">
                            <div class="section-title">纸质合同附件</div>
                            ${paperContractHtml}
                            <div style="text-align: right; margin-top: 15px;">
                                <button class="btn btn-outline" style="margin-right: 10px; font-size:12px; padding: 5px 10px;" onclick="openPaperContractUpload('training', '${order.id}')">
                                    <i class="fas fa-upload"></i> 上传纸质合同
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                ${paymentInfoHtml}
                
                <!-- 审批流程区域 -->
                <div class="section">
                    <div class="section-title">
                        <i class="fas fa-gavel" style="color: #e74c3c; margin-right: 8px;"></i>
                        审批流程
                    </div>
                    <ul class="timeline">
                        ${approvalHistoryHtml}
                    </ul>
                    ${approvalActionsHtml}
                </div>
                
                <!-- 操作日志区域 -->
                <div class="section">
                    <div class="section-title">
                        <i class="fas fa-history" style="color: #95a5a6; margin-right: 8px;"></i>
                        操作日志
                    </div>
                    <div style="text-align: right; margin-bottom: 15px;">
                        <button class="btn btn-outline" style="font-size:12px; padding: 5px 10px;" onclick="openOperationLog('training', '${order.id}')">
                            <i class="fas fa-list"></i> 查看完整日志
                        </button>
                    </div>
                    <ul class="timeline">
                        ${order.operationLogs && order.operationLogs.length > 0 
                            ? order.operationLogs.slice(0, 3).map(log => `
                                <li class="timeline-item">
                                    <div class="timeline-time">${log.time}</div>
                                    <div class="timeline-content">
                                        <strong>${log.operator}</strong> ${log.comment}
                                    </div>
                                </li>
                            `).join('')
                            : '<li>暂无操作记录</li>'
                        }
                    </ul>
                </div>
            `;
            
            document.getElementById('training-detail-drawer').classList.add('open');
            document.querySelector('.overlay').classList.add('active');
        }

        function closeTrainingDetailDrawer() {
            document.getElementById('training-detail-drawer').classList.remove('open');
            if (!document.querySelector('.drawer.open')) {
                document.querySelector('.overlay').classList.remove('active');
            }
        }
        
        function switchContractTab(tabType) {
            // 更新标签页状态
            document.querySelectorAll('.contract-tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            // 显示对应内容
            document.querySelectorAll('.contract-content').forEach(content => content.style.display = 'none');
            if (tabType === 'electronic') {
                document.getElementById('electronic-contract-content').style.display = 'block';
            } else {
                document.getElementById('paper-contract-content').style.display = 'block';
            }
        }
        
        function openPaperContractUpload(orderType, orderId) {
            // 打开纸质合同上传弹窗
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 500px;">
                    <div class="modal-header">
                        <h3>上传纸质合同</h3>
                        <button onclick="this.closest('.modal').remove()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="paper-contract-form">
                            <div class="form-group">
                                <label>合同附件 <span style="color:red">*</span></label>
                                <input type="file" class="form-control" name="contractFile" accept=".pdf,.doc,.docx,.jpg,.png" required>
                                <small style="color: var(--gray);">支持格式：PDF、DOC、DOCX、JPG、PNG，文件大小不超过10MB</small>
                            </div>
                            <div class="form-group">
                                <label>合同名称 <span style="color:red">*</span></label>
                                <input type="text" class="form-control" name="contractName" required>
                            </div>
                            <div class="form-group">
                                <label>合同编号 <span style="color:red">*</span></label>
                                <input type="text" class="form-control" name="contractNumber" required>
                            </div>
                            <div class="form-group">
                                <label>签署日期 <span style="color:red">*</span></label>
                                <input type="date" class="form-control" name="signDate" required>
                            </div>
                            <div class="form-group">
                                <label>合同金额 <span style="color:red">*</span></label>
                                <input type="number" class="form-control" name="contractAmount" min="0" required>
                            </div>
                            <div class="form-group">
                                <label>备注</label>
                                <textarea class="form-control" name="remarks" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-outline" onclick="this.closest('.modal').remove()">取消</button>
                        <button class="btn btn-primary" onclick="submitPaperContract('${orderType}', '${orderId}')">提交审核</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            document.querySelector('.overlay').classList.add('active');
        }
        
        function submitPaperContract(orderType, orderId) {
            const form = document.getElementById('paper-contract-form');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }
            
            const formData = new FormData(form);
            const contractData = {
                contractName: formData.get('contractName'),
                contractNumber: formData.get('contractNumber'),
                signDate: formData.get('signDate'),
                contractAmount: formData.get('contractAmount'),
                remarks: formData.get('remarks'),
                fileName: formData.get('contractFile').name
            };
            
            // 更新订单数据
            let orderData, orderIndex;
            if (orderType === 'training') {
                orderData = trainingOrdersData;
                orderIndex = trainingOrdersData.findIndex(o => o.id === orderId);
            }
            
            if (orderIndex !== -1) {
                const order = orderData[orderIndex];
                order.paperContractFile = contractData.fileName;
                order.paperContractInfo = contractData;
                order.status = ORDER_STATUS.APPROVING;
                
                // 记录操作日志
                addOperationLog(orderType, orderId, 'upload_contract', [
                    { field: '纸质合同', oldValue: '无', newValue: contractData.contractName }
                ], '上传纸质合同并提交审核');
                
                // 关闭弹窗并刷新详情页
                document.querySelector('.modal').remove();
                document.querySelector('.overlay').classList.remove('active');
                openTrainingDetailDrawer(orderId);
                renderTrainingOrders();
                
                alert('纸质合同上传成功，已提交审核！');
            }
        }
        
        function filterTrainingOrders() {
            const statusFilter = document.getElementById('training-status-filter')?.value || '';
            const paymentFilter = document.getElementById('training-payment-filter')?.value || '';
            const searchText = document.getElementById('training-search')?.value.toLowerCase() || '';
            
            const filteredOrders = trainingOrdersData.filter(order => {
                const statusMatch = !statusFilter || order.status === statusFilter;
                const paymentMatch = !paymentFilter || order.paymentStatus === paymentFilter;
                const searchMatch = !searchText || 
                    order.enterpriseName.toLowerCase().includes(searchText) ||
                    order.trainingProjectName.toLowerCase().includes(searchText) ||
                    order.id.toLowerCase().includes(searchText);
                
                return statusMatch && paymentMatch && searchMatch;
            });
            
            renderFilteredTrainingOrders(filteredOrders);
        }
        
        function filterPersonalOrders() {
            const statusFilter = document.getElementById('personal-status-filter')?.value || '';
            const paymentFilter = document.getElementById('personal-payment-filter')?.value || '';
            const typeFilter = document.getElementById('personal-type-filter')?.value || '';
            const searchText = document.getElementById('personal-search')?.value.toLowerCase() || '';
            
            const filteredOrders = personalOrdersData.filter(order => {
                const statusMatch = !statusFilter || order.status === statusFilter;
                const paymentMatch = !paymentFilter || order.paymentStatus === paymentFilter;
                const typeMatch = !typeFilter || order.orderType === typeFilter;
                const searchMatch = !searchText || 
                    order.studentName.toLowerCase().includes(searchText) ||
                    order.courseName.toLowerCase().includes(searchText) ||
                    order.id.toLowerCase().includes(searchText);
                
                return statusMatch && paymentMatch && typeMatch && searchMatch;
            });
            
            renderFilteredPersonalOrders(filteredOrders);
        }
        
        function renderFilteredPersonalOrders(filteredOrders) {
            const tbody = document.getElementById('personal-orders-tbody');
            if (!tbody) return;
            
            tbody.innerHTML = '';
            
            if (filteredOrders.length === 0) {
                tbody.innerHTML = `<tr><td colspan="11" style="text-align:center; padding: 20px;">暂无符合条件的订单</td></tr>`;
                return;
            }
            
            filteredOrders.forEach(order => {
                const statusClass = statusClassMap[order.status] || 'status-pending';
                const paymentStatusClass = paymentStatusClassMap[order.paymentStatus] || 'status-pending';
                const learningStatusClass = order.learningStatus === 'pending' ? 'status-pending' : 
                                          order.learningStatus === 'processing' ? 'status-processing' : 'status-completed';
                
                const rowHtml = `
                <tr data-id="${order.id}">
                    <td><span class="order-id" style="cursor:default;">${order.id}</span></td>
                    <td>${order.studentName}</td>
                    <td><span class="status-badge ${order.orderType === '个人培训' ? 'status-processing' : 'status-warning'}">${order.orderType}</span></td>
                    <td>${order.courseName}</td>
                    <td>${order.orderSource || '-'}</td>
                    <td>¥${Number(order.amount).toLocaleString()}</td>
                    <td><span class="status-badge ${statusClass}">${order.status}</span></td>
                    <td><span class="status-badge ${paymentStatusClass}">${order.paymentStatus}</span></td>
                    <td><span class="status-badge ${learningStatusClass}">${order.learningStatusText || order.learningStatus}</span></td>
                    <td>${order.registerTime || order.signupDate || '-'}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-action btn-view" data-id="${order.id}">查看</button>
                            <button class="btn-action btn-edit" data-id="${order.id}">编辑</button>
                            <button class="btn-action btn-delete" data-id="${order.id}">删除</button>
                            <button class="btn-action" style="background: var(--gray); color: white;" onclick="openOperationLog('personal', '${order.id}')">操作日志</button>
                        </div>
                    </td>
                </tr>`;
                tbody.insertAdjacentHTML('beforeend', rowHtml);
            });
        }
        
        function renderFilteredTrainingOrders(filteredOrders) {
            const tbody = document.getElementById('training-orders-tbody');
            if (!tbody) return;
            
            tbody.innerHTML = '';
            
            if (filteredOrders.length === 0) {
                tbody.innerHTML = `<tr><td colspan="10" style="text-align:center; padding: 20px;">暂无符合条件的订单</td></tr>`;
                return;
            }
            
            filteredOrders.forEach(order => {
                const statusClass = statusClassMap[order.status] || 'status-pending';
                const paymentStatusClass = paymentStatusClassMap[order.paymentStatus] || 'status-pending';
                const rowHtml = `
                <tr data-id="${order.id}">
                    <td><span class="order-id" style="cursor:default;">${order.id}</span></td>
                    <td>${order.enterpriseName}</td>
                    <td>${order.trainingProjectName}</td>
                    <td>${order.participantsCount}人</td>
                    <td>${order.period || '-'}</td>
                    <td>¥${Number(order.amount).toLocaleString()}</td>
                    <td><span class="status-badge ${statusClass}">${order.status}</span></td>
                    <td><span class="status-badge ${paymentStatusClass}">${order.paymentStatus}</span></td>
                    <td>${order.createTime}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-action btn-view" data-id="${order.id}">查看</button>
                            <button class="btn-action btn-edit" data-id="${order.id}">编辑</button>
                            <button class="btn-action btn-delete" data-id="${order.id}">删除</button>
                            <button class="btn-action" style="background: var(--gray); color: white;" onclick="openOperationLog('training', '${order.id}')">操作日志</button>
                        </div>
                    </td>
                </tr>`;
                tbody.insertAdjacentHTML('beforeend', rowHtml);
            });
        }
        
        function filterDomesticOrders() {
            const statusFilter = document.getElementById('domestic-status-filter')?.value || '';
            const paymentFilter = document.getElementById('domestic-payment-filter')?.value || '';
            const typeFilter = document.getElementById('domestic-type-filter')?.value || '';
            const searchText = document.getElementById('domestic-search')?.value.toLowerCase() || '';
            
            const filteredOrders = domesticOrdersData.filter(order => {
                const statusMatch = !statusFilter || order.status === statusFilter;
                const paymentMatch = !paymentFilter || order.paymentStatus === paymentFilter;
                const typeMatch = !typeFilter || order.serviceType === typeFilter;
                const searchMatch = !searchText || 
                    order.customerName.toLowerCase().includes(searchText) ||
                    order.servicePerson.toLowerCase().includes(searchText) ||
                    (order.serviceAgency && order.serviceAgency.toLowerCase().includes(searchText));
                
                return statusMatch && paymentMatch && typeMatch && searchMatch;
            });
            
            renderFilteredDomesticOrders(filteredOrders);
        }
        
        function renderFilteredDomesticOrders(filteredOrders) {
            const tbody = document.getElementById('domestic-orders-tbody');
            if (!tbody) return;
            
            tbody.innerHTML = '';
            
            if (filteredOrders.length === 0) {
                tbody.innerHTML = `<tr><td colspan="11" style="text-align:center; padding: 20px;">暂无符合条件的家政服务订单</td></tr>`;
                return;
            }
            
            filteredOrders.forEach(order => {
                const statusClass = statusClassMap[order.status] || 'status-pending';
                const paymentStatusClass = paymentStatusClassMap[order.paymentStatus] || 'status-pending';
                const serviceAgency = order.assignedProvider && order.assignedProvider.type === 'agency' 
                    ? order.assignedProvider.name 
                    : (order.serviceAgency || '-');
                
                // 任务进度显示
                const taskProgress = order.taskProgress || { completed: 0, total: 0 };
                const progressText = `${taskProgress.completed}/${taskProgress.total}`;
                const progressPercentage = taskProgress.total > 0 ? (taskProgress.completed / taskProgress.total * 100) : 0;
                
                const rowHtml = `
                <tr data-id="${order.id}">
                    <td><span class="order-id" style="cursor:default;">${order.id}</span></td>
                    <td>
                        <div style="font-weight: 500;">${order.customerName}</div>
                        <div style="font-size: 12px; color: var(--gray);">${order.customerPhone}</div>
                    </td>
                    <td>${order.serviceType}</td>
                    <td>${order.assignedProvider ? `${order.assignedProvider.name} (${order.assignedProvider.type === 'individual' ? '个人阿姨' : '服务机构'})` : order.servicePerson}</td>
                    <td>${serviceAgency}</td>
                    <td>¥${Number(order.amount).toLocaleString()}</td>
                    <td>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span style="font-weight: 500; color: var(--primary);">${progressText}</span>
                            <div style="flex: 1; height: 6px; background: var(--light-gray); border-radius: 3px; overflow: hidden;">
                                <div style="width: ${progressPercentage}%; height: 100%; background: var(--primary); border-radius: 3px; transition: width 0.3s;"></div>
                            </div>
                        </div>
                    </td>
                    <td><span class="status-badge ${statusClass}">${order.status}</span></td>
                    <td><span class="status-badge ${paymentStatusClass}">${order.paymentStatus}</span></td>
                    <td>${new Date(order.appointmentTime).toLocaleString('zh-CN')}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-action btn-view" data-id="${order.id}">查看</button>
                            <button class="btn-action btn-edit" data-id="${order.id}">编辑</button>
                            <button class="btn-action btn-delete" data-id="${order.id}">删除</button>
                            <button class="btn-action" style="background: var(--gray); color: white;" onclick="openOperationLog('domestic', '${order.id}')">操作日志</button>
                        </div>
                    </td>
                </tr>`;
                tbody.insertAdjacentHTML('beforeend', rowHtml);
            });
        }
        
        function editTrainingFromDetailView() {
            if (currentTrainingOrderId) {
                closeTrainingDetailDrawer();
                openTrainingDrawer('edit', currentTrainingOrderId);
            }
        }

        function deleteTrainingOrder(orderId) {
            if (confirm('您确定要删除此培训订单吗？此操作不可恢复。')) {
                const order = trainingOrdersData.find(o => o.id === orderId);
                if (order) {
                    // 记录删除日志
                    addOperationLog('training', orderId, 'delete', [], '删除企业培训订单');
                    
                const index = trainingOrdersData.findIndex(o => o.id === orderId);
                if (index !== -1) {
                    trainingOrdersData.splice(index, 1);
                    renderTrainingOrders();
                    }
                }
            }
        }

        function approveTrainingOrder(orderId) {
            const order = trainingOrdersData.find(o => o.id === orderId);
            if(order) {
                const oldStatus = order.status;
                const oldPaymentStatus = order.paymentStatus;
                
                // 根据文档要求，审批通过后进入待支付状态
                order.status = ORDER_STATUS.PENDING_PAYMENT;
                order.paymentStatus = PAYMENT_STATUS.PENDING;
                
                order.approvalHistory.push({
                    action: '批准',
                    user: '张三 (管理员)',
                    time: new Date().toLocaleString('zh-CN'),
                    comments: '合同审批通过，订单进入待支付状态。'
                });
                
                // 记录操作日志
                addOperationLog('training', orderId, 'approve', [
                    { field: '订单状态', oldValue: oldStatus, newValue: ORDER_STATUS.PENDING_PAYMENT },
                    { field: '支付状态', oldValue: oldPaymentStatus, newValue: PAYMENT_STATUS.PENDING }
                ], '合同审批通过');
                
                closeTrainingDetailDrawer();
                renderTrainingOrders();
                alert('审批通过！订单已进入待支付状态。');
            }
        }

        function rejectTrainingOrder(orderId) {
            const reason = prompt('请输入驳回原因：');
            if (reason) {
                const order = trainingOrdersData.find(o => o.id === orderId);
                if(order) {
                    const oldStatus = order.status;
                    const oldPaymentStatus = order.paymentStatus;
                    
                    order.status = ORDER_STATUS.REJECTED;
                    order.paymentStatus = PAYMENT_STATUS.CANCELLED;
                    
                    order.approvalHistory.push({
                        action: '驳回',
                        user: '张三 (管理员)',
                        time: new Date().toLocaleString('zh-CN'),
                        comments: reason
                    });
                    
                    // 记录操作日志
                    addOperationLog('training', orderId, 'reject', [
                        { field: '订单状态', oldValue: oldStatus, newValue: ORDER_STATUS.REJECTED },
                        { field: '支付状态', oldValue: oldPaymentStatus, newValue: PAYMENT_STATUS.CANCELLED }
                    ], `驳回原因：${reason}`);
                    
                    closeTrainingDetailDrawer();
                    renderTrainingOrders();
                    alert('订单已驳回！');
                }
            }
        }

        // --- 个人培训与认证订单相关功能 ---
        function renderPersonalOrders() {
            console.log("DEBUG: renderPersonalOrders called.");
            const tbody = document.getElementById('personal-orders-tbody');
            if (!tbody) {
                console.error("DEBUG: personal-orders-tbody container not found!");
                return;
            }
            tbody.innerHTML = '';

            if (personalOrdersData.length === 0) {
                tbody.innerHTML = `<tr><td colspan="11" style="text-align:center; padding: 20px;">暂无个人培训或认证订单</td></tr>`;
                return;
            }

            personalOrdersData.forEach(order => {
                const statusClass = statusClassMap[order.status] || 'status-pending';
                const paymentStatusClass = paymentStatusClassMap[order.paymentStatus] || 'status-pending';
                const learningStatusClass = order.learningStatus === 'pending' ? 'status-pending' : 
                                          order.learningStatus === 'processing' ? 'status-processing' : 'status-completed';
                
                const rowHtml = `
                <tr data-id="${order.id}">
                    <td><span class="order-id" style="cursor:default;">${order.id}</span></td>
                    <td>${order.studentName}</td>
                    <td><span class="status-badge ${order.orderType === '个人培训' ? 'status-processing' : 'status-warning'}">${order.orderType}</span></td>
                    <td>${order.courseName}</td>
                    <td>${order.orderSource || '-'}</td>
                    <td>¥${Number(order.amount).toLocaleString()}</td>
                    <td><span class="status-badge ${statusClass}">${order.status}</span></td>
                    <td><span class="status-badge ${paymentStatusClass}">${order.paymentStatus}</span></td>
                    <td><span class="status-badge ${learningStatusClass}">${order.learningStatusText || order.learningStatus}</span></td>
                    <td>${order.registerTime || order.signupDate || '-'}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-action btn-view" data-id="${order.id}">查看</button>
                            <button class="btn-action btn-edit" data-id="${order.id}">编辑</button>
                            <button class="btn-action btn-delete" data-id="${order.id}">删除</button>
                            <button class="btn-action" style="background: var(--gray); color: white;" onclick="openOperationLog('personal', '${order.id}')">操作日志</button>
                        </div>
                    </td>
                </tr>`;
                tbody.insertAdjacentHTML('beforeend', rowHtml);
            });
        }
        
        function openPersonalDrawer(mode = 'new', orderId = null) {
            const drawer = document.getElementById('personal-order-drawer');
            const title = document.getElementById('personal-drawer-title');
            const form = document.getElementById('personal-order-form');
            
            form.reset();
            currentPersonalOrderId = null;

            if (mode === 'edit' && orderId) {
                title.textContent = '编辑个人培训订单';
                const order = personalOrdersData.find(o => o.id === orderId);
                if (order) {
                    currentPersonalOrderId = orderId;
                    form.orderType.value = order.orderType;
                    form.studentName.value = order.studentName;
                    form.courseName.value = order.courseName;
                    form.orderSource.value = order.orderSource || '';
                    form.amount.value = order.amount;
                    form.paymentStatus.value = order.paymentStatus;
                    form.learningStatus.value = order.learningStatus;
                    form.relatedOpportunity.value = order.relatedOpportunity || '';
                    form.relatedLead.value = order.relatedLead || '';
                    
                    // 填充支付信息
                    if (order.paymentInfo) {
                        form.actualAmount.value = order.paymentInfo.actualAmount || '';
                        form.paymentMethod.value = order.paymentInfo.paymentMethod || '';
                        form.paymentDate.value = order.paymentInfo.paymentDate || '';
                        form.operator.value = order.paymentInfo.operator || '';
                        form.remarks.value = order.paymentInfo.remarks || '';
                    }
                    
                    // 触发支付状态变化事件，显示/隐藏支付信息区域
                    togglePaymentInfo('personal');
                    
                    // 填充合同信息
                    if (order.contractType) {
                        form.contractType.value = order.contractType;
                        // 触发合同类型切换
                        const contractTypeEvent = new Event('change');
                        form.contractType.dispatchEvent(contractTypeEvent);
                    }
                    form.contractTemplate.value = order.contractTemplate || '';
                    form.contractNumber.value = order.contractNumber || '';
                    form.contractName.value = order.contractName || '';
                    form.contractSignDate.value = order.contractSignDate || '';
                    form.contractAmount.value = order.contractAmount || '';
                    
                    // 显示已上传的文件名
                    if (order.paperContract) {
                        document.getElementById('personal-paper-contract-filename').textContent = order.paperContract;
                    }
                }
            } else {
                title.textContent = '新建个人培训订单';
            }

            drawer.classList.add('open');
            document.querySelector('.overlay').classList.add('active');
        }

        function closePersonalDrawer() {
            document.getElementById('personal-order-drawer').classList.remove('open');
            if (!document.querySelector('.drawer.open')) {
                document.querySelector('.overlay').classList.remove('active');
            }
        }

        function submitPersonalOrder() {
            const form = document.getElementById('personal-order-form');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const orderData = {
                orderType: form.orderType.value,
                studentName: form.studentName.value,
                courseName: form.courseName.value,
                orderSource: form.orderSource.value,
                amount: form.amount.value,
                paymentStatus: form.paymentStatus.value,
                learningStatus: form.learningStatus.value,
                relatedOpportunity: form.relatedOpportunity.value,
                relatedLead: form.relatedLead.value,
                // 合同信息
                contractType: form.contractType.value,
                contractTemplate: form.contractTemplate.value,
                contractNumber: form.contractNumber.value,
                contractName: form.contractName.value,
                contractSignDate: form.contractSignDate.value,
                contractAmount: form.contractAmount.value,
                paperContract: form.paperContract.files[0]?.name || null,
                // 支付信息
                paymentInfo: form.paymentStatus.value === 'completed' ? {
                    actualAmount: form.actualAmount.value,
                    paymentMethod: form.paymentMethod.value,
                    paymentDate: form.paymentDate.value,
                    operator: form.operator.value,
                    remarks: form.remarks.value
                } : null
            };
            
            if (currentPersonalOrderId) {
                // 编辑模式 - 记录变更
                const index = personalOrdersData.findIndex(o => o.id === currentPersonalOrderId);
                if (index !== -1) {
                    const oldData = personalOrdersData[index];
                    
                    // 比较变更
                    const fieldMap = {
                        orderType: '订单类型',
                        studentName: '学员姓名',
                        courseName: '课程名称',
                        orderSource: '订单来源',
                        amount: '订单金额',
                        paymentStatus: '支付状态',
                        learningStatus: '学习状态'
                    };
                    
                    const changes = compareObjects(oldData, orderData, fieldMap);
                    
                    personalOrdersData[index] = { ...oldData, ...orderData };
                    
                    // 记录操作日志
                    if (changes.length > 0) {
                        addOperationLog('personal', currentPersonalOrderId, 'edit', changes, '编辑订单信息');
                    }
                }
            } else {
                // 新建模式
                const typePrefix = orderData.orderType === '个人培训' ? 'PT' : 'CE';
                orderData.id = typePrefix + new Date().getTime();
                orderData.signupDate = new Date().toISOString().split('T')[0];
                orderData.status = ORDER_STATUS.PENDING_APPROVAL;
                orderData.approvalHistory = [];
                orderData.operationLogs = [];
                personalOrdersData.unshift(orderData);
                
                // 记录创建日志
                addOperationLog('personal', orderData.id, 'create', [], '创建个人培训订单');
            }
            
            renderPersonalOrders();
            closePersonalDrawer();
        }

        function openPersonalDetailDrawer(orderId) {
            currentPersonalOrderId = orderId;
            const order = personalOrdersData.find(p => p.id === orderId);
            if (!order) return;
            
            const detailBody = document.getElementById('personal-detail-body');
            const paymentStatusText = { pending: '待支付', completed: '已支付', refunded: '已退款' }[order.paymentStatus];
            const learningStatusText = { pending: '未开始', processing: '学习中 / 待考试', completed: '已完成 / 已通过' }[order.learningStatus];

            const signatureStatusMap = {
                unsigned: { text: '未签署', badge: 'status-unsigned' },
                pending: { text: '待签署', badge: 'status-pending' },
                signed: { text: '已签署', badge: 'status-completed' }
            };
            const signatureInfo = signatureStatusMap[order.signature] || signatureStatusMap['unsigned'];

            const signatureHtml = `
            <div class="section">
                <div class="section-title">电子协议</div>
                <div class="signature-item">
                    <div class="signature-party">
                        <i class="fas fa-file-signature"></i> 个人培训/认证协议
                    </div>
                    <div class="signature-status">
                        <span class="status-badge ${signatureInfo.badge}">${signatureInfo.text}</span>
                    </div>
                </div>
                <div style="text-align: right; margin-top: 15px;">
                     ${order.signature === 'signed' ? `<button class="btn btn-outline" style="font-size:12px; padding: 5px 10px;">下载协议</button>` : `<button class="btn btn-primary" style="font-size:12px; padding: 5px 10px;">发起签约</button>`}
                </div>
            </div>
            `;

            // 审批历史记录
            const approvalHistoryHtml = order.approvalHistory && order.approvalHistory.length > 0
                ? order.approvalHistory.map(item => `
                    <li class="timeline-item">
                        <div class="timeline-time">${item.time}</div>
                        <div class="timeline-content">
                            <strong>${item.user}</strong> ${item.action === '批准' ? '批准了订单' : item.action === '拒绝' ? '拒绝了订单' : '发起了审批'}。
                            ${item.comments ? `<div style="margin-top:5px; font-style: italic;">备注: ${item.comments}</div>` : ''}
                        </div>
                    </li>
                `).join('')
                : '<li>无审批记录</li>';
            
            // 审批和收款操作按钮
            let approvalActionsHtml = '';
            if (order.status === ORDER_STATUS.PENDING_APPROVAL) {
                approvalActionsHtml = `
                    <div style="text-align: right; margin-top: 15px;">
                        <button class="btn btn-primary" style="font-size:12px; padding: 5px 10px;" onclick="initiateApproval('personal', '${order.id}')">
                            <i class="fas fa-gavel"></i> 发起审批
                        </button>
                    </div>
                `;
            } else if (order.status === ORDER_STATUS.APPROVING) {
                approvalActionsHtml = `
                    <div style="text-align: right; margin-top: 15px;">
                        <button class="btn btn-success" style="margin-right: 10px; background:var(--success); color:white; font-size:12px; padding: 5px 10px;" onclick="approveOrder('personal', '${order.id}')">
                            <i class="fas fa-check"></i> 同意
                        </button>
                        <button class="btn btn-danger" style="background:var(--danger); color:white; font-size:12px; padding: 5px 10px;" onclick="rejectOrder('personal', '${order.id}')">
                            <i class="fas fa-times"></i> 拒绝
                        </button>
                    </div>
                `;
            } else if (order.status === ORDER_STATUS.PENDING_PAYMENT) {
                approvalActionsHtml = `
                    <div style="text-align: right; margin-top: 15px;">
                        <button class="btn btn-success" style="background:var(--success); color:white; font-size:12px; padding: 5px 10px;" onclick="openPaymentModal('personal', '${order.id}')">
                            <i class="fas fa-money-bill-wave"></i> 确认收款
                        </button>
                    </div>
                `;
            }

            // 收款信息显示
            const paymentInfoHtml = order.paymentInfo ? `
                <div class="section">
                    <div class="section-title">
                        <i class="fas fa-money-bill-wave" style="color: #27ae60; margin-right: 8px;"></i>
                        收款信息
                    </div>
                    <div class="project-card payment-info-card" style="box-shadow:none; border: 1px solid #27ae60;">
                        <div class="project-meta">
                            <div class="meta-item"><div class="meta-label">收款金额</div><div class="meta-value" style="color: #27ae60; font-weight: bold;">¥${Number(order.paymentInfo.actualAmount).toLocaleString()}</div></div>
                            <div class="meta-item"><div class="meta-label">收款方式</div><div class="meta-value">${getPaymentMethodText(order.paymentInfo.paymentMethod)}</div></div>
                            <div class="meta-item"><div class="meta-label">收款日期</div><div class="meta-value">${order.paymentInfo.paymentDate}</div></div>
                            <div class="meta-item"><div class="meta-label">操作人</div><div class="meta-value">${order.paymentInfo.operator}</div></div>
                            ${order.paymentInfo.remarks ? `<div class="meta-item" style="grid-column: 1 / -1;"><div class="meta-label">收款备注</div><div class="meta-value">${order.paymentInfo.remarks}</div></div>` : ''}
                        </div>
                    </div>
                </div>
            ` : '';

            detailBody.innerHTML = `
                <div class="project-card" style="box-shadow:none; border: 1px solid var(--border);">
                    <div class="project-header">
                        <div class="project-title">${order.courseName}</div>
                        <div class="project-status status-badge ${order.orderType === '个人培训' ? 'status-processing' : 'status-warning'}">${order.orderType}</div>
                    </div>
                    <div class="project-meta">
                        <div class="meta-item"><div class="meta-label">订单号</div><div class="meta-value">${order.id}</div></div>
                        <div class="meta-item"><div class="meta-label">学员姓名</div><div class="meta-value">${order.studentName}</div></div>
                        <div class="meta-item"><div class="meta-label">课程/考试项目</div><div class="meta-value">${order.courseName}</div></div>
                        <div class="meta-item"><div class="meta-label">订单来源</div><div class="meta-value">${order.orderSource || '-'}</div></div>
                        <div class="meta-item"><div class="meta-label">订单金额</div><div class="meta-value">¥${Number(order.amount).toLocaleString()}</div></div>
                        <div class="meta-item"><div class="meta-label">订单状态</div><div class="meta-value"><span class="status-badge ${statusClassMap[order.status] || 'status-pending'}">${order.status}</span></div></div>
                        <div class="meta-item"><div class="meta-label">支付状态</div><div class="meta-value"><span class="status-badge ${paymentStatusClassMap[order.paymentStatus] || 'status-pending'}">${order.paymentStatus}</span></div></div>
                        <div class="meta-item"><div class="meta-label">学习/考试状态</div><div class="meta-value"><span class="status-badge ${order.learningStatus === 'pending' ? 'status-pending' : order.learningStatus === 'processing' ? 'status-processing' : 'status-completed'}">${order.learningStatusText || order.learningStatus}</span></div></div>
                        <div class="meta-item"><div class="meta-label">报名时间</div><div class="meta-value">${order.registerTime || order.signupDate || '-'}</div></div>
                        ${order.relatedOpportunity ? `<div class="meta-item"><div class="meta-label">关联商机</div><div class="meta-value">${order.relatedOpportunity}</div></div>` : ''}
                        ${order.relatedLead ? `<div class="meta-item"><div class="meta-label">关联线索</div><div class="meta-value">${order.relatedLead}</div></div>` : ''}
                    </div>
                </div>
                
                ${paymentInfoHtml}
                
                ${signatureHtml}
                
                <!-- 审批流程区域 -->
                <div class="section">
                    <div class="section-title">
                        <i class="fas fa-gavel" style="color: #e74c3c; margin-right: 8px;"></i>
                        审批流程
                    </div>
                    <ul class="timeline">
                        ${approvalHistoryHtml}
                    </ul>
                    ${approvalActionsHtml}
                </div>
                
                <!-- 操作日志区域 -->
                <div class="section">
                    <div class="section-title">
                        <i class="fas fa-history" style="color: #95a5a6; margin-right: 8px;"></i>
                        操作日志
                    </div>
                    <div style="text-align: right; margin-bottom: 15px;">
                        <button class="btn btn-outline" style="font-size:12px; padding: 5px 10px;" onclick="openOperationLog('personal', '${order.id}')">
                            <i class="fas fa-list"></i> 查看完整日志
                        </button>
                    </div>
                    <ul class="timeline">
                        ${order.operationLogs && order.operationLogs.length > 0 
                            ? order.operationLogs.slice(0, 3).map(log => `
                                <li class="timeline-item">
                                    <div class="timeline-time">${log.time}</div>
                                    <div class="timeline-content">
                                        <strong>${log.operator}</strong> ${log.comment}
                                    </div>
                                </li>
                            `).join('')
                            : '<li>暂无操作记录</li>'
                        }
                    </ul>
                </div>
            `;
            document.getElementById('personal-detail-drawer').classList.add('open');
            document.querySelector('.overlay').classList.add('active');
        }

        function closePersonalDetailDrawer() {
            document.getElementById('personal-detail-drawer').classList.remove('open');
            if (!document.querySelector('.drawer.open')) {
                document.querySelector('.overlay').classList.remove('active');
            }
        }

        function deletePersonalOrder(orderId) {
            if (confirm('您确定要删除此订单吗？此操作不可恢复。')) {
                const order = personalOrdersData.find(o => o.id === orderId);
                if (order) {
                    // 记录删除日志
                    addOperationLog('personal', orderId, 'delete', [], '删除个人培训订单');
                    
                const index = personalOrdersData.findIndex(o => o.id === orderId);
                if (index !== -1) {
                    personalOrdersData.splice(index, 1);
                    renderPersonalOrders();
                    }
                }
            }
        }

        // --- 家政服务订单 ---
        function renderDomesticOrders() {
            const tbody = document.getElementById('domestic-orders-tbody');
            tbody.innerHTML = '';
            if (domesticOrdersData.length === 0) {
                tbody.innerHTML = `<tr><td colspan="11" style="text-align:center; padding: 20px;">暂无家政服务订单</td></tr>`;
                return;
            }
            domesticOrdersData.forEach(order => {
                const statusClass = statusClassMap[order.status] || 'status-pending';
                const paymentStatusClass = paymentStatusClassMap[order.paymentStatus] || 'status-pending';
                const serviceAgency = order.assignedProvider && order.assignedProvider.type === 'agency' 
                    ? order.assignedProvider.name 
                    : (order.serviceAgency || '-');
                
                // 任务进度显示
                const taskProgress = order.taskProgress || { completed: 0, total: 0 };
                const progressText = `${taskProgress.completed}/${taskProgress.total}`;
                const progressPercentage = taskProgress.total > 0 ? (taskProgress.completed / taskProgress.total * 100) : 0;
                
                tbody.innerHTML += `
                <tr data-id="${order.id}">
                    <td><span class="order-id" style="cursor:default;">${order.id}</span></td>
                    <td>
                        <div style="font-weight: 500;">${order.customerName}</div>
                        <div style="font-size: 12px; color: var(--gray);">${order.customerPhone}</div>
                    </td>
                    <td>${order.serviceType}</td>
                    <td>${order.assignedProvider ? `${order.assignedProvider.name} (${order.assignedProvider.type === 'individual' ? '个人阿姨' : '服务机构'})` : order.servicePerson}</td>
                    <td>${serviceAgency}</td>
                    <td>¥${Number(order.amount).toLocaleString()}</td>
                    <td>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span style="font-weight: 500; color: var(--primary);">${progressText}</span>
                            <div style="flex: 1; height: 6px; background: var(--light-gray); border-radius: 3px; overflow: hidden;">
                                <div style="width: ${progressPercentage}%; height: 100%; background: var(--primary); border-radius: 3px; transition: width 0.3s;"></div>
                            </div>
                        </div>
                    </td>
                    <td><span class="status-badge ${statusClass}">${order.status}</span></td>
                    <td><span class="status-badge ${paymentStatusClass}">${order.paymentStatus}</span></td>
                    <td>${new Date(order.appointmentTime).toLocaleString('zh-CN')}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-action btn-view" data-id="${order.id}">查看</button>
                            <button class="btn-action btn-edit" data-id="${order.id}">编辑</button>
                            <button class="btn-action btn-delete" data-id="${order.id}">删除</button>
                            <button class="btn-action" style="background: var(--gray); color: white;" onclick="openOperationLog('domestic', '${order.id}')">操作日志</button>
                        </div>
                    </td>
                </tr>`;
            });
        }
        function openDomesticDrawer(mode = 'new', orderId = null) {
            console.log('DEBUG: openDomesticDrawer called with mode:', mode, 'orderId:', orderId);
            
            const drawer = document.getElementById('domestic-order-drawer');
            const form = document.getElementById('domestic-order-form');
            
            if (!drawer) {
                console.error('DEBUG: domestic-order-drawer not found!');
                return;
            }
            
            if (!form) {
                console.error('DEBUG: domestic-order-form not found!');
                return;
            }
            
            form.reset();
            currentDomesticOrderId = null;
             document.getElementById('domestic-paper-contract-filename').textContent = '';

            if (mode === 'edit' && orderId) {
                console.log('DEBUG: Editing order:', orderId);
                document.getElementById('domestic-drawer-title').textContent = '编辑家政服务订单';
                const order = domesticOrdersData.find(o => o.id === orderId);
                if (order) {
                    currentDomesticOrderId = orderId;
                    form.customerName.value = order.customerName;
                    form.customerPhone.value = order.customerPhone;
                    form.serviceAddress.value = order.serviceAddress;
                    form.serviceType.value = order.serviceType;
                    form.appointmentTime.value = order.appointmentTime;
                    form.servicePerson.value = order.servicePerson;
                    form.serviceAgency.value = order.serviceAgency || '';
                    form.amount.value = order.amount;
                    form.paymentStatus.value = order.paymentStatus || '未支付';
                    form.relatedOpportunity.value = order.relatedOpportunity || '';
                    form.relatedLead.value = order.relatedLead || '';
                    
                    // 填充支付信息
                    if (order.paymentInfo) {
                        form.actualAmount.value = order.paymentInfo.actualAmount || '';
                        form.paymentMethod.value = order.paymentInfo.paymentMethod || '';
                        // 转换日期格式为datetime-local格式
                        if (order.paymentInfo.paymentDate) {
                            let dateStr = order.paymentInfo.paymentDate;
                            // 如果是'2024-05-30'这种格式，补全为'2024-05-30T00:00'
                            if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                                dateStr += 'T00:00';
                            }
                            form.paymentDate.value = dateStr;
                        } else {
                            form.paymentDate.value = '';
                        }
                        form.operator.value = order.paymentInfo.operator || '';
                        form.remarks.value = order.paymentInfo.remarks || '';
                    }
                    
                    // 触发支付状态变化事件，显示/隐藏支付信息区域
                    togglePaymentInfo('domestic');
                    
                    // 填充合同信息
                    if (order.contractType && form.contractType) {
                        form.contractType.value = order.contractType;
                        // 触发合同类型切换
                        try {
                        const contractTypeEvent = new Event('change');
                        form.contractType.dispatchEvent(contractTypeEvent);
                        } catch (error) {
                            console.warn('DEBUG: Error dispatching contract type event:', error);
                        }
                    }
                    form.contractTemplate.value = order.contractTemplate || '';
                    form.contractNumber.value = order.contractNumber || '';
                    form.contractName.value = order.contractName || '';
                    form.contractSignDate.value = order.contractSignDate || '';
                    form.contractAmount.value = order.contractAmount || '';
                    
                    // 显示已上传的文件名
                    if (order.paperContractFile) {
                        document.getElementById('domestic-paper-contract-filename').textContent = order.paperContractFile;
                    }
                } else {
                    console.error('DEBUG: Order not found:', orderId);
                }
            } else {
                console.log('DEBUG: Creating new order');
                document.getElementById('domestic-drawer-title').textContent = '新建家政服务订单';
            }
            
            drawer.classList.add('open');
            document.querySelector('.overlay').classList.add('active');
            console.log('DEBUG: Drawer opened successfully');
        }
        function closeDomesticDrawer() {
            document.getElementById('domestic-order-drawer').classList.remove('open');
            if (!document.querySelector('.drawer.open')) document.querySelector('.overlay').classList.remove('active');
        }
        function submitDomesticOrder() {
            const form = document.getElementById('domestic-order-form');
            if (!form.checkValidity()) { form.reportValidity(); return; }
            const orderData = {
                customerName: form.customerName.value, 
                customerPhone: form.customerPhone.value, 
                serviceAddress: form.serviceAddress.value,
                serviceType: form.serviceType.value, 
                appointmentTime: form.appointmentTime.value, 
                servicePerson: form.servicePerson.value,
                serviceAgency: form.serviceAgency.value,
                amount: form.amount.value,
                paymentStatus: form.paymentStatus.value,
                relatedOpportunity: form.relatedOpportunity.value,
                relatedLead: form.relatedLead.value,
                // 合同信息
                contractType: form.contractType.value,
                contractTemplate: form.contractTemplate.value,
                contractNumber: form.contractNumber.value,
                contractName: form.contractName.value,
                contractSignDate: form.contractSignDate.value,
                contractAmount: form.contractAmount.value,
                paperContractFile: form.paperContract.files.length > 0 ? form.paperContract.files[0].name : null,
                // 支付信息
                paymentInfo: form.paymentStatus.value === '已支付' ? {
                    actualAmount: form.actualAmount.value,
                    paymentMethod: form.paymentMethod.value,
                    paymentDate: form.paymentDate.value,
                    operator: form.operator.value,
                    remarks: form.remarks.value
                } : null
            };
            if (currentDomesticOrderId) {
                // 编辑模式 - 记录变更
                const index = domesticOrdersData.findIndex(o => o.id === currentDomesticOrderId);
                if (index !== -1) {
                    const oldData = domesticOrdersData[index];
                    if(!orderData.paperContractFile) orderData.paperContractFile = oldData.paperContractFile;
                    
                    // 比较变更
                    const fieldMap = {
                        customerName: '客户姓名',
                        customerPhone: '联系电话',
                        serviceAddress: '服务地址',
                        serviceType: '服务类型',
                        appointmentTime: '预约时间',
                        servicePerson: '服务人员',
                        serviceAgency: '服务机构',
                        amount: '订单金额'
                    };
                    
                    const changes = compareObjects(oldData, orderData, fieldMap);
                    
                    domesticOrdersData[index] = { ...oldData, ...orderData };
                    
                    // 记录操作日志
                    if (changes.length > 0) {
                        addOperationLog('domestic', currentDomesticOrderId, 'edit', changes, '编辑订单信息');
                    }
                }
            } else {
                // 新建模式
                orderData.id = 'DS' + new Date().getTime();
                orderData.status = ORDER_STATUS.PENDING_APPROVAL;
                orderData.approvalHistory = [];
                orderData.creationDate = new Date().toISOString().split('T')[0];
                orderData.operationLogs = [];
                
                // 根据服务类型设置默认任务进度
                let defaultTaskProgress = { completed: 0, total: 1 };
                switch(orderData.serviceType) {
                    case '月嫂服务':
                        defaultTaskProgress = { completed: 0, total: 30 }; // 30天
                        break;
                    case '育儿嫂服务':
                        defaultTaskProgress = { completed: 0, total: 31 }; // 31天
                        break;
                    case '小时工':
                        defaultTaskProgress = { completed: 0, total: 4 }; // 4小时
                        break;
                    case '深度保洁':
                    case '家电清洗':
                        defaultTaskProgress = { completed: 0, total: 1 }; // 1次
                        break;
                    case '家务保姆':
                        defaultTaskProgress = { completed: 0, total: 30 }; // 30天
                        break;
                    default:
                        defaultTaskProgress = { completed: 0, total: 1 };
                }
                orderData.taskProgress = defaultTaskProgress;
                
                domesticOrdersData.unshift(orderData);
                
                // 记录创建日志
                addOperationLog('domestic', orderData.id, 'create', [], '创建家政服务订单');
            }
            renderDomesticOrders();
            closeDomesticDrawer();
        }
        function openDomesticDetailDrawer(orderId) {
            currentDomesticOrderId = orderId;
            const order = domesticOrdersData.find(o => o.id === orderId);
            if (!order) return;
            const detailBody = document.getElementById('domestic-detail-body');

            // 审批操作按钮
            let approvalActionsHtml = '';
            if (order.status === '待审批') {
                approvalActionsHtml = `
                    <div style="margin-top: 15px;">
                        <button class="btn btn-success" onclick="approveDomesticOrder('${order.id}')">
                            <i class="fas fa-check"></i> 同意
                        </button>
                        <button class="btn btn-danger" onclick="rejectDomesticOrder('${order.id}')">
                            <i class="fas fa-times"></i> 拒绝
                        </button>
                    </div>
                `;
            }

            // 收款确认按钮
            let paymentActionsHtml = '';
            if (order.status === '待支付' && order.paymentStatus === '未支付') {
                paymentActionsHtml = `
                    <div style="margin-top: 15px;">
                        <button class="btn btn-primary" onclick="confirmDomesticPayment('${order.id}')">
                            <i class="fas fa-money-bill"></i> 确认收款
                        </button>
                    </div>
                `;
            }

            // 合同信息
            let contractHtml = '';
            // 为订单数据添加默认合同类型
            const contractType = order.contractType || 'paper';
            
            if (contractType === 'electronic') {
                contractHtml = `
                    <div class="meta-item">
                        <div class="meta-label">合同类型</div>
                        <div class="meta-value">电子合同</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">合同模板</div>
                        <div class="meta-value">${order.contractTemplate || '家政服务协议模板'}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">合同状态</div>
                        <div class="meta-value">
                            <span class="status-badge status-completed">已签署</span>
                        </div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">签署时间</div>
                        <div class="meta-value">${order.contractSignDate || '2024-11-14'}</div>
                    </div>
                `;
            } else {
                // 默认显示纸质合同信息
                contractHtml = `
                    <div class="meta-item">
                        <div class="meta-label">合同类型</div>
                        <div class="meta-value">纸质合同</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">合同编号</div>
                        <div class="meta-value">${order.contractNumber || 'HT-202406001'}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">合同名称</div>
                        <div class="meta-value">${order.contractName || '家政服务合同'}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">合同周期</div>
                        <div class="meta-value">${order.contractStartDate || '2024-6-1'} - ${order.contractEndDate || '2024-6-30'}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">签署日期</div>
                        <div class="meta-value">${order.contractSignDate || '2024-6-1'}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">合同金额</div>
                        <div class="meta-value">¥${order.contractAmount ? Number(order.contractAmount).toLocaleString() : Number(order.amount).toLocaleString()}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">合同附件</div>
                        <div class="meta-value">
                            ${order.paperContractFile ? 
                                `<div class="attachment-item">
                                    <i class="fas fa-file-contract"></i>
                                    <span class="attachment-name">${order.paperContractFile}</span>
                                    <button class="btn btn-outline" style="font-size:12px; padding: 5px 10px;">下载</button>
                                </div>` : 
                                `<div class="attachment-item">
                                    <i class="fas fa-file-contract"></i>
                                    <span class="attachment-name">家政服务合同.pdf</span>
                                    <button class="btn btn-outline" style="font-size:12px; padding: 5px 10px;">下载</button>
                                </div>`
                            }
                        </div>
                    </div>
                `;
            }

            // 审批历史
            const approvalHistoryHtml = (order.approvalHistory && order.approvalHistory.length > 0)
                ? order.approvalHistory.map(approval => `
                    <li class="timeline-item">
                        <div class="timeline-time">${approval.time}</div>
                        <div class="timeline-content">
                            <strong>${approval.user}</strong> ${approval.action}
                            ${approval.comments ? `<br><small style="color: var(--gray);">${approval.comments}</small>` : ''}
                        </div>
                    </li>
                `).join('')
                : '<li>暂无审批记录</li>';

            // 操作日志
            const operationLogsHtml = (order.operationLogs && order.operationLogs.length > 0)
                ? order.operationLogs.slice(0, 3).map(log => `
                    <li class="timeline-item">
                        <div class="timeline-time">${log.time}</div>
                        <div class="timeline-content">
                            <strong>${log.operator}</strong> ${log.comment}
                        </div>
                    </li>
                `).join('')
                : '<li>暂无操作记录</li>';

            // 人员变动记录
            const personnelChangesHtml = (order.personnelChanges && order.personnelChanges.length > 0)
                ? `
                <div class="table-wrapper" style="margin-top:10px;">
                    <table class="order-table" style="box-shadow: none;">
                      <thead>
                        <tr>
                          <th>变动类型</th>
                          <th>原人员</th>
                          <th>新人员</th>
                          <th>变动时间</th>
                          <th>变动原因</th>
                          <th>操作人</th>
                        </tr>
                      </thead>
                      <tbody>
                ${order.personnelChanges.map(change => `
                    <tr>
                        <td><span class="status-badge ${change.type === '更换' ? 'status-warning' : 'status-info'}">${change.type}</span></td>
                        <td>${change.originalPerson || '-'}</td>
                        <td>${change.newPerson || '-'}</td>
                        <td>${change.changeTime}</td>
                        <td>${change.reason}</td>
                        <td>${change.operator}</td>
                    </tr>
                `).join('')}
                      </tbody>
                    </table>
                </div>`
                : '<div style="margin-top:15px; padding: 15px 0; color: var(--gray);">暂无人员变动记录</div>';

            // 收支记录
            const financialRecordsHtml = (order.financialRecords && order.financialRecords.length > 0)
                ? `
                <div class="table-wrapper" style="margin-top:10px;">
                    <table class="order-table" style="box-shadow: none;">
                      <thead>
                        <tr>
                          <th>类型</th>
                          <th>金额</th>
                          <th>日期</th>
                          <th>描述</th>
                          <th>操作</th>
                        </tr>
                      </thead>
                      <tbody>
                ${order.financialRecords.map(record => `
                    <tr>
                        <td><span class="status-badge ${record.type === 'income' ? 'status-completed' : 'status-rejected'}">${record.type === 'income' ? '额外收入' : '赔偿支出'}</span></td>
                        <td>¥${record.amount}</td>
                        <td>${record.date}</td>
                        <td>${record.description}</td>
                        <td><button class="btn-action btn-edit" style="font-size:12px; padding: 3px 8px;" onclick="openFinancialRecordDrawer('edit', '${order.id}', '${record.recordId}')">编辑</button></td>
                    </tr>
                `).join('')}
                      </tbody>
                    </table>
                </div>`
                : '<div style="margin-top:15px; padding: 15px 0; color: var(--gray);">暂无收支记录</div>';

            // 服务评价
            const evaluationHtml = order.evaluation 
                ? `
                <div class="evaluation-section">
                    <div class="evaluation-rating">
                        <div class="stars">
                            ${'★'.repeat(order.evaluation.rating)}${'☆'.repeat(5 - order.evaluation.rating)}
                        </div>
                        <div class="rating-text">${order.evaluation.rating}.0分</div>
                    </div>
                    <div class="evaluation-tags">
                        ${order.evaluation.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                    <div class="evaluation-comment">
                        ${order.evaluation.comment}
                    </div>
                </div>` 
                : '';
            
            const evaluationActionsHtml = order.status === '已完成'
                ? `<div style="text-align: right; margin-top: 15px;">
                       <button class="btn btn-primary" style="font-size:12px; padding: 5px 10px;" onclick="openDomesticEvaluationDrawer('${order.id}')">${order.evaluation ? '编辑评价' : '添加评价'}</button>
                   </div>`
                : '';

            // 售后记录
            const afterSalesHtml = (order.afterSales && order.afterSales.length > 0)
                ? `
                <div class="table-wrapper" style="margin-top:10px;">
                    <table class="order-table" style="box-shadow: none;">
                      <thead>
                        <tr>
                          <th>工单类型</th>
                          <th>状态</th>
                          <th>创建时间</th>
                          <th>操作</th>
                        </tr>
                      </thead>
                      <tbody>
                ${order.afterSales.map(ticket => `
                    <tr>
                        <td>${ticket.type}</td>
                        <td><span class="status-badge ${statusClassMap[ticket.status] || ''}">${ticket.status}</span></td>
                        <td>${ticket.creationDate}</td>
                        <td>
                            <button class="btn-action btn-view" style="font-size:12px; padding: 3px 8px;" onclick="openDomesticAfterSalesDrawer('edit', '${order.id}', '${ticket.ticketId}')">详情</button>
                        </td>
                    </tr>
                `).join('')}
                      </tbody>
                    </table>
                </div>`
                : '<div style="margin-top:15px; padding: 15px 0; color: var(--gray);">暂无售后记录</div>';

            detailBody.innerHTML = `
                <div class="project-card" style="box-shadow:none; border: 1px solid var(--border);">
                    <div class="project-header">
                        <div class="project-title">${order.serviceType} - ${order.customerName}</div>
                        <div class="project-status status-badge ${statusClassMap[order.status] || 'status-pending'}">${order.status}</div>
                    </div>
                    <div class="project-meta">
                        <div class="meta-item">
                            <div class="meta-label">订单号</div>
                            <div class="meta-value">${order.id}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">支付状态</div>
                            <div class="meta-value">
                                <span class="status-badge ${paymentStatusClassMap[order.paymentStatus] || 'status-pending'}">${order.paymentStatus}</span>
                            </div>
                        </div>
                        ${order.relatedOpportunity ? `<div class="meta-item"><div class="meta-label">关联商机</div><div class="meta-value">${order.relatedOpportunity}</div></div>` : ''}
                        ${order.relatedLead ? `<div class="meta-item"><div class="meta-label">关联线索</div><div class="meta-value">${order.relatedLead}</div></div>` : ''}
                    </div>
                </div>
                
                <!-- Tab页导航 -->
                <div class="business-tabs" style="margin-top: 20px;">
                    <div class="business-nav">
                        <div class="business-tab active" onclick="switchDomesticDetailTab('basic', '${order.id}')">基本信息</div>
                        <div class="business-tab" onclick="switchDomesticDetailTab('tasks', '${order.id}')">服务任务列表</div>
                        <div class="business-tab" onclick="switchDomesticDetailTab('contract', '${order.id}')" style="display: none;">合同管理</div>
                        <div class="business-tab" onclick="switchDomesticDetailTab('approval', '${order.id}')" style="display: none;">审批流程</div>
                        <div class="business-tab" onclick="switchDomesticDetailTab('payment', '${order.id}')">收款信息</div>
                        <div class="business-tab" onclick="switchDomesticDetailTab('logs', '${order.id}')">操作日志</div>
                        <div class="business-tab" onclick="switchDomesticDetailTab('personnel', '${order.id}')">人员变动</div>
                        <div class="business-tab" onclick="switchDomesticDetailTab('financial', '${order.id}')">收支记录</div>
                        <div class="business-tab" onclick="switchDomesticDetailTab('evaluation', '${order.id}')">服务评价</div>
                        <div class="business-tab" onclick="switchDomesticDetailTab('aftersales', '${order.id}')">售后记录</div>
                    </div>
                    
                    <!-- Tab页内容 -->
                    <div class="order-content">
                        <!-- 基本信息Tab -->
                        <div id="domestic-tab-basic" class="domestic-tab-content active">
                            <div class="section">
                                <div class="section-title">
                                    <i class="fas fa-info-circle" style="color: #3498db; margin-right: 8px;"></i>
                                    基本信息
                                </div>
                                <div class="project-meta">
                                    <div class="meta-item">
                                        <div class="meta-label">客户姓名</div>
                                        <div class="meta-value">${order.customerName}</div>
                                    </div>
                                    <div class="meta-item">
                                        <div class="meta-label">联系电话</div>
                                        <div class="meta-value">${order.customerPhone}</div>
                                    </div>
                                    <div class="meta-item">
                                        <div class="meta-label">服务地址</div>
                                        <div class="meta-value">${order.serviceAddress}</div>
                                    </div>
                                    <div class="meta-item">
                                        <div class="meta-label">服务类型</div>
                                        <div class="meta-value">${order.serviceType}</div>
                                    </div>
                                    <div class="meta-item">
                                        <div class="meta-label">预约时间</div>
                                        <div class="meta-value">${new Date(order.appointmentTime).toLocaleString('zh-CN')}</div>
                                    </div>
                                    <div class="meta-item">
                                        <div class="meta-label">服务人员</div>
                                        <div class="meta-value">${order.servicePerson}</div>
                                    </div>
                                    <div class="meta-item">
                                        <div class="meta-label">服务机构</div>
                                        <div class="meta-value">${order.serviceAgency || '-'}</div>
                                    </div>
                                    <div class="meta-item">
                                        <div class="meta-label">订单金额</div>
                                        <div class="meta-value">¥${Number(order.amount).toLocaleString()}</div>
                                    </div>
                                </div>
                            </div>
                
                        </div>
                        
                        <!-- 服务任务列表Tab -->
                        <div id="domestic-tab-tasks" class="domestic-tab-content">
                            <div class="section">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                    <div class="section-title" style="border: none; margin: 0;">
                                        <i class="fas fa-tasks" style="color: #3498db; margin-right: 8px;"></i>
                                        服务任务列表
                                    </div>
                                    <div style="display: flex; gap: 10px;">
                                        <button class="btn btn-outline" onclick="batchReassignTasks('${order.id}')">
                                            <i class="fas fa-user-exchange"></i> 批量重指派
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- 任务筛选 -->
                                <div class="filter-bar" style="margin-bottom: 20px;">
                                    <div class="filter-group">
                                        <label>任务状态:</label>
                                        <select id="task-status-filter" onchange="filterTasks('${order.id}')">
                                            <option value="">全部状态</option>
                                            <option value="待指派">待指派</option>
                                            <option value="待执行">待执行</option>
                                            <option value="已完成">已完成</option>
                                            <option value="已取消">已取消</option>
                                        </select>
                                    </div>
                                    <div class="filter-group">
                                        <label>执行人员:</label>
                                        <select id="task-auntie-filter" onchange="filterTasks('${order.id}')">
                                            <option value="">全部人员</option>
                                        </select>
                                    </div>
                                    <div class="filter-group">
                                        <label>日期范围:</label>
                                        <input type="date" id="task-date-start" onchange="filterTasks('${order.id}')" style="margin-right: 5px;">
                                        <span>至</span>
                                        <input type="date" id="task-date-end" onchange="filterTasks('${order.id}')" style="margin-left: 5px;">
                                    </div>
                                </div>
                                
                                <!-- 任务列表 -->
                                <div id="domestic-tasks-table-container">
                                    ${generateTasksTableHtml(order)}
                                </div>
                            </div>
                        </div>
                        
                        <!-- 合同管理Tab -->
                        <div id="domestic-tab-contract" class="domestic-tab-content" style="display: none;">
                            <div class="section">
                                <div class="section-title">
                                    <i class="fas fa-file-contract" style="color: #f39c12; margin-right: 8px;"></i>
                                    合同管理
                                </div>
                                <div class="project-card" style="box-shadow:none; border: 1px solid var(--border);">
                                    <div class="project-meta">
                                        ${contractHtml}
                                    </div>
                                    <div style="text-align: right; margin-top: 15px;">
                                        ${order.contractType === 'electronic' ? `
                                            <button class="btn btn-outline" style="margin-right: 10px; font-size:12px; padding: 5px 10px;" onclick="alert('查看合同功能开发中...')">
                                                <i class="fas fa-eye"></i> 查看合同
                                            </button>
                                            <button class="btn btn-outline" style="font-size:12px; padding: 5px 10px;" onclick="alert('下载合同功能开发中...')">
                                                <i class="fas fa-download"></i> 下载合同
                                            </button>
                                        ` : `
                                            <button class="btn btn-outline" style="margin-right: 10px; font-size:12px; padding: 5px 10px;" onclick="openContractModal('domestic', '${order.id}', 'electronic')">
                                                <i class="fas fa-file-signature"></i> 发起电子合同
                                            </button>
                                            <button class="btn btn-outline" style="font-size:12px; padding: 5px 10px;" onclick="openContractModal('domestic', '${order.id}', 'paper')">
                                                <i class="fas fa-upload"></i> 上传纸质合同
                                            </button>
                                        `}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 审批流程Tab -->
                        <div id="domestic-tab-approval" class="domestic-tab-content" style="display: none;">
                            <div class="section">
                                <div class="section-title">
                                    <i class="fas fa-gavel" style="color: #e74c3c; margin-right: 8px;"></i>
                                    审批流程
                                </div>
                                <ul class="timeline">
                                    ${approvalHistoryHtml}
                                </ul>
                                ${approvalActionsHtml}
                            </div>
                        </div>
                        
                        <!-- 收款信息Tab -->
                        <div id="domestic-tab-payment" class="domestic-tab-content">
                            <div class="section">
                                <div class="section-title">
                                    <i class="fas fa-money-bill-wave" style="color: #27ae60; margin-right: 8px;"></i>
                                    收款信息
                                </div>
                                <div class="project-meta">
                                    <div class="meta-item">
                                        <div class="meta-label">订单金额</div>
                                        <div class="meta-value">¥${Number(order.amount).toLocaleString()}</div>
                                    </div>
                                    <div class="meta-item">
                                        <div class="meta-label">支付状态</div>
                                        <div class="meta-value">
                                            <span class="status-badge ${paymentStatusClassMap[order.paymentStatus] || 'status-pending'}">${order.paymentStatus}</span>
                                        </div>
                                    </div>
                                    ${order.paymentInfo && order.paymentInfo.actualAmount ? `
                                    <div class="meta-item">
                                        <div class="meta-label">收款金额</div>
                                        <div class="meta-value">¥${Number(order.paymentInfo.actualAmount).toLocaleString()}</div>
                                    </div>
                                    ` : ''}
                                    ${order.paymentInfo && order.paymentInfo.paymentMethod ? `
                                    <div class="meta-item">
                                        <div class="meta-label">收款方式</div>
                                        <div class="meta-value">${order.paymentInfo.paymentMethod}</div>
                                    </div>
                                    ` : ''}
                                    ${order.paymentInfo && order.paymentInfo.paymentDate ? `
                                    <div class="meta-item">
                                        <div class="meta-label">收款日期</div>
                                        <div class="meta-value">${order.paymentInfo.paymentDate}</div>
                                    </div>
                                    ` : ''}
                                    ${order.paymentInfo && order.paymentInfo.operator ? `
                                    <div class="meta-item">
                                        <div class="meta-label">操作人</div>
                                        <div class="meta-value">${order.paymentInfo.operator}</div>
                                    </div>
                                    ` : ''}
                                    ${order.paymentInfo && order.paymentInfo.remarks ? `
                                    <div class="meta-item">
                                        <div class="meta-label">收款备注</div>
                                        <div class="meta-value">${order.paymentInfo.remarks}</div>
                                    </div>
                                    ` : ''}
                                    ${order.paymentTime ? `
                                    <div class="meta-item">
                                        <div class="meta-label">支付时间</div>
                                        <div class="meta-value">${order.paymentTime}</div>
                                    </div>
                                    ` : ''}
                                    ${order.paymentMethod ? `
                                    <div class="meta-item">
                                        <div class="meta-label">支付方式</div>
                                        <div class="meta-value">${order.paymentMethod}</div>
                                    </div>
                                    ` : ''}
                                </div>
                                ${paymentActionsHtml}
                            </div>
                        </div>
                        
                        <!-- 操作日志Tab -->
                        <div id="domestic-tab-logs" class="domestic-tab-content">
                            <div class="section">
                                <div class="section-title">
                                    <i class="fas fa-history" style="color: #95a5a6; margin-right: 8px;"></i>
                                    操作日志
                                </div>
                                <div style="text-align: right; margin-bottom: 15px;">
                                    <button class="btn btn-outline" style="font-size:12px; padding: 5px 10px;" onclick="openOperationLog('domestic', '${order.id}')">
                                        <i class="fas fa-list"></i> 查看完整日志
                                    </button>
                                </div>
                                <ul class="timeline">
                                    ${operationLogsHtml}
                                </ul>
                            </div>
                        </div>

                        <!-- 人员变动记录Tab -->
                        <div id="domestic-tab-personnel" class="domestic-tab-content">
                            <div class="section">
                                <div class="section-title">
                                    <i class="fas fa-user-exchange" style="color: #9b59b6; margin-right: 8px;"></i>
                                    人员变动记录
                                </div>
                                ${personnelChangesHtml}
                            </div>
                        </div>

                        <!-- 收支记录Tab -->
                        <div id="domestic-tab-financial" class="domestic-tab-content">
                            <div class="section">
                                <div class="section-title">
                                    <i class="fas fa-chart-line" style="color: #e67e22; margin-right: 8px;"></i>
                                    收支记录
                                </div>
                                <div style="text-align: right; margin-bottom: 15px;">
                                    <button class="btn btn-primary" style="font-size:12px; padding: 5px 10px;" onclick="openFinancialRecordDrawer('new', '${order.id}')">
                                        <i class="fas fa-plus"></i> 新增收支记录
                                    </button>
                                </div>
                                ${financialRecordsHtml}
                            </div>
                        </div>

                        <!-- 服务评价Tab -->
                        <div id="domestic-tab-evaluation" class="domestic-tab-content">
                            <div class="section">
                                <div class="section-title">
                                    <i class="fas fa-star" style="color: #f1c40f; margin-right: 8px;"></i>
                                    服务评价
                                </div>
                                ${order.evaluation ? evaluationHtml : '<div style="padding: 15px 0; color: var(--gray);">客户暂未评价</div>'}
                                ${evaluationActionsHtml}
                            </div>
                        </div>

                        <!-- 售后记录Tab -->
                        <div id="domestic-tab-aftersales" class="domestic-tab-content">
                            <div class="section">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <h4 class="section-title" style="border: none; margin: 0;">
                                        <i class="fas fa-tools" style="color: #34495e; margin-right: 8px;"></i>
                                        售后记录
                                    </h4>
                                    <button class="btn btn-primary" style="font-size:12px; padding: 5px 10px;" onclick="openDomesticAfterSalesDrawer('new', '${order.id}')">
                                        <i class="fas fa-plus"></i> 新建售后工单
                                    </button>
                                </div>
                                ${afterSalesHtml}
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('domestic-detail-drawer').classList.add('open');
            document.querySelector('.overlay').classList.add('active');
        }
        function closeDomesticDetailDrawer() {
            document.getElementById('domestic-detail-drawer').classList.remove('open');
            if (!document.querySelector('.drawer.open')) document.querySelector('.overlay').classList.remove('active');
        }
        function editDomesticFromDetailView() { if(currentDomesticOrderId) { closeDomesticDetailDrawer(); openDomesticDrawer('edit', currentDomesticOrderId); } }
        function deleteDomesticOrder(orderId) { 
            if(confirm('确定删除？')) { 
                const order = domesticOrdersData.find(o => o.id === orderId);
                if (order) {
                    // 记录删除日志
                    addOperationLog('domestic', orderId, 'delete', [], '删除家政服务订单');
                }
                domesticOrdersData = domesticOrdersData.filter(o => o.id !== orderId); 
                renderDomesticOrders(); 
            } 
        }
        
        function approveDomesticOrder(orderId) {
            const order = domesticOrdersData.find(o => o.id === orderId);
            if(order) {
                const oldStatus = order.status;
                const oldPaymentStatus = order.paymentStatus;
                
                // 根据文档要求，审批通过后进入待支付状态
                order.status = ORDER_STATUS.PENDING_PAYMENT;
                order.paymentStatus = PAYMENT_STATUS.PENDING;
                
                order.approvalHistory.push({
                    action: '批准',
                    user: '张三 (管理员)',
                    time: new Date().toLocaleString('zh-CN'),
                    comments: '合同审批通过，订单进入待支付状态。'
                });
                
                // 记录操作日志
                addOperationLog('domestic', orderId, 'approve', [
                    { field: '订单状态', oldValue: oldStatus, newValue: ORDER_STATUS.PENDING_PAYMENT },
                    { field: '支付状态', oldValue: oldPaymentStatus, newValue: PAYMENT_STATUS.PENDING }
                ], '合同审批通过');
                
                closeDomesticDetailDrawer();
                renderDomesticOrders();
                alert('审批通过！订单已进入待支付状态。');
            }
        }

        function rejectDomesticOrder(orderId) {
            const reason = prompt('请输入驳回原因：');
            if (reason) {
                const order = domesticOrdersData.find(o => o.id === orderId);
                if(order) {
                    const oldStatus = order.status;
                    const oldPaymentStatus = order.paymentStatus;
                    
                    order.status = ORDER_STATUS.REJECTED;
                    order.paymentStatus = PAYMENT_STATUS.CANCELLED;
                    
                    order.approvalHistory.push({
                        action: '驳回',
                        user: '张三 (管理员)',
                        time: new Date().toLocaleString('zh-CN'),
                        comments: reason
                    });
                    
                    // 记录操作日志
                    addOperationLog('domestic', orderId, 'reject', [
                        { field: '订单状态', oldValue: oldStatus, newValue: ORDER_STATUS.REJECTED },
                        { field: '支付状态', oldValue: oldPaymentStatus, newValue: PAYMENT_STATUS.CANCELLED }
                    ], `驳回原因：${reason}`);
                    
                    closeDomesticDetailDrawer();
                    renderDomesticOrders();
                    alert('订单已驳回！');
                }
            }
        }

        function confirmDomesticPayment(orderId) {
            const order = domesticOrdersData.find(o => o.id === orderId);
            if(order) {
                const paymentMethod = prompt('请输入支付方式（如：现金、微信、支付宝、银行转账）：');
                if (paymentMethod) {
                    const oldPaymentStatus = order.paymentStatus;
                    
                    order.paymentStatus = PAYMENT_STATUS.COMPLETED;
                    order.paymentTime = new Date().toLocaleString('zh-CN');
                    order.paymentMethod = paymentMethod;
                    
                    // 设置完整的paymentInfo字段
                    order.paymentInfo = {
                        actualAmount: order.amount,
                        paymentMethod: paymentMethod,
                        paymentDate: new Date().toISOString().split('T')[0],
                        operator: '当前用户',
                        remarks: '通过确认收款功能设置'
                    };
                    
                    // 记录操作日志
                    addOperationLog('domestic', orderId, 'payment', [
                        { field: '支付状态', oldValue: oldPaymentStatus, newValue: PAYMENT_STATUS.COMPLETED },
                        { field: '支付方式', oldValue: '', newValue: paymentMethod },
                        { field: '支付时间', oldValue: '', newValue: order.paymentTime }
                    ], '确认收款');
                    
                    closeDomesticDetailDrawer();
                    renderDomesticOrders();
                    alert('收款确认成功！');
                }
            }
        }

        function openDomesticAfterSalesDrawer(mode = 'new', orderId, ticketId = null) {
            const drawer = document.getElementById('domestic-after-sales-drawer');
            const title = document.getElementById('after-sales-drawer-title');
            const form = document.getElementById('domestic-after-sales-form');
            
            form.reset();
            currentDomesticOrderId = orderId;
            currentAfterSalesTicketId = null;

            if (mode === 'edit' && orderId && ticketId) {
                title.textContent = '编辑售后工单';
                const order = domesticOrdersData.find(o => o.id === orderId);
                const ticket = order?.afterSales.find(t => t.ticketId === ticketId);
                if (ticket) {
                    currentAfterSalesTicketId = ticketId;
                    form.ticketId.value = ticket.ticketId;
                    form.type.value = ticket.type;
                    form.description.value = ticket.description;
                    form.status.value = ticket.status;
                    form.resolution.value = ticket.resolution || '';
                }
            } else {
                title.textContent = '新建售后工单';
            }

            drawer.classList.add('open');
            if (!document.querySelector('.overlay').classList.contains('active')) {
                 document.querySelector('.overlay').classList.add('active');
            }
        }

        function closeDomesticAfterSalesDrawer() {
            document.getElementById('domestic-after-sales-drawer').classList.remove('open');
            // Don't remove overlay if detail drawer is still open
            if (!document.getElementById('domestic-detail-drawer').classList.contains('open')) {
                document.querySelector('.overlay').classList.remove('active');
            }
        }

        function submitDomesticAfterSales() {
            const form = document.getElementById('domestic-after-sales-form');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const order = domesticOrdersData.find(o => o.id === currentDomesticOrderId);
            if (!order) return;

            const ticketData = {
                type: form.type.value,
                description: form.description.value,
                status: form.status.value,
                resolution: form.resolution.value,
            };

            if (currentAfterSalesTicketId) { // Editing existing ticket
                const ticketIndex = order.afterSales.findIndex(t => t.ticketId === currentAfterSalesTicketId);
                if (ticketIndex !== -1) {
                    const originalTicket = order.afterSales[ticketIndex];
                    order.afterSales[ticketIndex] = { ...originalTicket, ...ticketData };
                    if(ticketData.status === '已解决' && !originalTicket.resolutionDate) {
                         order.afterSales[ticketIndex].resolutionDate = new Date().toISOString().split('T')[0];
                    }
                }
            } else { // Creating new ticket
                ticketData.ticketId = 'AS' + Date.now();
                ticketData.creationDate = new Date().toISOString().split('T')[0];
                if (ticketData.status === '已解决' || ticketData.status === '已关闭') {
                    ticketData.resolutionDate = new Date().toISOString().split('T')[0];
                }
                order.afterSales.push(ticketData);
            }
            
            closeDomesticAfterSalesDrawer();
            // Refresh the detail view to show the changes
            openDomesticDetailDrawer(currentDomesticOrderId);
        }
  
        function openDomesticEvaluationDrawer(orderId) {
            const drawer = document.getElementById('domestic-evaluation-drawer');
            const form = document.getElementById('domestic-evaluation-form');
            form.reset();
            form.orderId.value = orderId;
            currentEvaluationOrderId = orderId;
            
            const order = domesticOrdersData.find(o => o.id === orderId);
            if (order && order.evaluation) {
                form.rating.value = order.evaluation.rating;
                form.tags.value = order.evaluation.tags.join(', ');
                form.comment.value = order.evaluation.comment;
                updateStars(order.evaluation.rating);
            } else {
                form.rating.value = 0;
                updateStars(0);
            }

            drawer.classList.add('open');
            if (!document.querySelector('.overlay').classList.contains('active')) {
                 document.querySelector('.overlay').classList.add('active');
            }
        }

        function closeDomesticEvaluationDrawer() {
            document.getElementById('domestic-evaluation-drawer').classList.remove('open');
            if (!document.getElementById('domestic-detail-drawer').classList.contains('open') && !document.getElementById('domestic-after-sales-drawer').classList.contains('open')) {
                 document.querySelector('.overlay').classList.remove('active');
            }
        }

        function submitDomesticEvaluation() {
            const form = document.getElementById('domestic-evaluation-form');
            const orderId = form.orderId.value;
            const order = domesticOrdersData.find(o => o.id === orderId);

            if (!order) {
                alert('未找到对应订单');
                return;
            }

            const rating = form.rating.value;
            if (!rating || rating === '0') {
                alert('请选择服务评分');
                return;
            }

            order.evaluation = {
                rating: parseInt(rating),
                tags: form.tags.value ? form.tags.value.split(',').map(tag => tag.trim()).filter(Boolean) : [],
                comment: form.comment.value
            };

            closeDomesticEvaluationDrawer();
            openDomesticDetailDrawer(orderId); // Refresh the detail view
        }

        function setupStarRating() {
            const starsContainer = document.querySelector('.stars-input');
            if (!starsContainer) return;

            starsContainer.addEventListener('click', e => {
                if (e.target.classList.contains('fa-star')) {
                    const value = e.target.dataset.value;
                    document.querySelector('#domestic-evaluation-form input[name="rating"]').value = value;
                    updateStars(value);
                }
            });
        }

        function updateStars(value) {
            const stars = document.querySelectorAll('.stars-input .fa-star');
            stars.forEach(star => {
                if (star.dataset.value <= value) {
                    star.style.color = 'var(--warning)';
                } else {
                    star.style.color = 'var(--gray)';
                }
            });
        }

        function openFinancialRecordDrawer(mode = 'new', orderId, recordId = null) {
            const drawer = document.getElementById('financial-record-drawer');
            const form = document.getElementById('financial-record-form');
            form.reset();
            currentDomesticOrderId = orderId;
            currentFinancialRecordId = null;
            form.orderId.value = orderId;

            if (mode === 'edit' && recordId) {
                document.getElementById('financial-record-drawer-title').textContent = '编辑收支记录';
                const order = domesticOrdersData.find(o => o.id === orderId);
                const record = order?.financialRecords.find(r => r.recordId === recordId);
                if (record) {
                    currentFinancialRecordId = recordId;
                    form.recordId.value = recordId;
                    form.type.value = record.type;
                    form.amount.value = record.amount;
                    form.date.value = record.date;
                    form.description.value = record.description;
                }
            } else {
                document.getElementById('financial-record-drawer-title').textContent = '记一笔';
                form.date.value = new Date().toISOString().slice(0, 10); // Default to today
            }
            drawer.classList.add('open');
            if (!document.querySelector('.overlay').classList.contains('active')) {
                document.querySelector('.overlay').classList.add('active');
            }
        }

        function closeFinancialRecordDrawer() {
            document.getElementById('financial-record-drawer').classList.remove('open');
            // Don't remove overlay if detail drawer is still open
            if (!document.getElementById('domestic-detail-drawer').classList.contains('open')) {
                document.querySelector('.overlay').classList.remove('active');
            }
        }

        function submitFinancialRecord() {
            const form = document.getElementById('financial-record-form');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const orderId = form.orderId.value;
            const order = domesticOrdersData.find(o => o.id === orderId);
            if (!order) return;

            const recordData = {
                type: form.type.value,
                amount: parseFloat(form.amount.value),
                date: form.date.value,
                description: form.description.value,
            };

            if (currentFinancialRecordId) { // Editing
                const recordIndex = order.financialRecords.findIndex(r => r.recordId === currentFinancialRecordId);
                if (recordIndex !== -1) {
                    order.financialRecords[recordIndex] = { ...order.financialRecords[recordIndex], ...recordData };
                }
            } else { // Creating
                recordData.recordId = 'FR' + Date.now();
                if (!order.financialRecords) {
                    order.financialRecords = [];
                }
                order.financialRecords.push(recordData);
            }

            closeFinancialRecordDrawer();
            openDomesticDetailDrawer(orderId); // Refresh detail view
        }

        // --- 操作日志相关功能 ---
        
        // 打开操作日志抽屉
        function openOperationLog(type, id) {
            currentOperationLogType = type;
            currentOperationLogId = id;
            
            const drawer = document.getElementById('operation-log-drawer');
            const title = document.getElementById('operation-log-title');
            const body = document.getElementById('operation-log-body');
            
            // 根据订单类型设置标题
            const typeNames = {
                'practice': '高校实践订单',
                'training': '企业培训订单', 
                'personal': '个人培训订单',
                'domestic': '家政服务订单'
            };
            
            title.textContent = `${typeNames[type]} - 操作日志`;
            
            // 获取订单数据
            let order = null;
            let logs = [];
            
            switch(type) {
                case 'practice':
                    order = practiceOrdersData.find(o => o.id === id);
                    break;
                case 'training':
                    order = trainingOrdersData.find(o => o.id === id);
                    break;
                case 'personal':
                    order = personalOrdersData.find(o => o.id === id);
                    break;
                case 'domestic':
                    order = domesticOrdersData.find(o => o.id === id);
                    break;
            }
            
            if (!order) {
                body.innerHTML = '<div style="text-align: center; padding: 20px; color: var(--gray);">未找到订单信息</div>';
                drawer.classList.add('open');
                document.querySelector('.overlay').classList.add('active');
                return;
            }
            
            logs = order.operationLogs || [];
            
            // 渲染操作日志
            if (logs.length === 0) {
                body.innerHTML = '<div style="text-align: center; padding: 20px; color: var(--gray);">暂无操作日志</div>';
            } else {
                body.innerHTML = logs.map(log => renderLogItem(log)).join('');
            }
            
            drawer.classList.add('open');
            document.querySelector('.overlay').classList.add('active');
        }
        
        // 关闭操作日志抽屉
        function closeOperationLog() {
            document.getElementById('operation-log-drawer').classList.remove('open');
            if (!document.querySelector('.drawer.open')) {
                document.querySelector('.overlay').classList.remove('active');
            }
        }
        
        // 渲染单个日志项
        function renderLogItem(log) {
            const actionClassMap = {
                'create': 'log-action-create',
                'edit': 'log-action-edit', 
                'delete': 'log-action-delete',
                'approve': 'log-action-approve',
                'reject': 'log-action-reject',
                '派单': 'log-action-approve'
            };
            
            const actionTextMap = {
                'create': '创建',
                'edit': '编辑',
                'delete': '删除',
                'approve': '审批通过',
                'reject': '审批驳回',
                '派单': '派单'
            };
            
            let changesHtml = '';
            
            // 处理派单操作的变更记录
            if (log.type === '派单' && log.changes) {
                changesHtml = `
                <div class="log-changes">
                    <div class="change-item">
                        <span class="change-field">订单状态:</span>
                        <span class="change-old">${log.changes.status.before}</span>
                        <span>→</span>
                        <span class="change-new">${log.changes.status.after}</span>
                    </div>
                    <div class="change-item">
                        <span class="change-field">派单对象:</span>
                        <span class="change-old">${log.changes.assignedProvider.before || '未派单'}</span>
                        <span>→</span>
                        <span class="change-new">${log.changes.assignedProvider.after}</span>
                    </div>
                </div>
                `;
            } else if (log.changes && log.changes.length > 0) {
                // 处理普通变更记录
                changesHtml = `
                <div class="log-changes">
                    ${log.changes.map(change => `
                        <div class="change-item">
                            <span class="change-field">${change.field}:</span>
                            <span class="change-old">${change.oldValue}</span>
                            <span>→</span>
                            <span class="change-new">${change.newValue}</span>
                        </div>
                    `).join('')}
                </div>
                `;
            }
            
            const commentHtml = log.comment 
                ? `<div class="log-comment">${log.comment}</div>` : '';
            
            const action = log.type || log.action;
            
            return `
                <div class="log-item">
                    <div class="log-header">
                        <div>
                            <span class="log-operator">${log.operator}</span>
                            <span class="log-time">${log.time}</span>
                        </div>
                        <span class="log-action ${actionClassMap[action] || ''}">${actionTextMap[action] || action}</span>
                    </div>
                    <div class="log-content">
                        ${changesHtml}
                        ${commentHtml}
                    </div>
                </div>
            `;
        }
        
        // 添加操作日志的通用函数
        function addOperationLog(type, id, action, changes = [], comment = '') {
            let order = null;
            
            switch(type) {
                case 'practice':
                    order = practiceOrdersData.find(o => o.id === id);
                    break;
                case 'training':
                    order = trainingOrdersData.find(o => o.id === id);
                    break;
                case 'personal':
                    order = personalOrdersData.find(o => o.id === id);
                    break;
                case 'domestic':
                    order = domesticOrdersData.find(o => o.id === id);
                    break;
            }
            
            if (!order) return;
            
            if (!order.operationLogs) {
                order.operationLogs = [];
            }
            
            const log = {
                id: 'LOG' + Date.now(),
                action: action,
                operator: '张三 (管理员)', // 这里应该从当前登录用户获取
                time: new Date().toLocaleString('zh-CN'),
                changes: changes,
                comment: comment
            };
            
            order.operationLogs.unshift(log);
        }
        
        // 比较两个对象，返回变更记录
        function compareObjects(oldObj, newObj, fieldMap = {}) {
            const changes = [];
            
            for (const key in newObj) {
                if (oldObj[key] !== newObj[key] && newObj[key] !== undefined) {
                    const fieldName = fieldMap[key] || key;
                    changes.push({
                        field: fieldName,
                        oldValue: oldObj[key] || '',
                        newValue: newObj[key]
                    });
                }
            }
            
            return changes;
        }

        // 控制支付信息区域的显示/隐藏
        function togglePaymentInfo(type) {
            const paymentStatusSelect = document.getElementById(`${type}-payment-status`);
            const paymentInfoDiv = document.getElementById(`${type}-payment-info`);
            
            if (paymentStatusSelect && paymentInfoDiv) {
                if (paymentStatusSelect.value === '已支付' || paymentStatusSelect.value === 'completed') {
                    paymentInfoDiv.style.display = 'block';
                    
                    // 设置必填字段验证
                    const requiredFields = paymentInfoDiv.querySelectorAll('input[required], select[required]');
                    requiredFields.forEach(field => {
                        field.required = true;
                    });
                } else {
                    paymentInfoDiv.style.display = 'none';
                    
                    // 清除必填字段验证
                    const fields = paymentInfoDiv.querySelectorAll('input, select, textarea');
                    fields.forEach(field => {
                        field.required = false;
                        field.value = '';
                    });
                }
            }
        }

        // 获取支付方式文本
        function getPaymentMethodText(method) {
            const methodMap = {
                '现金': '现金',
                '微信支付': '微信支付',
                '支付宝': '支付宝',
                '银行转账': '银行转账',
                'POS机刷卡': 'POS机刷卡',
                '其他': '其他'
            };
            return methodMap[method] || method || '-';
        }

        // 派单功能
        function openDispatchDrawer(orderId) {
            currentDispatchOrderId = orderId;
            selectedProviderId = null;
            
            // 获取订单信息
            const order = domesticOrdersData.find(o => o.id === orderId);
            if (!order) return;
            
            // 更新订单信息显示
            document.getElementById('dispatch-customer-name').textContent = order.customerName;
            document.getElementById('dispatch-service-type').textContent = order.serviceType;
            document.getElementById('dispatch-appointment-time').textContent = order.appointmentTime;
            document.getElementById('dispatch-service-address').textContent = order.serviceAddress;
            
            document.getElementById('dispatch-customer-name-agency').textContent = order.customerName;
            document.getElementById('dispatch-service-type-agency').textContent = order.serviceType;
            document.getElementById('dispatch-appointment-time-agency').textContent = order.appointmentTime;
            document.getElementById('dispatch-service-address-agency').textContent = order.serviceAddress;
            
            // 渲染服务人员列表
            renderIndividualProviders();
            renderAgencyProviders();
            
            // 显示抽屉
            document.getElementById('dispatch-drawer').classList.add('open');
            document.querySelector('.overlay').classList.add('active');
            
            // 重置选择状态
            document.getElementById('confirm-dispatch-btn').disabled = true;
        }
        
        function closeDispatchDrawer() {
            document.getElementById('dispatch-drawer').classList.remove('open');
            if (!document.querySelector('.drawer.open')) {
                document.querySelector('.overlay').classList.remove('active');
            }
            selectedProviderId = null;
        }
        
        function switchDispatchTab(type) {
            currentDispatchType = type;
            
            // 更新标签状态
            document.querySelectorAll('.dispatch-tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            // 更新内容显示
            document.querySelectorAll('.dispatch-section').forEach(section => section.classList.remove('active'));
            if (type === 'individual') {
                document.getElementById('individual-dispatch').classList.add('active');
            } else {
                document.getElementById('agency-dispatch').classList.add('active');
            }
            
            // 重置选择状态
            selectedProviderId = null;
            document.getElementById('confirm-dispatch-btn').disabled = true;
            document.querySelectorAll('.provider-card').forEach(card => card.classList.remove('selected'));
        }
        
        function renderIndividualProviders() {
            const container = document.getElementById('individual-provider-list');
            container.innerHTML = '';
            
            individualProvidersData.forEach(provider => {
                const card = document.createElement('div');
                card.className = 'provider-card';
                card.onclick = () => selectProvider(provider.id, 'individual');
                
                const statusClass = provider.status === 'available' ? 'success' : 'warning';
                const statusText = provider.status === 'available' ? '可接单' : '忙碌中';
                
                card.innerHTML = `
                    <div class="provider-header">
                        <div class="provider-avatar">${provider.avatar}</div>
                        <div class="provider-info">
                            <h4>${provider.name}</h4>
                            <p>${provider.age}岁 · ${provider.experience}年经验 · ${provider.location}</p>
                        </div>
                    </div>
                    <div class="provider-stats">
                        <div class="stat-item">
                            <div class="stat-value">${provider.rating}</div>
                            <div class="stat-label">评分</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${provider.completedOrders}</div>
                            <div class="stat-label">完成订单</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${provider.hourlyRate}元</div>
                            <div class="stat-label">时薪</div>
                        </div>
                    </div>
                    <div class="provider-tags">
                        ${provider.skills.map(skill => `<span class="provider-tag">${skill}</span>`).join('')}
                        ${provider.tags.map(tag => `<span class="provider-tag ${tag.includes('平台自营') ? 'warning' : ''}">${tag}</span>`).join('')}
                        <span class="provider-tag ${statusClass}">${statusText}</span>
                    </div>
                `;
                
                container.appendChild(card);
            });
        }
        
        function renderAgencyProviders() {
            const container = document.getElementById('agency-provider-list');
            container.innerHTML = '';
            
            agencyProvidersData.forEach(provider => {
                const card = document.createElement('div');
                card.className = 'provider-card';
                card.onclick = () => selectProvider(provider.id, 'agency');
                
                const statusClass = provider.status === 'available' ? 'success' : 'warning';
                const statusText = provider.status === 'available' ? '可接单' : '忙碌中';
                
                card.innerHTML = `
                    <div class="provider-header">
                        <div class="provider-avatar">${provider.avatar}</div>
                        <div class="provider-info">
                            <h4>${provider.name}</h4>
                            <p>${provider.staffCount}名员工 · ${provider.location}</p>
                        </div>
                    </div>
                    <div class="provider-stats">
                        <div class="stat-item">
                            <div class="stat-value">${provider.rating}</div>
                            <div class="stat-label">评分</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${provider.completedOrders}</div>
                            <div class="stat-label">完成订单</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${provider.staffCount}</div>
                            <div class="stat-label">员工数</div>
                        </div>
                    </div>
                    <div class="provider-tags">
                        ${provider.services.map(service => `<span class="provider-tag">${service}</span>`).join('')}
                        ${provider.tags.map(tag => `<span class="provider-tag ${tag.includes('平台自营') ? 'warning' : ''}">${tag}</span>`).join('')}
                        <span class="provider-tag ${statusClass}">${statusText}</span>
                    </div>
                `;
                
                container.appendChild(card);
            });
        }
        
        function selectProvider(providerId, type) {
            selectedProviderId = providerId;
            currentDispatchType = type;
            
            // 更新选择状态
            document.querySelectorAll('.provider-card').forEach(card => card.classList.remove('selected'));
            event.currentTarget.classList.add('selected');
            
            // 启用确认按钮
            document.getElementById('confirm-dispatch-btn').disabled = false;
        }
        
        function filterIndividualProviders() {
            const searchTerm = document.getElementById('individual-search').value.toLowerCase();
            const cards = document.querySelectorAll('#individual-provider-list .provider-card');
            
            cards.forEach(card => {
                const text = card.textContent.toLowerCase();
                card.style.display = text.includes(searchTerm) ? 'block' : 'none';
            });
        }
        
        function filterAgencyProviders() {
            const searchTerm = document.getElementById('agency-search').value.toLowerCase();
            const cards = document.querySelectorAll('#agency-provider-list .provider-card');
            
            cards.forEach(card => {
                const text = card.textContent.toLowerCase();
                card.style.display = text.includes(searchTerm) ? 'block' : 'none';
            });
        }
        
        function filterBySkill(skill) {
            // 更新标签状态
            document.querySelectorAll('[data-skill]').forEach(tag => tag.classList.remove('active'));
            event.target.classList.add('active');
            
            const cards = document.querySelectorAll('#individual-provider-list .provider-card');
            
            cards.forEach(card => {
                if (skill === 'all') {
                    card.style.display = 'block';
                } else {
                    const hasSkill = card.textContent.includes(skill);
                    card.style.display = hasSkill ? 'block' : 'none';
                }
            });
        }
        
        function filterByService(service) {
            // 更新标签状态
            document.querySelectorAll('[data-service]').forEach(tag => tag.classList.remove('active'));
            event.target.classList.add('active');
            
            const cards = document.querySelectorAll('#agency-provider-list .provider-card');
            
            cards.forEach(card => {
                if (service === 'all') {
                    card.style.display = 'block';
                } else {
                    const hasService = card.textContent.includes(service);
                    card.style.display = hasService ? 'block' : 'none';
                }
            });
        }
        
        function confirmDispatch() {
            if (!selectedProviderId || !currentDispatchOrderId) {
                alert('请选择服务人员');
                return;
            }
            
            const order = domesticOrdersData.find(o => o.id === currentDispatchOrderId);
            const provider = currentDispatchType === 'individual' 
                ? individualProvidersData.find(p => p.id === selectedProviderId)
                : agencyProvidersData.find(p => p.id === selectedProviderId);
            
            if (!order || !provider) {
                alert('数据错误');
                return;
            }
            
            // 更新订单状态
            order.status = '已派单';
            order.assignedProvider = {
                id: provider.id,
                name: provider.name,
                type: currentDispatchType,
                phone: provider.phone,
                assignedTime: new Date().toLocaleString()
            };
            
            // 记录操作日志
            const logEntry = {
                type: '派单',
                operator: '系统管理员',
                time: new Date().toLocaleString(),
                changes: {
                    status: {
                        before: '待派单',
                        after: '已派单'
                    },
                    assignedProvider: {
                        before: null,
                        after: `${provider.name} (${currentDispatchType === 'individual' ? '个人阿姨' : '服务机构'})`
                    }
                },
                comment: `派单给${currentDispatchType === 'individual' ? '个人阿姨' : '服务机构'}: ${provider.name}`
            };
            
            if (!order.operationLogs) {
                order.operationLogs = [];
            }
            order.operationLogs.unshift(logEntry);
            
            // 重新渲染订单列表
            renderDomesticOrders();
            
            // 关闭抽屉
            closeDispatchDrawer();
            
            // 显示成功提示
            showNotification('派单成功', 'success');
        }

        // 通知提示函数
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-size: 14px;
                z-index: 10000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 300px;
                word-wrap: break-word;
            `;
            
            // 根据类型设置背景色
            const colors = {
                success: '#27ae60',
                warning: '#f39c12',
                error: '#e74c3c',
                info: '#3498db'
            };
            notification.style.backgroundColor = colors[type] || colors.info;
            
            notification.textContent = message;
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 家政服务订单详情Tab页切换函数
        function switchDomesticDetailTab(tabName, orderId) {
            // 隐藏所有Tab内容
            document.querySelectorAll('.domestic-tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有Tab的active状态
            document.querySelectorAll('.business-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的Tab内容
            document.getElementById(`domestic-tab-${tabName}`).classList.add('active');
            
            // 激活选中的Tab
            event.target.classList.add('active');
            
            // 如果是任务列表Tab，初始化任务数据
            if (tabName === 'tasks') {
                initializeTasksForOrder(orderId);
            }
        }

        // 初始化订单的任务数据
        function initializeTasksForOrder(orderId) {
            const order = domesticOrdersData.find(o => o.id === orderId);
            if (!order) return;
            
            // 如果订单还没有任务数据，生成默认任务
            if (!order.tasks) {
                generateTasksForOrder(orderId);
            }
            
            // 更新执行人员筛选选项
            updateTaskAuntieFilter(order);
            
            // 渲染任务表格
            renderTasksTable(order);
        }

        // 生成订单的任务列表
        function generateTasksForOrder(orderId) {
            const order = domesticOrdersData.find(o => o.id === orderId);
            if (!order) return;
            
            const tasks = [];
            const taskProgress = order.taskProgress || { completed: 0, total: 1 };
            const totalTasks = taskProgress.total;
            
            // 根据服务类型生成任务
            for (let i = 1; i <= totalTasks; i++) {
                const taskDate = new Date(order.appointmentTime);
                if (order.serviceType.includes('月嫂') || order.serviceType.includes('育儿嫂') || order.serviceType.includes('保姆')) {
                    // 连续服务，每天一个任务
                    taskDate.setDate(taskDate.getDate() + i - 1);
                } else if (order.serviceType.includes('小时工')) {
                    // 小时工，按小时计算
                    taskDate.setHours(taskDate.getHours() + (i - 1) * 4);
                } else {
                    // 单次服务
                    taskDate.setDate(taskDate.getDate() + i - 1);
                }
                
                const task = {
                    taskId: `${orderId}-TASK-${i.toString().padStart(3, '0')}`,
                    orderId: orderId,
                    taskSequence: i,
                    serviceDate: taskDate.toISOString().split('T')[0],
                    status: i <= taskProgress.completed ? '已完成' : (i <= taskProgress.completed + 1 ? '待执行' : '待指派'),
                    assignedAuntieId: order.servicePerson || null,
                    assignedAuntieName: order.servicePerson || null,
                    completedByAuntieId: i <= taskProgress.completed ? (order.servicePerson || null) : null,
                    completedByAuntieName: i <= taskProgress.completed ? (order.servicePerson || null) : null,
                    completionTime: i <= taskProgress.completed ? new Date(taskDate).toLocaleString() : null,
                    checkInLocation: i <= taskProgress.completed ? (order.serviceAddress || '客户地址') : null,
                    completionProof: i <= taskProgress.completed ? '已上传' : null,
                    remarks: i <= taskProgress.completed ? '任务已完成' : '',
                    serviceContent: getServiceContentByType(order.serviceType, i)
                };
                
                tasks.push(task);
            }
            
            order.tasks = tasks;
            
            // 重新渲染任务表格
            renderTasksTable(order);
            
            showNotification('任务生成成功', 'success');
        }

        // 根据服务类型获取服务内容
        function getServiceContentByType(serviceType, taskIndex) {
            const contentMap = {
                '月嫂服务': [
                    '产妇护理、新生儿护理、母乳喂养指导',
                    '产妇营养餐制作、新生儿护理、产后恢复指导',
                    '产妇护理、新生儿护理、家庭卫生清洁',
                    '产妇营养餐制作、新生儿护理、育儿知识指导',
                    '产妇护理、新生儿护理、母乳喂养指导'
                ],
                '育儿嫂服务': [
                    '婴幼儿日常护理、辅食制作、早教活动',
                    '婴幼儿护理、营养餐制作、卫生清洁',
                    '婴幼儿护理、早教活动、安全监护',
                    '婴幼儿护理、辅食制作、家庭卫生',
                    '婴幼儿护理、早教活动、营养餐制作'
                ],
                '小时工': [
                    '家庭清洁、整理收纳',
                    '厨房清洁、餐具消毒',
                    '卫生间清洁、垃圾处理',
                    '客厅清洁、物品整理'
                ],
                '深度保洁': ['全屋深度清洁、家具保养、消毒除菌'],
                '家电清洗': ['空调清洗、冰箱除味、洗衣机清洁'],
                '家务保姆': [
                    '日常清洁、洗衣做饭',
                    '家庭清洁、营养餐制作',
                    '卫生清洁、物品整理',
                    '日常护理、家庭服务'
                ]
            };
            
            const contents = contentMap[serviceType] || ['家政服务'];
            return contents[(taskIndex - 1) % contents.length];
        }

        // 生成任务表格HTML
        function generateTasksTableHtml(order) {
            if (!order.tasks || order.tasks.length === 0) {
                return '<div style="text-align: center; padding: 40px; color: var(--gray);">暂无任务数据，请先生成任务</div>';
            }
            
            return `
                <table class="task-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="select-all-tasks" onchange="toggleAllTasks()"></th>
                            <th>任务ID</th>
                            <th>任务序号</th>
                            <th>计划服务日期</th>
                            <th>计划服务内容</th>
                            <th>任务状态</th>
                            <th>当前服务人员</th>
                            <th>最终完成人员</th>
                            <th>完成时间</th>
                            <th>打卡地点</th>
                            <th>完成凭证</th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${order.tasks.map(task => `
                            <tr data-task-id="${task.taskId}">
                                <td><input type="checkbox" class="task-checkbox" value="${task.taskId}"></td>
                                <td><span class="task-id">${task.taskId}</span></td>
                                <td><span class="task-sequence">第${task.taskSequence}次/共${order.tasks.length}次</span></td>
                                <td>${task.serviceDate}</td>
                                <td>${task.serviceContent}</td>
                                <td><span class="task-status task-status-${getTaskStatusClass(task.status)}">${task.status}</span></td>
                                <td>${task.assignedAuntieName || '-'}</td>
                                <td>${task.completedByAuntieName || '-'}</td>
                                <td>${task.completionTime || '-'}</td>
                                <td>${task.checkInLocation || '-'}</td>
                                <td>${task.completionProof === '已上传' ? `<span class="proof-link" onclick="viewCompletionProof('${task.taskId}')" style="color: var(--primary); cursor: pointer; text-decoration: underline;">查看凭证</span>` : (task.completionProof || '-')}</td>
                                <td>${task.remarks || '-'}</td>
                                <td>
                                    <div class="task-actions">
                                        ${task.status === '待指派' ? `<button class="btn-assign" onclick="assignTask('${task.taskId}')">指派</button>` : ''}
                                        ${task.status === '待执行' ? `<button class="btn-complete" onclick="completeTask('${task.taskId}')">完成</button>` : ''}
                                        ${task.status !== '已完成' && task.status !== '已取消' ? `<button class="btn-cancel" onclick="cancelTask('${task.taskId}')">取消</button>` : ''}
                                        <button class="btn-edit" onclick="editTask('${task.taskId}')">编辑</button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        }

        // 渲染任务表格
        function renderTasksTable(order) {
            const container = document.getElementById('domestic-tasks-table-container');
            if (container) {
                container.innerHTML = generateTasksTableHtml(order);
            }
        }

        // 获取任务状态对应的CSS类
        function getTaskStatusClass(status) {
            const statusMap = {
                '待指派': 'pending',
                '待执行': 'assigned',
                '已完成': 'completed',
                '已取消': 'cancelled'
            };
            return statusMap[status] || 'pending';
        }

        // 更新任务执行人员筛选选项
        function updateTaskAuntieFilter(order) {
            const filter = document.getElementById('task-auntie-filter');
            if (!filter) return;
            
            // 清空现有选项
            filter.innerHTML = '<option value="">全部人员</option>';
            
            // 获取所有任务中的执行人员
            const aunties = new Set();
            if (order.tasks) {
                order.tasks.forEach(task => {
                    if (task.assignedAuntieName) {
                        aunties.add(task.assignedAuntieName);
                    }
                    if (task.completedByAuntieName) {
                        aunties.add(task.completedByAuntieName);
                    }
                });
            }
            
            // 添加执行人员选项
            aunties.forEach(auntie => {
                const option = document.createElement('option');
                option.value = auntie;
                option.textContent = auntie;
                filter.appendChild(option);
            });
        }

        // 筛选任务
        function filterTasks(orderId) {
            const order = domesticOrdersData.find(o => o.id === orderId);
            if (!order || !order.tasks) return;
            
            const statusFilter = document.getElementById('task-status-filter').value;
            const auntieFilter = document.getElementById('task-auntie-filter').value;
            const dateStart = document.getElementById('task-date-start').value;
            const dateEnd = document.getElementById('task-date-end').value;
            
            const filteredTasks = order.tasks.filter(task => {
                // 状态筛选
                if (statusFilter && task.status !== statusFilter) return false;
                
                // 执行人员筛选
                if (auntieFilter && task.assignedAuntieName !== auntieFilter && task.completedByAuntieName !== auntieFilter) return false;
                
                // 日期范围筛选
                if (dateStart && task.serviceDate < dateStart) return false;
                if (dateEnd && task.serviceDate > dateEnd) return false;
                
                return true;
            });
            
            // 渲染筛选后的任务
            renderFilteredTasksTable(order, filteredTasks);
        }

        // 渲染筛选后的任务表格
        function renderFilteredTasksTable(order, filteredTasks) {
            const container = document.getElementById('domestic-tasks-table-container');
            if (!container) return;
            
            if (filteredTasks.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: var(--gray);">暂无符合条件的任务</div>';
                return;
            }
            
            container.innerHTML = `
                <table class="task-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="select-all-tasks" onchange="toggleAllTasks()"></th>
                            <th>任务ID</th>
                            <th>任务序号</th>
                            <th>计划服务日期</th>
                            <th>计划服务内容</th>
                            <th>任务状态</th>
                            <th>当前服务人员</th>
                            <th>最终完成人员</th>
                            <th>完成时间</th>
                            <th>打卡地点</th>
                            <th>完成凭证</th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredTasks.map(task => `
                            <tr data-task-id="${task.taskId}">
                                <td><input type="checkbox" class="task-checkbox" value="${task.taskId}"></td>
                                <td><span class="task-id">${task.taskId}</span></td>
                                <td><span class="task-sequence">第${task.taskSequence}次/共${order.tasks.length}次</span></td>
                                <td>${task.serviceDate}</td>
                                <td>${task.serviceContent}</td>
                                <td><span class="task-status task-status-${getTaskStatusClass(task.status)}">${task.status}</span></td>
                                <td>${task.assignedAuntieName || '-'}</td>
                                <td>${task.completedByAuntieName || '-'}</td>
                                <td>${task.completionTime || '-'}</td>
                                <td>${task.checkInLocation || '-'}</td>
                                <td>${task.completionProof === '已上传' ? `<span class="proof-link" onclick="viewCompletionProof('${task.taskId}')" style="color: var(--primary); cursor: pointer; text-decoration: underline;">查看凭证</span>` : (task.completionProof || '-')}</td>
                                <td>${task.remarks || '-'}</td>
                                <td>
                                    <div class="task-actions">
                                        ${task.status === '待指派' ? `<button class="btn-assign" onclick="assignTask('${task.taskId}')">指派</button>` : ''}
                                        ${task.status === '待执行' ? `<button class="btn-complete" onclick="completeTask('${task.taskId}')">完成</button>` : ''}
                                        ${task.status !== '已完成' && task.status !== '已取消' ? `<button class="btn-cancel" onclick="cancelTask('${task.taskId}')">取消</button>` : ''}
                                        <button class="btn-edit" onclick="editTask('${task.taskId}')">编辑</button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        }

        // 全选/取消全选任务
        function toggleAllTasks() {
            const selectAll = document.getElementById('select-all-tasks');
            const checkboxes = document.querySelectorAll('.task-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        // 批量重指派任务
        function batchReassignTasks(orderId) {
            const selectedTasks = document.querySelectorAll('.task-checkbox:checked');
            if (selectedTasks.length === 0) {
                showNotification('请选择要重指派的任务', 'warning');
                return;
            }
            
            // 保存当前订单ID和选中的任务，用于后续确认
            window.currentBatchReassignOrderId = orderId;
            window.currentBatchReassignTaskIds = Array.from(selectedTasks).map(checkbox => checkbox.value);
            
            // 打开阿姨选择弹窗
            openAuntieSelectModal();
        }

        // 更新任务进度
        function updateTaskProgress(order) {
            if (!order.tasks) return;
            
            const completedTasks = order.tasks.filter(task => task.status === '已完成').length;
            const totalTasks = order.tasks.length;
            
            if (!order.taskProgress) {
                order.taskProgress = { completed: 0, total: totalTasks };
            }
            
            order.taskProgress.completed = completedTasks;
            order.taskProgress.total = totalTasks;
        }

        // 指派任务
        function assignTask(taskId) {
            // 保存当前任务ID，用于后续确认
            window.currentAssignTaskId = taskId;
            
            // 打开阿姨选择弹窗
            openAuntieSelectModal();
        }

        // 完成任务
        function completeTask(taskId) {
            // 保存当前任务ID
            document.getElementById('complete-task-id').value = taskId;
            
            // 获取订单和任务信息
            const order = findOrderByTaskId(taskId);
            if (!order) {
                showNotification('订单不存在', 'error');
                return;
            }
            
            const task = order.tasks.find(t => t.taskId === taskId);
            if (!task) {
                showNotification('任务不存在', 'error');
                return;
            }
            
            // 填充完成人员选项
            const auntieSelect = document.getElementById('complete-auntie-select');
            auntieSelect.innerHTML = '<option value="">请选择完成人员</option>';
            
            // 添加当前指派的人员
            if (task.assignedAuntieName) {
                const option = document.createElement('option');
                option.value = task.assignedAuntieName;
                option.textContent = task.assignedAuntieName;
                auntieSelect.appendChild(option);
            }
            
            // 添加所有可用阿姨
            individualProvidersData.forEach(auntie => {
                if (auntie.status === 'available' && auntie.name !== task.assignedAuntieName) {
                    const option = document.createElement('option');
                    option.value = auntie.name;
                    option.textContent = auntie.name;
                    auntieSelect.appendChild(option);
                }
            });
            
            // 设置默认完成时间
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            document.getElementById('complete-time').value = localDateTime;
            
            // 设置默认打卡地点
            document.getElementById('complete-location').value = order.serviceAddress || '';
            
            // 清空其他字段
            document.getElementById('complete-proof').value = '';
            document.getElementById('complete-remarks').value = '';
            
            // 显示弹窗
            document.getElementById('complete-task-modal').style.display = 'flex';
            document.querySelector('.overlay').classList.add('active');
        }

        // 取消任务
        function cancelTask(taskId) {
            if (!confirm('确定要取消这个任务吗？')) return;
            
            const order = findOrderByTaskId(taskId);
            if (!order) return;
            
            const task = order.tasks.find(t => t.taskId === taskId);
            if (task) {
                const oldStatus = task.status;
                task.status = '已取消';
                task.remarks = '任务已取消';
                
                // 记录操作日志
                addOperationLog('domestic', order.id, 'cancel_task', [
                    { field: '任务状态', oldValue: oldStatus, newValue: '已取消' }
                ], `取消任务${task.taskSequence}`);
                
                renderTasksTable(order);
                showNotification('任务已取消', 'info');
            }
        }

        // 编辑任务
        function editTask(taskId) {
            const order = findOrderByTaskId(taskId);
            if (!order) return;
            
            const task = order.tasks.find(t => t.taskId === taskId);
            if (task) {
                const newRemarks = prompt('请输入新的备注信息：', task.remarks || '');
                if (newRemarks !== null) {
                    const oldRemarks = task.remarks || '';
                    task.remarks = newRemarks;
                    
                    // 记录操作日志
                    addOperationLog('domestic', order.id, 'edit_task', [
                        { field: '备注信息', oldValue: oldRemarks, newValue: newRemarks }
                    ], `编辑任务${task.taskSequence}备注`);
                    
                    renderTasksTable(order);
                    showNotification('任务备注编辑成功', 'success');
                }
            }
        }

        // 根据任务ID查找订单
        function findOrderByTaskId(taskId) {
            return domesticOrdersData.find(order => 
                order.tasks && order.tasks.some(task => task.taskId === taskId)
            );
        }

        // 阿姨选择弹窗相关函数
        let selectedAuntieId = null;
        let selectedAuntieName = null;

        // 打开阿姨选择弹窗
        function openAuntieSelectModal() {
            selectedAuntieId = null;
            selectedAuntieName = null;
            document.getElementById('confirm-auntie-select').disabled = true;
            
            // 渲染阿姨列表
            renderAuntieList();
            
            // 显示弹窗
            document.getElementById('auntie-select-modal').style.display = 'flex';
            document.querySelector('.overlay').classList.add('active');
        }

        // 关闭阿姨选择弹窗
        function closeAuntieSelectModal() {
            document.getElementById('auntie-select-modal').style.display = 'none';
            document.querySelector('.overlay').classList.remove('active');
            selectedAuntieId = null;
            selectedAuntieName = null;
        }

        // 渲染阿姨列表
        function renderAuntieList() {
            const auntieList = document.getElementById('auntie-list');
            const availableAunties = individualProvidersData.filter(auntie => auntie.status === 'available');
            
            auntieList.innerHTML = availableAunties.map(auntie => `
                <div class="auntie-item" onclick="selectAuntie('${auntie.id}', '${auntie.name}')" style="
                    padding: 15px;
                    border: 1px solid var(--border);
                    border-radius: 8px;
                    margin-bottom: 10px;
                    cursor: pointer;
                    transition: all 0.3s;
                    background: white;
                " data-auntie-id="${auntie.id}">
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <div style="
                            width: 50px;
                            height: 50px;
                            border-radius: 50%;
                            background: var(--primary);
                            color: white;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 18px;
                            font-weight: bold;
                        ">${auntie.avatar}</div>
                        <div style="flex: 1;">
                            <div style="font-weight: 600; font-size: 16px; margin-bottom: 5px;">${auntie.name}</div>
                            <div style="color: var(--gray); font-size: 14px; margin-bottom: 5px;">
                                ${auntie.age}岁 · ${auntie.experience}年经验 · 评分${auntie.rating}
                            </div>
                            <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                                ${auntie.skills.map(skill => `<span style="
                                    background: rgba(52, 152, 219, 0.1);
                                    color: var(--primary);
                                    padding: 2px 8px;
                                    border-radius: 12px;
                                    font-size: 11px;
                                ">${skill}</span>`).join('')}
                            </div>
                        </div>
                        <div style="text-align: right;">
                            <div style="color: var(--gray); font-size: 12px;">已完成${auntie.completedOrders}单</div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 选择阿姨
        function selectAuntie(auntieId, auntieName) {
            // 移除之前的选择状态
            document.querySelectorAll('.auntie-item').forEach(item => {
                item.style.borderColor = 'var(--border)';
                item.style.backgroundColor = 'white';
            });
            
            // 设置当前选择状态
            const selectedItem = document.querySelector(`[data-auntie-id="${auntieId}"]`);
            if (selectedItem) {
                selectedItem.style.borderColor = 'var(--primary)';
                selectedItem.style.backgroundColor = 'rgba(52, 152, 219, 0.05)';
            }
            
            selectedAuntieId = auntieId;
            selectedAuntieName = auntieName;
            
            // 启用确认按钮
            document.getElementById('confirm-auntie-select').disabled = false;
        }

        // 确认阿姨选择
        function confirmAuntieSelection() {
            if (!selectedAuntieId || !selectedAuntieName) {
                showNotification('请选择阿姨', 'warning');
                return;
            }
            
            // 处理批量重指派
            if (window.currentBatchReassignOrderId && window.currentBatchReassignTaskIds) {
                const orderId = window.currentBatchReassignOrderId;
                const taskIds = window.currentBatchReassignTaskIds;
                
                const order = domesticOrdersData.find(o => o.id === orderId);
                if (!order) {
                    showNotification('订单不存在', 'error');
                    return;
                }
                
                let reassignedCount = 0;
                taskIds.forEach(taskId => {
                    const task = order.tasks.find(t => t.taskId === taskId);
                    if (task && task.status === '待执行') {
                        task.assignedAuntieName = selectedAuntieName;
                        task.assignedAuntieId = selectedAuntieId;
                        reassignedCount++;
                    }
                });
                
                if (reassignedCount > 0) {
                    // 更新任务进度
                    updateTaskProgress(order);
                    
                    // 记录操作日志
                    addOperationLog('domestic', orderId, 'batch_reassign', [], `批量重指派${reassignedCount}个任务给${selectedAuntieName}`);
                    
                    // 重新渲染任务表格
                    renderTasksTable(order);
                    
                    // 关闭弹窗
                    closeAuntieSelectModal();
                    
                    showNotification(`成功重指派${reassignedCount}个任务给${selectedAuntieName}`, 'success');
                } else {
                    showNotification('没有可重指派的任务', 'warning');
                }
                
                // 清理全局变量
                window.currentBatchReassignOrderId = null;
                window.currentBatchReassignTaskIds = null;
            }
            // 处理单个任务指派
            else if (window.currentAssignTaskId) {
                const taskId = window.currentAssignTaskId;
                const order = findOrderByTaskId(taskId);
                
                if (!order) {
                    showNotification('订单不存在', 'error');
                    return;
                }
                
                const task = order.tasks.find(t => t.taskId === taskId);
                if (task) {
                    task.assignedAuntieName = selectedAuntieName;
                    task.assignedAuntieId = selectedAuntieId;
                    task.status = '待执行';
                    
                    // 记录操作日志
                    addOperationLog('domestic', order.id, 'assign_task', [
                        { field: '任务状态', oldValue: '待指派', newValue: '待执行' },
                        { field: '执行人员', oldValue: '', newValue: selectedAuntieName }
                    ], `指派任务${task.taskSequence}给${selectedAuntieName}`);
                    
                    renderTasksTable(order);
                    
                    // 关闭弹窗
                    closeAuntieSelectModal();
                    
                    showNotification('任务指派成功', 'success');
                }
                
                // 清理全局变量
                window.currentAssignTaskId = null;
            }
        }

        // 筛选阿姨
        function filterAunties() {
            const searchTerm = document.getElementById('auntie-search').value.toLowerCase();
            const auntieItems = document.querySelectorAll('.auntie-item');
            
            auntieItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                item.style.display = text.includes(searchTerm) ? 'block' : 'none';
            });
        }

        // 查看完成凭证
        function viewCompletionProof(taskId) {
            const order = findOrderByTaskId(taskId);
            if (!order) {
                showNotification('订单不存在', 'error');
                return;
            }
            
            const task = order.tasks.find(t => t.taskId === taskId);
            if (!task) {
                showNotification('任务不存在', 'error');
                return;
            }
            
            // 模拟图片URL（实际项目中应该是真实的图片URL）
            const imageUrl = `https://via.placeholder.com/600x400/3498db/ffffff?text=任务${task.taskId}完成凭证`;
            
            // 显示图片
            const container = document.getElementById('proof-image-container');
            container.innerHTML = `
                <div style="margin-bottom: 15px;">
                    <strong>任务信息：</strong>${task.taskSequence} - ${task.serviceContent}
                </div>
                <img src="${imageUrl}" alt="完成凭证" style="max-width: 100%; max-height: 500px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                <div style="margin-top: 15px; color: var(--gray); font-size: 14px;">
                    完成时间：${task.completionTime || '-'}<br>
                    完成人员：${task.completedByAuntieName || '-'}<br>
                    打卡地点：${task.checkInLocation || '-'}
                </div>
            `;
            
            // 显示弹窗
            document.getElementById('proof-image-modal').style.display = 'flex';
            document.querySelector('.overlay').classList.add('active');
        }

        // 关闭完成凭证图片弹窗
        function closeProofImageModal() {
            document.getElementById('proof-image-modal').style.display = 'none';
            document.querySelector('.overlay').classList.remove('active');
        }

        // 关闭完成任务弹窗
        function closeCompleteTaskModal() {
            document.getElementById('complete-task-modal').style.display = 'none';
            document.querySelector('.overlay').classList.remove('active');
        }

        // 提交完成任务
        function submitCompleteTask() {
            const taskId = document.getElementById('complete-task-id').value;
            const completedByAuntieName = document.getElementById('complete-auntie-select').value;
            const completionTime = document.getElementById('complete-time').value;
            const checkInLocation = document.getElementById('complete-location').value;
            const completionProof = document.getElementById('complete-proof').files[0];
            const remarks = document.getElementById('complete-remarks').value;

            // 验证必填字段
            if (!completedByAuntieName) {
                showNotification('请选择最终完成人员', 'warning');
                return;
            }
            if (!completionTime) {
                showNotification('请选择完成时间', 'warning');
                return;
            }
            if (!checkInLocation) {
                showNotification('请输入打卡地点', 'warning');
                return;
            }
            if (!completionProof) {
                showNotification('请上传完成凭证', 'warning');
                return;
            }

            // 获取订单和任务
            const order = findOrderByTaskId(taskId);
            if (!order) {
                showNotification('订单不存在', 'error');
                return;
            }

            const task = order.tasks.find(t => t.taskId === taskId);
            if (!task) {
                showNotification('任务不存在', 'error');
                return;
            }

            // 模拟文件上传（实际项目中应该上传到服务器）
            const fileName = completionProof.name;
            
            // 更新任务信息
            task.status = '已完成';
            task.completedByAuntieName = completedByAuntieName;
            task.completedByAuntieId = completedByAuntieName; // 简化处理
            task.completionTime = new Date(completionTime).toLocaleString();
            task.checkInLocation = checkInLocation;
            task.completionProof = '已上传';
            task.remarks = remarks || '任务已完成';

            // 更新任务进度
            updateTaskProgress(order);

            // 记录操作日志
            addOperationLog('domestic', order.id, 'complete_task', [
                { field: '任务状态', oldValue: '待执行', newValue: '已完成' },
                { field: '最终完成人员', oldValue: '', newValue: completedByAuntieName },
                { field: '完成时间', oldValue: '', newValue: task.completionTime },
                { field: '打卡地点', oldValue: '', newValue: checkInLocation },
                { field: '完成凭证', oldValue: '', newValue: fileName },
                { field: '备注', oldValue: '', newValue: remarks || '任务已完成' }
            ], `完成任务${task.taskSequence}`);

            // 关闭弹窗
            closeCompleteTaskModal();

            // 重新渲染任务表格
            renderTasksTable(order);

            showNotification('任务完成成功', 'success');
        }
    </script>
</body>
</html>