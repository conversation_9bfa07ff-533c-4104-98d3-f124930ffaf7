package cn.bztmaster.cnt.module.system.controller.app.notify.vo.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "用户 App - 标记站内信已读 Request VO")
@Data
public class AppNotifyMessageUpdateReadReqVO {

    @Schema(description = "站内信编号列表", requiredMode = Schema.RequiredMode.REQUIRED, example = "[1024, 2048]")
    @NotEmpty(message = "站内信编号列表不能为空")
    private List<Long> ids;

    @Schema(description = "用户唯一标识ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "wx_123456789")
    @NotNull(message = "用户唯一标识ID不能为空")
    private String oneId;
}
