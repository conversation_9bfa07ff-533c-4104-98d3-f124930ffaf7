package cn.bztmaster.cnt.module.system.controller.app.notify;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.system.controller.app.notify.vo.message.AppNotifyMessagePageReqVO;
import cn.bztmaster.cnt.module.system.controller.app.notify.vo.message.AppNotifyMessageRespVO;
import cn.bztmaster.cnt.module.system.dal.dataobject.notify.NotifyMessageDO;
import cn.bztmaster.cnt.module.system.service.notify.NotifyMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 App - 站内信")
@RestController
@RequestMapping("/system/notify-message")
@Validated
public class AppNotifyMessageController {

    @Resource
    private NotifyMessageService notifyMessageService;

    @GetMapping("/page")
    @Operation(summary = "获得我的站内信分页")
    @PermitAll
    public CommonResult<PageResult<AppNotifyMessageRespVO>> getMyNotifyMessagePage(@Valid AppNotifyMessagePageReqVO pageVO) {
        // 使用oneId查询站内信，而不是userId
        PageResult<NotifyMessageDO> pageResult = notifyMessageService.getNotifyMessagePageByOneId(pageVO);
        return success(BeanUtils.toBean(pageResult, AppNotifyMessageRespVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "获得站内信")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "oneId", description = "用户唯一标识ID", required = true, example = "wx_123456789")
    @PermitAll
    public CommonResult<AppNotifyMessageRespVO> getNotifyMessage(@RequestParam("id") Long id, 
                                                               @RequestParam("oneId") String oneId) {
        NotifyMessageDO message = notifyMessageService.getNotifyMessageByIdAndOneId(id, oneId);
        return success(BeanUtils.toBean(message, AppNotifyMessageRespVO.class));
    }

    @PutMapping("/update-read")
    @Operation(summary = "标记站内信为已读")
    @Parameter(name = "ids", description = "编号列表，多个ID用逗号分隔", required = true, example = "1024,2048")
    @Parameter(name = "oneId", description = "用户唯一标识ID", required = true, example = "wx_123456789")
    @PermitAll
    public CommonResult<Boolean> updateNotifyMessageRead(@RequestParam("ids") String idsStr,
                                                       @RequestParam("oneId") String oneId) {
        // 将逗号分隔的字符串转换为List<Long>
        List<Long> ids = Arrays.stream(idsStr.split(","))
                .map(String::trim)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        notifyMessageService.updateNotifyMessageReadByOneId(ids, oneId);
        return success(Boolean.TRUE);
    }

    @PutMapping("/update-all-read")
    @Operation(summary = "标记所有站内信为已读")
    @Parameter(name = "oneId", description = "用户唯一标识ID", required = true, example = "wx_123456789")
    public CommonResult<Boolean> updateAllNotifyMessageRead(@RequestParam("oneId") String oneId) {
        notifyMessageService.updateAllNotifyMessageReadByOneId(oneId);
        return success(Boolean.TRUE);
    }

    @GetMapping("/get-unread-list")
    @Operation(summary = "获取当前用户的最新站内信列表，默认 10 条")
    @Parameter(name = "oneId", description = "用户唯一标识ID", required = true, example = "wx_123456789")
    @Parameter(name = "size", description = "获取数量", example = "10")
    @PermitAll
    public CommonResult<List<AppNotifyMessageRespVO>> getUnreadNotifyMessageList(
            @RequestParam("oneId") String oneId,
            @RequestParam(name = "size", defaultValue = "10") Integer size) {
        List<NotifyMessageDO> list = notifyMessageService.getUnreadNotifyMessageListByOneId(oneId, size);
        return success(BeanUtils.toBean(list, AppNotifyMessageRespVO.class));
    }

    @GetMapping("/get-unread-count")
    @Operation(summary = "获得当前用户的未读站内信数量")
    @Parameter(name = "oneId", description = "用户唯一标识ID", required = true, example = "wx_123456789")
    @PermitAll
    public CommonResult<Long> getUnreadNotifyMessageCount(@RequestParam("oneId") String oneId) {
        return success(notifyMessageService.getUnreadNotifyMessageCountByOneId(oneId));
    }

} 