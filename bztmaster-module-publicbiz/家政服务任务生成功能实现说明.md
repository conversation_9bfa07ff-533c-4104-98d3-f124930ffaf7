# 家政服务任务生成功能实现说明

## 功能概述

根据订单主表和家政订单详情表的数据，自动生成家政服务任务并存储到家政服务任务表中。支持两种套餐类型的任务生成逻辑：

1. **长周期套餐任务生成**：根据服务周期和服务频次自动计算任务数量
2. **次数次卡套餐任务生成**：根据服务次数和预约时间生成对应数量的任务

## 实现文件列表

### 1. Controller层
- **文件**: `DomesticTaskOrderController.java`
- **路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/admin/order/`
- **功能**: 提供RESTful API接口，处理任务生成相关的HTTP请求

### 2. Service层
- **接口**: `DomesticTaskService.java`
- **实现**: `DomesticTaskServiceImpl.java`
- **路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/service/domestic/`
- **功能**: 实现任务生成的核心业务逻辑

### 3. VO层
- **请求VO**: `DomesticTaskGenerateReqVO.java`
- **响应VO**: `DomesticTaskGenerateRespVO.java`
- **路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/admin/order/vo/`
- **功能**: 定义请求参数和响应数据结构

### 4. 测试文件
- **单元测试**: `DomesticTaskServiceTest.java`
- **接口测试**: `DomesticTaskOrderController.http`
- **功能**: 验证任务生成功能的正确性

## 核心接口说明

### 1. 生成家政服务任务
- **接口路径**: `POST /publicbiz/domestic-task-order/generate-tasks`
- **功能**: 根据订单信息生成家政服务任务
- **权限**: `publicbiz:domestic-task-order:generate`

**请求参数**:
```json
{
  "orderId": 1024,
  "forceRegenerate": false
}
```

**响应结果**:
```json
{
  "code": 0,
  "data": {
    "orderId": 1024,
    "orderNo": "JZ20250814001",
    "packageType": "long-term",
    "packageName": "30天每日保洁服务",
    "taskCount": 30,
    "taskIds": [1, 2, 3, ...],
    "detail": {
      "servicePeriod": 30,
      "serviceFrequency": "每日",
      "serviceTimes": 30,
      "generateRule": "根据30天每日服务规则，生成30个任务",
      "startDate": "2025-08-14",
      "endDate": "2025-09-13"
    }
  }
}
```

### 2. 查询任务列表
- **接口路径**: `GET /publicbiz/domestic-task-order/tasks/by-order/{orderId}`
- **功能**: 根据订单ID查询任务列表
- **权限**: `publicbiz:domestic-task-order:query`

### 3. 检查任务生成状态
- **接口路径**: `GET /publicbiz/domestic-task-order/tasks/check-generated/{orderId}`
- **功能**: 检查订单是否已生成任务
- **权限**: `publicbiz:domestic-task-order:query`

### 4. 删除订单任务
- **接口路径**: `DELETE /publicbiz/domestic-task-order/tasks/by-order/{orderId}`
- **功能**: 删除订单相关的所有任务
- **权限**: `publicbiz:domestic-task-order:delete`

## 业务逻辑详解

### 长周期套餐任务生成逻辑

1. **数据来源**：
   - 服务周期：从 `service_package_duration` 字段获取（如：30天、26天）
   - 服务频次：从 `service_frequency` 字段获取（如：每日、每周）

2. **计算规则**：
   - 每日服务：任务数量 = 服务周期天数
   - 每周服务：任务数量 = (服务周期天数 + 6) / 7（向上取整）
   - 每月服务：任务数量 = (服务周期天数 + 29) / 30（向上取整）

3. **示例**：
   - 30天每日服务 → 生成30个任务
   - 26天每日服务 → 生成26个任务
   - 28天每周服务 → 生成4个任务

### 次数次卡套餐任务生成逻辑

1. **数据来源**：
   - 服务次数：从 `service_times` 字段获取
   - 预约时间：从 `service_schedule` JSON字段解析

2. **计算规则**：
   - 任务数量 = 服务次数
   - 排班日期根据预约时间安排确定

3. **示例**：
   - 4次上门收纳 → 生成4个任务
   - 6次深度保洁 → 生成6个任务

## 任务状态管理

所有生成的任务初始状态设置为 `"pending"`（待分配），等待后续分配给具体的服务人员。

任务状态枚举：
- `pending`: 待分配
- `assigned`: 已分配
- `in_progress`: 进行中
- `completed`: 已完成
- `cancelled`: 已取消

## 任务编号生成规则

任务编号格式：`JZ + YYYYMMDD + 8位序列号`

示例：`JZ2025081400000001`

## 异常处理

1. **订单不存在**：返回400错误，提示"订单不存在"
2. **家政订单详情不存在**：返回400错误，提示"家政订单详情不存在"
3. **任务已生成**：返回400错误，提示"该订单已生成任务"（除非设置强制重新生成）
4. **不支持的套餐类型**：返回400错误，提示"不支持的套餐类型"
5. **服务配置错误**：返回400错误，提示具体的配置错误信息

## 日志记录

使用 `@LogRecord` 注解记录关键操作：
- 任务生成操作
- 任务删除操作
- 记录操作人、操作时间、业务编号等信息

## 权限控制

所有接口都需要相应的权限：
- `publicbiz:domestic-task-order:generate`: 生成任务权限
- `publicbiz:domestic-task-order:query`: 查询任务权限
- `publicbiz:domestic-task-order:delete`: 删除任务权限

## 测试验证

### 单元测试覆盖
- 长周期套餐任务生成测试
- 次数次卡套餐任务生成测试
- 异常情况处理测试
- 边界条件测试

### 接口测试用例
- 正常任务生成流程
- 强制重新生成任务
- 参数验证测试
- 错误处理测试

## 使用示例

```bash
# 1. 生成长周期套餐任务
curl -X POST "http://localhost:8080/publicbiz/domestic-task-order/generate-tasks" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{"orderId": 1024, "forceRegenerate": false}'

# 2. 查询任务列表
curl -X GET "http://localhost:8080/publicbiz/domestic-task-order/tasks/by-order/1024" \
  -H "Authorization: Bearer your_token"

# 3. 检查任务生成状态
curl -X GET "http://localhost:8080/publicbiz/domestic-task-order/tasks/check-generated/1024" \
  -H "Authorization: Bearer your_token"
```

## 注意事项

1. **数据一致性**：任务生成过程使用事务保证数据一致性
2. **性能考虑**：大批量任务生成时建议分批处理
3. **并发控制**：避免同一订单并发生成任务
4. **数据校验**：严格校验输入参数和业务数据
5. **日志监控**：关键操作都有详细的日志记录，便于问题排查

## 扩展性

该实现具有良好的扩展性，可以方便地：
- 添加新的套餐类型支持
- 扩展任务生成规则
- 增加任务状态管理功能
- 集成任务调度和分配功能
