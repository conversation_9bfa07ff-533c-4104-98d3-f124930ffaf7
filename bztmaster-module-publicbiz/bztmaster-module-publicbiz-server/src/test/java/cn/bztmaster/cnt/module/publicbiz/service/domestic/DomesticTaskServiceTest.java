package cn.bztmaster.cnt.module.publicbiz.service.domestic;

import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.DomesticTaskGenerateRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic.DomesticTaskMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.DomesticOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.service.domestic.impl.DomesticTaskServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 家政服务任务生成测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class DomesticTaskServiceTest {

    @Mock
    private DomesticTaskMapper domesticTaskMapper;

    @Mock
    private PublicbizOrderMapper orderMapper;

    @Mock
    private DomesticOrderMapper domesticOrderMapper;

    @InjectMocks
    private DomesticTaskServiceImpl domesticTaskService;

    private PublicbizOrderDO mockOrder;
    private DomesticOrderDO mockDomesticOrder;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        mockOrder = new PublicbizOrderDO();
        mockOrder.setId(1024L);
        mockOrder.setOrderNo("JZ20250814001");
        mockOrder.setOrderType("domestic");
        mockOrder.setBusinessLine("家政服务");

        mockDomesticOrder = new DomesticOrderDO();
        mockDomesticOrder.setId(2021L);
        mockDomesticOrder.setOrderId(1024L);
        mockDomesticOrder.setOrderNo("JZ20250814001");
        mockDomesticOrder.setCustomerName("张三");
        mockDomesticOrder.setCustomerPhone("***********");
        mockDomesticOrder.setCustomerOneid("customer-001");
        mockDomesticOrder.setServiceAddress("北京市朝阳区测试地址");
        mockDomesticOrder.setServiceCategoryId(1L);
        mockDomesticOrder.setServiceCategoryName("家庭保洁");
        mockDomesticOrder.setServicePackageName("30天每日保洁服务");
        mockDomesticOrder.setServiceDescription("专业家庭保洁服务");
        mockDomesticOrder.setServiceDuration("4小时");
        mockDomesticOrder.setServiceFrequency("每日");
        mockDomesticOrder.setServiceStartDate(new Date());
    }

    @Test
    void testGenerateLongTermTasks() {
        // 准备长周期套餐数据
        mockDomesticOrder.setServicePackageType("long-term");
        mockDomesticOrder.setServicePackageDuration("30天");
        mockDomesticOrder.setServiceFrequency("每日");

        // Mock数据库查询
        when(domesticOrderMapper.selectByOrderId(1024L)).thenReturn(mockDomesticOrder);
        when(domesticTaskMapper.selectCount(anyString(), any())).thenReturn(0L);
        when(domesticOrderMapper.selectById(2021L)).thenReturn(mockDomesticOrder);

        // 执行测试
        DomesticTaskGenerateRespVO result = domesticTaskService.generateTasks(mockOrder);

        // 验证结果
        assertNotNull(result);
        assertEquals(1024L, result.getOrderId());
        assertEquals("JZ20250814001", result.getOrderNo());
        assertEquals("long-term", result.getPackageType());
        assertEquals("30天每日保洁服务", result.getPackageName());
        assertEquals(30, result.getTaskCount()); // 30天每日服务应该生成30个任务

        // 验证任务生成详情
        assertNotNull(result.getDetail());
        assertEquals(30, result.getDetail().getServicePeriod());
        assertEquals("每日", result.getDetail().getServiceFrequency());
        assertEquals(30, result.getDetail().getServiceTimes());
        assertTrue(result.getDetail().getGenerateRule().contains("30天每日服务规则"));

        // 验证数据库操作
        verify(domesticTaskMapper, times(30)).insert(any(DomesticTaskDO.class));
        verify(domesticOrderMapper).updateById(any(DomesticOrderDO.class));
    }

    @Test
    void testGenerateCountCardTasks() {
        // 准备次数次卡套餐数据
        mockDomesticOrder.setServicePackageType("count-card");
        mockDomesticOrder.setServiceTimes(4);
        mockDomesticOrder.setServicePackageName("4次上门收纳服务");
        mockDomesticOrder.setServiceSchedule("{\"dates\":[\"2025-08-14\",\"2025-08-21\",\"2025-08-28\",\"2025-09-04\"]}");

        // Mock数据库查询
        when(domesticOrderMapper.selectByOrderId(1024L)).thenReturn(mockDomesticOrder);
        when(domesticTaskMapper.selectCount(anyString(), any())).thenReturn(0L);
        when(domesticOrderMapper.selectById(2021L)).thenReturn(mockDomesticOrder);

        // 执行测试
        DomesticTaskGenerateRespVO result = domesticTaskService.generateTasks(mockOrder);

        // 验证结果
        assertNotNull(result);
        assertEquals(1024L, result.getOrderId());
        assertEquals("count-card", result.getPackageType());
        assertEquals(4, result.getTaskCount()); // 4次服务应该生成4个任务

        // 验证任务生成详情
        assertNotNull(result.getDetail());
        assertEquals(4, result.getDetail().getServiceTimes());
        assertTrue(result.getDetail().getGenerateRule().contains("4次服务配置"));

        // 验证数据库操作
        verify(domesticTaskMapper, times(4)).insert(any(DomesticTaskDO.class));
        verify(domesticOrderMapper).updateById(any(DomesticOrderDO.class));
    }

    @Test
    void testGenerateTasksOrderNotFound() {
        // 验证空订单对象异常
        assertThrows(Exception.class, () -> {
            domesticTaskService.generateTasks(null);
        });
    }

    @Test
    void testGenerateTasksAlreadyGenerated() {
        // Mock已生成任务
        when(domesticOrderMapper.selectByOrderId(1024L)).thenReturn(mockDomesticOrder);
        when(domesticTaskMapper.selectCount(anyString(), any())).thenReturn(5L); // 已有任务

        // 验证异常
        assertThrows(Exception.class, () -> {
            domesticTaskService.generateTasks(mockOrder);
        });
    }

    @Test
    void testGenerateTaskNoGeneration() {
        // 测试任务编号生成
        String taskNo = domesticTaskService.generateTaskNo();
        
        assertNotNull(taskNo);
        assertTrue(taskNo.startsWith("JZ"));
        assertTrue(taskNo.length() > 10);
    }

    @Test
    void testGetTasksByOrderId() {
        // Mock查询结果
        List<DomesticTaskDO> mockTasks = List.of(new DomesticTaskDO(), new DomesticTaskDO());
        when(domesticTaskMapper.selectList(anyString(), any())).thenReturn(mockTasks);

        // 执行测试
        List<DomesticTaskDO> result = domesticTaskService.getTasksByOrderId(1024L);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(domesticTaskMapper).selectList("order_id", 1024L);
    }

    @Test
    void testHasTasksGenerated() {
        // Mock有任务
        when(domesticTaskMapper.selectCount(anyString(), any())).thenReturn(3L);
        assertTrue(domesticTaskService.hasTasksGenerated(1024L));

        // Mock无任务
        when(domesticTaskMapper.selectCount(anyString(), any())).thenReturn(0L);
        assertFalse(domesticTaskService.hasTasksGenerated(1024L));
    }

    @Test
    void testBatchGenerateTasks() {
        // 准备测试数据
        List<PublicbizOrderDO> mockOrders = new ArrayList<>();

        // 第一个订单 - 长周期套餐
        PublicbizOrderDO order1 = new PublicbizOrderDO();
        order1.setId(1024L);
        order1.setOrderNo("JZ20250814001");
        order1.setOrderType("domestic");
        order1.setPaymentStatus("paid");
        order1.setOrderStatus("executing");
        mockOrders.add(order1);

        // 第二个订单 - 次数次卡套餐
        PublicbizOrderDO order2 = new PublicbizOrderDO();
        order2.setId(2048L);
        order2.setOrderNo("JZ20250814002");
        order2.setOrderType("domestic");
        order2.setPaymentStatus("paid");
        order2.setOrderStatus("executing");
        mockOrders.add(order2);

        // 准备家政订单详情
        DomesticOrderDO domesticOrder1 = new DomesticOrderDO();
        domesticOrder1.setId(2021L);
        domesticOrder1.setOrderId(1024L);
        domesticOrder1.setServicePackageType("long-term");
        domesticOrder1.setServicePackageDuration("30天");
        domesticOrder1.setServiceFrequency("每日");
        domesticOrder1.setServicePackageName("30天每日保洁服务");
        domesticOrder1.setCustomerName("张三");
        domesticOrder1.setCustomerPhone("***********");
        domesticOrder1.setCustomerOneid("customer-001");
        domesticOrder1.setServiceAddress("北京市朝阳区测试地址");
        domesticOrder1.setServiceCategoryId(1L);
        domesticOrder1.setServiceCategoryName("家庭保洁");
        domesticOrder1.setServiceDescription("专业家庭保洁服务");
        domesticOrder1.setServiceDuration("4小时");
        domesticOrder1.setServiceStartDate(new Date());

        DomesticOrderDO domesticOrder2 = new DomesticOrderDO();
        domesticOrder2.setId(2022L);
        domesticOrder2.setOrderId(2048L);
        domesticOrder2.setServicePackageType("count-card");
        domesticOrder2.setServiceTimes(4);
        domesticOrder2.setServicePackageName("4次上门收纳服务");
        domesticOrder2.setCustomerName("李四");
        domesticOrder2.setCustomerPhone("13800138001");
        domesticOrder2.setCustomerOneid("customer-002");
        domesticOrder2.setServiceAddress("上海市浦东新区测试地址");
        domesticOrder2.setServiceCategoryId(2L);
        domesticOrder2.setServiceCategoryName("上门收纳");
        domesticOrder2.setServiceDescription("专业上门收纳服务");
        domesticOrder2.setServiceDuration("3小时");
        domesticOrder2.setServiceStartDate(new Date());

        // Mock数据库查询
        when(orderMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(mockOrders);
        when(orderMapper.selectById(1024L)).thenReturn(order1);
        when(orderMapper.selectById(2048L)).thenReturn(order2);
        when(domesticOrderMapper.selectByOrderId(1024L)).thenReturn(domesticOrder1);
        when(domesticOrderMapper.selectByOrderId(2048L)).thenReturn(domesticOrder2);
        when(domesticTaskMapper.selectCount(anyString(), any())).thenReturn(0L); // 都未生成任务
        when(domesticOrderMapper.selectById(2021L)).thenReturn(domesticOrder1);
        when(domesticOrderMapper.selectById(2022L)).thenReturn(domesticOrder2);

        // 执行测试
        Map<String, Object> result = domesticTaskService.batchGenerateTasks();

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.get("totalOrders"));
        assertEquals(2, result.get("successCount"));
        assertEquals(0, result.get("skipCount"));
        assertEquals(0, result.get("failCount"));

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> successList = (List<Map<String, Object>>) result.get("successList");
        assertEquals(2, successList.size());

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> failList = (List<Map<String, Object>>) result.get("failList");
        assertEquals(0, failList.size());

        // 验证数据库操作
        verify(orderMapper).selectList(any(LambdaQueryWrapperX.class));
        verify(domesticTaskMapper, times(34)).insert(any(DomesticTaskDO.class)); // 30 + 4 = 34个任务
        verify(domesticOrderMapper, times(2)).updateById(any(DomesticOrderDO.class));
    }

    @Test
    void testBatchGenerateTasksWithSkippedOrders() {
        // 准备测试数据 - 一个已生成任务的订单
        List<PublicbizOrderDO> mockOrders = new ArrayList<>();
        PublicbizOrderDO order = new PublicbizOrderDO();
        order.setId(1024L);
        order.setOrderNo("JZ20250814001");
        mockOrders.add(order);

        // Mock数据库查询
        when(orderMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(mockOrders);
        when(domesticTaskMapper.selectCount(anyString(), any())).thenReturn(5L); // 已有任务

        // 执行测试
        Map<String, Object> result = domesticTaskService.batchGenerateTasks();

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.get("totalOrders"));
        assertEquals(0, result.get("successCount"));
        assertEquals(1, result.get("skipCount"));
        assertEquals(0, result.get("failCount"));

        // 验证没有调用任务生成
        verify(domesticTaskMapper, never()).insert(any(DomesticTaskDO.class));
    }
}
