package cn.bztmaster.cnt.module.publicbiz.service.domestic;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.DomesticTaskGenerateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.DomesticTaskGenerateRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic.DomesticTaskMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.DomesticOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.service.domestic.impl.DomesticTaskServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 家政服务任务生成测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class DomesticTaskServiceTest {

    @Mock
    private DomesticTaskMapper domesticTaskMapper;

    @Mock
    private PublicbizOrderMapper orderMapper;

    @Mock
    private DomesticOrderMapper domesticOrderMapper;

    @InjectMocks
    private DomesticTaskServiceImpl domesticTaskService;

    private PublicbizOrderDO mockOrder;
    private DomesticOrderDO mockDomesticOrder;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        mockOrder = new PublicbizOrderDO();
        mockOrder.setId(1024L);
        mockOrder.setOrderNo("JZ20250814001");
        mockOrder.setOrderType("domestic");
        mockOrder.setBusinessLine("家政服务");

        mockDomesticOrder = new DomesticOrderDO();
        mockDomesticOrder.setId(2021L);
        mockDomesticOrder.setOrderId(1024L);
        mockDomesticOrder.setOrderNo("JZ20250814001");
        mockDomesticOrder.setCustomerName("张三");
        mockDomesticOrder.setCustomerPhone("***********");
        mockDomesticOrder.setCustomerOneid("customer-001");
        mockDomesticOrder.setServiceAddress("北京市朝阳区测试地址");
        mockDomesticOrder.setServiceCategoryId(1L);
        mockDomesticOrder.setServiceCategoryName("家庭保洁");
        mockDomesticOrder.setServicePackageName("30天每日保洁服务");
        mockDomesticOrder.setServiceDescription("专业家庭保洁服务");
        mockDomesticOrder.setServiceDuration("4小时");
        mockDomesticOrder.setServiceFrequency("每日");
        mockDomesticOrder.setServiceStartDate(new Date());
    }

    @Test
    void testGenerateLongTermTasks() {
        // 准备长周期套餐数据
        mockDomesticOrder.setServicePackageType("long-term");
        mockDomesticOrder.setServicePackageDuration("30天");
        mockDomesticOrder.setServiceFrequency("每日");

        // Mock数据库查询
        when(orderMapper.selectById(1024L)).thenReturn(mockOrder);
        when(domesticOrderMapper.selectByOrderId(1024L)).thenReturn(mockDomesticOrder);
        when(domesticTaskMapper.selectCount(anyString(), any())).thenReturn(0L);
        when(domesticOrderMapper.selectById(2021L)).thenReturn(mockDomesticOrder);

        // 执行测试
        DomesticTaskGenerateReqVO reqVO = new DomesticTaskGenerateReqVO();
        reqVO.setOrderId(1024L);
        reqVO.setForceRegenerate(false);

        DomesticTaskGenerateRespVO result = domesticTaskService.generateTasks(reqVO);

        // 验证结果
        assertNotNull(result);
        assertEquals(1024L, result.getOrderId());
        assertEquals("JZ20250814001", result.getOrderNo());
        assertEquals("long-term", result.getPackageType());
        assertEquals("30天每日保洁服务", result.getPackageName());
        assertEquals(30, result.getTaskCount()); // 30天每日服务应该生成30个任务

        // 验证任务生成详情
        assertNotNull(result.getDetail());
        assertEquals(30, result.getDetail().getServicePeriod());
        assertEquals("每日", result.getDetail().getServiceFrequency());
        assertEquals(30, result.getDetail().getServiceTimes());
        assertTrue(result.getDetail().getGenerateRule().contains("30天每日服务规则"));

        // 验证数据库操作
        verify(domesticTaskMapper, times(30)).insert(any(DomesticTaskDO.class));
        verify(domesticOrderMapper).updateById(any(DomesticOrderDO.class));
    }

    @Test
    void testGenerateCountCardTasks() {
        // 准备次数次卡套餐数据
        mockDomesticOrder.setServicePackageType("count-card");
        mockDomesticOrder.setServiceTimes(4);
        mockDomesticOrder.setServicePackageName("4次上门收纳服务");
        mockDomesticOrder.setServiceSchedule("{\"dates\":[\"2025-08-14\",\"2025-08-21\",\"2025-08-28\",\"2025-09-04\"]}");

        // Mock数据库查询
        when(orderMapper.selectById(1024L)).thenReturn(mockOrder);
        when(domesticOrderMapper.selectByOrderId(1024L)).thenReturn(mockDomesticOrder);
        when(domesticTaskMapper.selectCount(anyString(), any())).thenReturn(0L);
        when(domesticOrderMapper.selectById(2021L)).thenReturn(mockDomesticOrder);

        // 执行测试
        DomesticTaskGenerateReqVO reqVO = new DomesticTaskGenerateReqVO();
        reqVO.setOrderId(1024L);
        reqVO.setForceRegenerate(false);

        DomesticTaskGenerateRespVO result = domesticTaskService.generateTasks(reqVO);

        // 验证结果
        assertNotNull(result);
        assertEquals(1024L, result.getOrderId());
        assertEquals("count-card", result.getPackageType());
        assertEquals(4, result.getTaskCount()); // 4次服务应该生成4个任务

        // 验证任务生成详情
        assertNotNull(result.getDetail());
        assertEquals(4, result.getDetail().getServiceTimes());
        assertTrue(result.getDetail().getGenerateRule().contains("4次服务配置"));

        // 验证数据库操作
        verify(domesticTaskMapper, times(4)).insert(any(DomesticTaskDO.class));
        verify(domesticOrderMapper).updateById(any(DomesticOrderDO.class));
    }

    @Test
    void testGenerateTasksOrderNotFound() {
        // Mock订单不存在
        when(orderMapper.selectById(1024L)).thenReturn(null);

        // 执行测试
        DomesticTaskGenerateReqVO reqVO = new DomesticTaskGenerateReqVO();
        reqVO.setOrderId(1024L);

        // 验证异常
        assertThrows(Exception.class, () -> {
            domesticTaskService.generateTasks(reqVO);
        });
    }

    @Test
    void testGenerateTasksAlreadyGenerated() {
        // Mock已生成任务
        when(orderMapper.selectById(1024L)).thenReturn(mockOrder);
        when(domesticOrderMapper.selectByOrderId(1024L)).thenReturn(mockDomesticOrder);
        when(domesticTaskMapper.selectCount(anyString(), any())).thenReturn(5L); // 已有任务

        // 执行测试
        DomesticTaskGenerateReqVO reqVO = new DomesticTaskGenerateReqVO();
        reqVO.setOrderId(1024L);
        reqVO.setForceRegenerate(false);

        // 验证异常
        assertThrows(Exception.class, () -> {
            domesticTaskService.generateTasks(reqVO);
        });
    }

    @Test
    void testGenerateTaskNoGeneration() {
        // 测试任务编号生成
        String taskNo = domesticTaskService.generateTaskNo();
        
        assertNotNull(taskNo);
        assertTrue(taskNo.startsWith("JZ"));
        assertTrue(taskNo.length() > 10);
    }

    @Test
    void testGetTasksByOrderId() {
        // Mock查询结果
        List<DomesticTaskDO> mockTasks = List.of(new DomesticTaskDO(), new DomesticTaskDO());
        when(domesticTaskMapper.selectList(anyString(), any())).thenReturn(mockTasks);

        // 执行测试
        List<DomesticTaskDO> result = domesticTaskService.getTasksByOrderId(1024L);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(domesticTaskMapper).selectList("order_id", 1024L);
    }

    @Test
    void testHasTasksGenerated() {
        // Mock有任务
        when(domesticTaskMapper.selectCount(anyString(), any())).thenReturn(3L);
        assertTrue(domesticTaskService.hasTasksGenerated(1024L));

        // Mock无任务
        when(domesticTaskMapper.selectCount(anyString(), any())).thenReturn(0L);
        assertFalse(domesticTaskService.hasTasksGenerated(1024L));
    }
}
