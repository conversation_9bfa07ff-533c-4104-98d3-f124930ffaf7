package cn.bztmaster.cnt.module.publicbiz.controller.admin.order;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import cn.bztmaster.cnt.framework.excel.core.util.ExcelUtils;
import com.mzt.logapi.starter.annotation.LogRecord;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderPageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderLogPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderLogPageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderApprovalReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderApprovalRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderContractUploadReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderContractUploadRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderStatisticsRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderDeleteReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderDetailReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderDropdownDataReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderDropdownDataRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderExportReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderExcelVO;
import cn.bztmaster.cnt.module.publicbiz.service.order.UniversityPracticeOrderService;
import cn.bztmaster.cnt.module.publicbiz.service.order.OrderDropdownDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 高校实践订单
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 高校实践订单")
@RestController
@RequestMapping("/publicbiz/order")
@Validated
@Slf4j
public class UniversityPracticeOrderController {

    @Resource
    private UniversityPracticeOrderService universityPracticeOrderService;

    @Resource
    private OrderDropdownDataService orderDropdownDataService;

    @PostMapping("/create")
    @Operation(summary = "创建高校实践订单")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:create')")
    public CommonResult<Long> createOrder(@Valid @RequestBody UniversityPracticeOrderSaveReqVO createReqVO) {
        Long id = universityPracticeOrderService.createOrder(createReqVO);
        return success(id);
    }

    @PostMapping("/update")
    @Operation(summary = "更新高校实践订单")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:update')")
    public CommonResult<Boolean> updateOrder(@Valid @RequestBody UniversityPracticeOrderSaveReqVO updateReqVO) {
        universityPracticeOrderService.updateOrder(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除高校实践订单")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:delete')")
    public CommonResult<Boolean> deleteOrder(@Valid @RequestBody UniversityPracticeOrderDeleteReqVO deleteReqVO) {
        universityPracticeOrderService.deleteOrder(deleteReqVO.getId(), deleteReqVO.getOrderNo());
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得高校实践订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:query')")
    public CommonResult<UniversityPracticeOrderDetailRespVO> getOrder(@RequestParam("id") Long id) {
        UniversityPracticeOrderDetailRespVO order = universityPracticeOrderService.getOrder(id);
        return success(order);
    }

    @GetMapping("/get-by-order-no")
    @Operation(summary = "通过订单号获得高校实践订单")
    @Parameter(name = "orderNo", description = "订单号", required = true, example = "HP202406001")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:query')")
    public CommonResult<UniversityPracticeOrderDetailRespVO> getOrderByOrderNo(@RequestParam("orderNo") String orderNo) {
        UniversityPracticeOrderDetailRespVO order = universityPracticeOrderService.getOrderByOrderNo(orderNo);
        return success(order);
    }

    @GetMapping("/page")
    @Operation(summary = "获得高校实践订单分页")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:query')")
    public CommonResult<PageResult<UniversityPracticeOrderPageRespVO>> getOrderPage(@Valid UniversityPracticeOrderPageReqVO pageReqVO) {
        PageResult<UniversityPracticeOrderPageRespVO> pageResult = universityPracticeOrderService.getOrderPage(pageReqVO);
        return success(pageResult);
    }

    @PostMapping("/detail")
    @Operation(summary = "查看高校实践订单详情")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:query')")
    public CommonResult<UniversityPracticeOrderDetailRespVO> getOrderDetail(@RequestBody UniversityPracticeOrderDetailReqVO detailReqVO) {
        UniversityPracticeOrderDetailRespVO order = universityPracticeOrderService.getOrder(detailReqVO.getId());
        return success(order);
    }

    @GetMapping("/operation-logs")
    @Operation(summary = "查询高校实践订单操作日志")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:query')")
    public CommonResult<PageResult<UniversityPracticeOrderLogPageRespVO>> getOrderOperationLogs(@Valid UniversityPracticeOrderLogPageReqVO pageReqVO) {
        PageResult<UniversityPracticeOrderLogPageRespVO> pageResult = universityPracticeOrderService.getOrderOperationLogs(pageReqVO);
        return success(pageResult);
    }

    @PostMapping("/initiate-approval")
    @Operation(summary = "发起高校实践订单审批")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:approval')")
    public CommonResult<UniversityPracticeOrderApprovalRespVO> initiateApproval(@Valid @RequestBody UniversityPracticeOrderApprovalReqVO approvalReqVO) {
        UniversityPracticeOrderApprovalRespVO result = universityPracticeOrderService.initiateApproval(approvalReqVO);
        return success(result);
    }

    @PostMapping("/upload-contract")
    @Operation(summary = "上传高校实践订单合同")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:upload')")
    public CommonResult<UniversityPracticeOrderContractUploadRespVO> uploadContract(@Valid UniversityPracticeOrderContractUploadReqVO uploadReqVO, 
                                                                                 @RequestParam("file") MultipartFile file) {
        UniversityPracticeOrderContractUploadRespVO result = universityPracticeOrderService.uploadContract(uploadReqVO, file);
        return success(result);
    }

    @GetMapping("/statistics/overview")
    @Operation(summary = "获取高校实践订单统计概览")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:query')")
    public CommonResult<UniversityPracticeOrderStatisticsRespVO> getOrderStatisticsOverview() {
        UniversityPracticeOrderStatisticsRespVO statistics = universityPracticeOrderService.getOrderStatisticsOverview();
        return success(statistics);
    }

    @GetMapping("/test")
    @Operation(summary = "测试接口")
    public CommonResult<String> test() {
        log.info("测试接口被调用");
        return success("接口正常工作");
    }

    @PostMapping("/test-dropdown")
    @Operation(summary = "测试下拉数据接口")
    public CommonResult<String> testDropdown() {
        try {
            log.info("测试下拉数据接口被调用");
            
            // 创建测试请求对象
            OrderDropdownDataReqVO testReq = new OrderDropdownDataReqVO();
            testReq.setOrderType("practice");
            testReq.setBusinessLine("高校实践");
            
            log.info("测试请求参数：{}", testReq);
            
            // 调用服务
            OrderDropdownDataRespVO result = orderDropdownDataService.getDropdownData(testReq);
            
            log.info("测试结果：商机={}, 线索={}", 
                    result.getBusinessOptions().size(), 
                    result.getLeadOptions().size());
            
            return success("测试成功，商机数量：" + result.getBusinessOptions().size() + 
                          "，线索数量：" + result.getLeadOptions().size());
            
        } catch (Exception e) {
            log.error("测试下拉数据接口失败", e);
            return CommonResult.error(500, "测试失败：" + e.getMessage());
        }
    }

    @PostMapping("/dropdown-data")
    @Operation(summary = "获取订单下拉数据")
    public CommonResult<OrderDropdownDataRespVO> getDropdownData(@Valid @RequestBody OrderDropdownDataReqVO reqVO) {
        try {
            log.info("开始获取下拉数据，请求参数：{}", reqVO);
            
            // 参数验证
            if (reqVO == null) {
                log.error("请求参数为空");
                return CommonResult.error(500, "请求参数不能为空");
            }
            
            if (reqVO.getOrderType() == null || reqVO.getOrderType().trim().isEmpty()) {
                log.error("订单类型为空");
                return CommonResult.error(400, "订单类型不能为空");
            }
            
            OrderDropdownDataRespVO dropdownData = orderDropdownDataService.getDropdownData(reqVO);
            log.info("下拉数据获取成功");
            return success(dropdownData);
            
        } catch (Exception e) {
            log.error("获取下拉数据失败，异常详情：", e);
            // 返回友好的错误信息，而不是抛出异常
            return CommonResult.error(500, "获取下拉数据失败：" + e.getMessage());
        }
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出高校实践订单 Excel")
    @LogRecord(type = "高校实践订单", subType = "导出Excel", bizNo = "{{#exportReqVO}}", success = "导出了高校实践订单Excel")
    public void exportOrderExcel(@Valid UniversityPracticeOrderExportReqVO exportReqVO,
                                HttpServletResponse response) throws IOException {
        // 设置分页大小为无限制，获取所有数据
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        
        // 获取订单列表
        List<UniversityPracticeOrderExcelVO> excelList = universityPracticeOrderService.getOrderExcelList(exportReqVO);
        
        // 导出 Excel
        ExcelUtils.write(response, "高校实践订单.xls", "数据", UniversityPracticeOrderExcelVO.class, excelList);
    }

}
