package cn.bztmaster.cnt.module.publicbiz.service.aunt.impl;

import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntWorkbenchInfoRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic.DomesticTaskMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.settlement.SettlementMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntWorkbenchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;

/**
 * 阿姨工作台 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuntWorkbenchServiceImpl implements AuntWorkbenchService {

    @Resource
    private PractitionerMapper practitionerMapper;

    @Resource
    private DomesticTaskMapper domesticTaskMapper;

    @Resource
    private SettlementMapper settlementMapper;

    @Resource
    private PublicbizOrderMapper orderMapper;

    @Override
    public AuntWorkbenchInfoRespVO getWorkbenchInfo(String auntOneId) {
        // 0. 测试MyBatis配置（调试用）
        testMyBatisConfiguration();
        
        // 1. 查询阿姨基本信息
        PractitionerDO practitioner = practitionerMapper.selectByAuntOneId(auntOneId);
        if (practitioner == null) {
            log.error("阿姨信息不存在，auntOneId: {}", auntOneId);
            return null;
        }

        // 2. 构建阿姨信息
        AuntWorkbenchInfoRespVO.AuntInfo auntInfo = buildAuntInfo(practitioner);

        // 3. 构建KPI数据
        AuntWorkbenchInfoRespVO.KPIData kpiData = buildKPIData(auntOneId);

        // 4. 构建订单统计
        AuntWorkbenchInfoRespVO.OrderStats orderStats = buildOrderStats(auntOneId);

        // 5. 组装返回数据
        AuntWorkbenchInfoRespVO workbenchInfo = new AuntWorkbenchInfoRespVO();
        workbenchInfo.setAuntInfo(auntInfo);
        workbenchInfo.setKpiData(kpiData);
        workbenchInfo.setOrderStats(orderStats);

        return workbenchInfo;
    }

    /**
     * 构建阿姨信息
     */
    private AuntWorkbenchInfoRespVO.AuntInfo buildAuntInfo(PractitionerDO practitioner) {
        AuntWorkbenchInfoRespVO.AuntInfo auntInfo = new AuntWorkbenchInfoRespVO.AuntInfo();
        auntInfo.setOneId(practitioner.getAuntOneid());
        auntInfo.setName(practitioner.getName());
        auntInfo.setPhone(practitioner.getPhone());
        auntInfo.setAvatar(practitioner.getAvatar());
        auntInfo.setServiceType(practitioner.getServiceType());
        auntInfo.setExperienceYears(practitioner.getExperienceYears());
        auntInfo.setRating(practitioner.getRating());
        auntInfo.setCurrentStatus(practitioner.getCurrentStatus());
        auntInfo.setAgencyName(practitioner.getAgencyName());
        return auntInfo;
    }

    /**
     * 构建KPI数据
     */
    private AuntWorkbenchInfoRespVO.KPIData buildKPIData(String auntOneId) {
        AuntWorkbenchInfoRespVO.KPIData kpiData = new AuntWorkbenchInfoRespVO.KPIData();
        
        // 获取今日排班数
        Integer todaySchedule = getTodayScheduleCount(auntOneId);
        kpiData.setTodaySchedule(todaySchedule);
        
        // 获取本月预估收入
        BigDecimal monthlyIncome = getMonthlyIncome(auntOneId);
        kpiData.setMonthlyIncome(monthlyIncome);
        
        // 获取本月服务时长
        Integer monthlyServiceHours = getMonthlyServiceHours(auntOneId);
        kpiData.setMonthlyServiceHours(monthlyServiceHours);
        
        return kpiData;
    }

    /**
     * 构建订单统计
     */
    private AuntWorkbenchInfoRespVO.OrderStats buildOrderStats(String auntOneId) {
        AuntWorkbenchInfoRespVO.OrderStats orderStats = new AuntWorkbenchInfoRespVO.OrderStats();
        
        // 获取各状态订单数量
        Integer pending = getOrderCountByStatus(auntOneId, "pending");
        Integer inProgress = getOrderCountByStatus(auntOneId, "in_progress");
        Integer completed = getOrderCountByStatus(auntOneId, "completed");
        Integer cancelled = getOrderCountByStatus(auntOneId, "cancelled");
        
        orderStats.setPending(pending);
        orderStats.setInProgress(inProgress);
        orderStats.setCompleted(completed);
        orderStats.setCancelled(cancelled);
        
        return orderStats;
    }

    /**
     * 获取今日排班数
     * 通过 publicbiz_domestic_task 表查询今日排班的任务数量
     */
    private Integer getTodayScheduleCount(String auntOneId) {
        try {
            // 获取今天的日期
            LocalDate today = LocalDate.now();
            
            log.info("查询今日排班数，auntOneId: {}, today: {}", auntOneId, today);
            
            // 调用Mapper查询今日排班数
            Integer count = domesticTaskMapper.selectTodayScheduleCount(auntOneId, today);
            return count != null ? count : 0;
            
        } catch (Exception e) {
            log.error("查询今日排班数失败，auntOneId: {}", auntOneId, e);
            return 0;
        }
    }

    /**
     * 获取本月预估收入
     * 通过 publicbiz_settlement 表查询阿姨相关的结算金额总和
     */
    private BigDecimal getMonthlyIncome(String auntOneId) {
        try {
            // 获取当前年月
            YearMonth currentMonth = YearMonth.now();
            LocalDateTime startOfMonth = currentMonth.atDay(1).atStartOfDay();
            LocalDateTime endOfMonth = currentMonth.atEndOfMonth().atTime(23, 59, 59);
            
            log.info("查询本月预估收入，auntOneId: {}, 开始时间: {}, 结束时间: {}", 
                    auntOneId, startOfMonth, endOfMonth);
            
            // 调用Mapper查询本月预估收入
            BigDecimal income = settlementMapper.selectMonthlyIncome(auntOneId, startOfMonth, endOfMonth);
            return income != null ? income : BigDecimal.ZERO;
            
        } catch (Exception e) {
            log.error("查询本月预估收入失败，auntOneId: {}", auntOneId, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取本月服务时长
     * 通过 publicbiz_domestic_task 表查询已完成任务的服务时长总和
     */
    private Integer getMonthlyServiceHours(String auntOneId) {
        try {
            // 获取当前年月
            YearMonth currentMonth = YearMonth.now();
            LocalDateTime startOfMonth = currentMonth.atDay(1).atStartOfDay();
            LocalDateTime endOfMonth = currentMonth.atEndOfMonth().atTime(23, 59, 59);
            
            log.info("查询本月服务时长，auntOneId: {}, 开始时间: {}, 结束时间: {}", 
                    auntOneId, startOfMonth, endOfMonth);
            
            // 调用Mapper查询本月服务时长
            Integer hours = domesticTaskMapper.selectMonthlyServiceHours(auntOneId, startOfMonth, endOfMonth);
            return hours != null ? hours : 0;
            
        } catch (Exception e) {
            log.error("查询本月服务时长失败，auntOneId: {}", auntOneId, e);
            return 0;
        }
    }

    /**
     * 根据状态获取订单数量
     * 通过 publicbiz_order 表查询阿姨名下不同状态的订单数量
     */
    private Integer getOrderCountByStatus(String auntOneId, String status) {
        try {
            log.info("查询订单数量，auntOneId: {}, status: {}", auntOneId, status);
            
            // 参数验证
            if (auntOneId == null || auntOneId.trim().isEmpty()) {
                log.error("阿姨OneID为空");
                return 0;
            }
            if (status == null || status.trim().isEmpty()) {
                log.error("订单状态为空");
                return 0;
            }
            
            log.info("参数验证通过，开始执行查询...");
            
            // 调用Mapper查询指定状态的订单数量
            Integer count = orderMapper.selectCountByStatusAndPractitioner(auntOneId, status);
            log.info("查询结果，auntOneId: {}, status: {}, count: {}", auntOneId, status, count);
            
            // 添加额外的调试信息
            if (count == null || count == 0) {
                log.warn("查询结果为0，开始排查问题...");
                
                // 尝试直接查询验证数据是否存在
                try {
                    // 这里可以添加一个简单的查询来验证数据
                    log.info("尝试验证数据是否存在...");
                } catch (Exception ex) {
                    log.error("验证查询失败", ex);
                }
            }
            
            return count != null ? count : 0;
            
        } catch (Exception e) {
            log.error("查询订单数量失败，auntOneId: {}, status: {}", auntOneId, status, e);
            return 0;
        }
    }

    /**
     * 测试MyBatis配置是否正常
     * 用于调试MyBatis查询问题
     */
    private void testMyBatisConfiguration() {
        try {
            log.info("开始测试MyBatis配置...");
            
            // 测试1：简单的count查询
            Long totalCount = orderMapper.selectCount(null);
            log.info("测试1 - 总订单数: {}", totalCount);
            
            // 测试2：使用固定参数查询
            String testAuntOneId = "fa2182d0-768a-11f0-ae0c-00163e1f6ba5";
            String testStatus = "pending";
            
            Integer testCount = orderMapper.selectCountByStatusAndPractitioner(testAuntOneId, testStatus);
            log.info("测试2 - 固定参数查询结果: {}", testCount);
            
        } catch (Exception e) {
            log.error("MyBatis配置测试失败", e);
        }
    }
} 