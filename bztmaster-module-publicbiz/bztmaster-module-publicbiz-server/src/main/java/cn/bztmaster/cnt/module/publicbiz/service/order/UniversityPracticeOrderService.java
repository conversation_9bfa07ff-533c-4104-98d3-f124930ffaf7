package cn.bztmaster.cnt.module.publicbiz.service.order;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.UniversityPracticeOrderRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.UniversityPracticeOrderSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderPageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderLogPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderLogPageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderApprovalReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderApprovalRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderContractUploadReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderContractUploadRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderStatisticsRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderExportReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderExcelVO;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * 高校实践订单 Service 接口
 *
 * <AUTHOR>
 */
public interface UniversityPracticeOrderService {

    /**
     * 创建高校实践订单
     *
     * @param createReqVO 创建信息
     * @return 订单编号
     */
    Long createOrder(@Valid UniversityPracticeOrderSaveReqVO createReqVO);

    /**
     * 更新高校实践订单
     *
     * @param updateReqVO 更新信息
     */
    void updateOrder(@Valid UniversityPracticeOrderSaveReqVO updateReqVO);

    /**
     * 删除高校实践订单
     *
     * @param id 订单编号
     * @param orderNo 订单号
     */
    void deleteOrder(Long id, String orderNo);

    /**
     * 获得高校实践订单
     *
     * @param id 订单编号
     * @return 高校实践订单
     */
    UniversityPracticeOrderDetailRespVO getOrder(Long id);

    /**
     * 通过订单号获得高校实践订单
     *
     * @param orderNo 订单号
     * @return 高校实践订单
     */
    UniversityPracticeOrderDetailRespVO getOrderByOrderNo(String orderNo);

    /**
     * 获得高校实践订单分页
     *
     * @param pageReqVO 分页查询
     * @return 高校实践订单分页
     */
    PageResult<UniversityPracticeOrderPageRespVO> getOrderPage(UniversityPracticeOrderPageReqVO pageReqVO);

    /**
     * 通过订单ID列表获得高校实践订单列表
     *
     * @param ids 订单编号数组
     * @return 高校实践订单列表
     */
    List<UniversityPracticeOrderRespDTO> getOrderList(List<Long> ids);

    /**
     * 通过订单号获得高校实践订单
     *
     * @param orderNo 订单号
     * @return 高校实践订单
     */
    UniversityPracticeOrderRespDTO getOrderByOrderNoForApi(String orderNo);

    /**
     * 创建高校实践订单（API）
     *
     * @param createReqDTO 创建信息
     * @return 订单编号
     */
    Long createOrderForApi(@Valid UniversityPracticeOrderSaveReqDTO createReqDTO);

    /**
     * 更新高校实践订单（API）
     *
     * @param updateReqDTO 更新信息
     */
    void updateOrderForApi(@Valid UniversityPracticeOrderSaveReqDTO updateReqDTO);

    /**
     * 删除高校实践订单（API）
     *
     * @param id 订单编号
     * @param orderNo 订单号
     */
    void deleteOrderForApi(Long id, String orderNo);

    /**
     * 生成订单号
     *
     * @return 订单号
     */
    String generateOrderNo();

    /**
     * 查询高校实践订单操作日志
     *
     * @param pageReqVO 分页查询条件
     * @return 操作日志分页结果
     */
    PageResult<UniversityPracticeOrderLogPageRespVO> getOrderOperationLogs(UniversityPracticeOrderLogPageReqVO pageReqVO);

    /**
     * 发起高校实践订单审批
     *
     * @param approvalReqVO 审批请求信息
     * @return 审批响应结果
     */
    UniversityPracticeOrderApprovalRespVO initiateApproval(UniversityPracticeOrderApprovalReqVO approvalReqVO);

    /**
     * 上传高校实践订单合同
     *
     * @param uploadReqVO 上传请求信息
     * @param file 合同文件
     * @return 上传响应结果
     */
    UniversityPracticeOrderContractUploadRespVO uploadContract(UniversityPracticeOrderContractUploadReqVO uploadReqVO, MultipartFile file);

    /**
     * 获取高校实践订单统计概览
     *
     * @return 统计概览信息
     */
    UniversityPracticeOrderStatisticsRespVO getOrderStatisticsOverview();

    /**
     * 获取高校实践订单Excel导出列表
     *
     * @param exportReqVO 导出请求条件
     * @return Excel导出数据列表
     */
    List<UniversityPracticeOrderExcelVO> getOrderExcelList(UniversityPracticeOrderExportReqVO exportReqVO);

}
