package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 任务工单 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_work_order")
@KeySequence("publicbiz_work_order_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkOrderDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 工单编号
     */
    private String workOrderNo;

    /**
     * 关联订单号
     */
    private String orderNo;

    /**
     * 工单标题
     */
    private String workOrderTitle;

    /**
     * 工单内容(投诉内容/请假理由)
     */
    private String workOrderContent;

    /**
     * 工单类型：complaint-投诉/substitution_request-换人申请/take_leave-请假/顶岗/separation_application-离职申请
     */
    private String workOrderType;

    /**
     * 优先级：low-低/medium-中/high-高/urgent-紧急
     */
    private String priority;

    /**
     * 工单状态：pending-待处理/processing-处理中/resolved-已解决/closed-已关闭
     */
    private String workOrderStatus;

    /**
     * 负责人ID
     */
    private Long assigneeId;

    /**
     * 负责人姓名
     */
    private String assigneeName;

    /**
     * 阿姨OneID GUID
     */
    private String auntOneid;

    /**
     * 阿姨姓名
     */
    private String auntName;

    /**
     * 请假类型(1-请假,2-调休)
     */
    private Integer leaveType;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 请假时长(小时)
     */
    private BigDecimal durationHours;

    /**
     * 请假天数
     */
    private BigDecimal durationDays;

    /**
     * 审批状态(0-审批中,1-已批准,2-已驳回)
     */
    private Integer status;

    /**
     * 审批时间
     */
    private LocalDateTime approveTime;

    /**
     * 审批备注/处理意见
     */
    private String approveRemark;

    /**
     * 投诉类型：service_quality-服务质量/attitude-服务态度/punctuality-守时问题/other-其他
     */
    private String complaintType;

    /**
     * 投诉等级：low-轻微/medium-中等/high-严重/urgent-紧急
     */
    private String complaintLevel;

    /**
     * 投诉时间
     */
    private LocalDateTime complaintTime;

    /**
     * 客户期望文本内容
     */
    private String customerExpectation;

    /**
     * 投诉人ID
     */
    private Long complainerId;

    /**
     * 投诉人姓名
     */
    private String complainerName;

    /**
     * 机构ID
     */
    private Long agencyId;

    /**
     * 机构名称
     */
    private String agencyName;

    /**
     * 重新指派起始日期
     */
    private LocalDate reassignmentStartDate;

    /**
     * 指派新阿姨OneID
     */
    private String newAuntOneid;

    /**
     * 指派新阿姨名称
     */
    private String newAuntName;

    /**
     * 指派说明内容
     */
    private String reassignmentDescription;

    /**
     * 指派更新时间
     */
    private LocalDateTime reassignmentUpdateTime;

    /**
     * 转派原因(系统基础数据值)
     */
    private String reassignmentReason;

    /**
     * 转派备注
     */
    private String reassignmentRemark;

    /**
     * 备注
     */
    private String remark;
}
