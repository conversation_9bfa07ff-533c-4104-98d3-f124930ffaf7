package cn.bztmaster.cnt.module.publicbiz.service.aunt.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntOrderDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntOrderListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntOrderListRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.DomesticOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 阿姨订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuntOrderServiceImpl implements AuntOrderService {

    @Resource
    private DomesticOrderMapper domesticOrderMapper;

    @Resource
    private PublicbizOrderMapper orderMapper;

    @Override
    public PageResult<AuntOrderListRespVO> getOrderList(AuntOrderListReqVO reqVO) {
        // 1. 根据阿姨OneID查询订单列表
        List<DomesticOrderDO> orderList = domesticOrderMapper.selectByPractitionerOneid(reqVO.getAuntOneId());
        
        // 2. 根据查询条件过滤
        List<DomesticOrderDO> filteredList = orderList.stream()
                .filter(order -> {
                    // 按订单状态过滤
                    if (reqVO.getOrderStatus() != null && !reqVO.getOrderStatus().isEmpty()) {
                        // 查询主订单状态进行过滤
                        PublicbizOrderDO mainOrder = orderMapper.selectById(order.getOrderId());
                        if (mainOrder != null && reqVO.getOrderStatus().equals(mainOrder.getOrderStatus())) {
                            return true;
                        }
                        return false;
                    }
                    // 按客户姓名过滤
                    if (reqVO.getCustomerName() != null && !reqVO.getCustomerName().isEmpty()) {
                        return order.getCustomerName().contains(reqVO.getCustomerName());
                    }
                    // 按服务地址过滤
                    if (reqVO.getServiceAddress() != null && !reqVO.getServiceAddress().isEmpty()) {
                        return order.getServiceAddress().contains(reqVO.getServiceAddress());
                    }
                    return true;
                })
                .collect(Collectors.toList());

        // 3. 转换为VO对象
        List<AuntOrderListRespVO> voList = filteredList.stream()
                .map(this::convertToOrderListRespVO)
                .collect(Collectors.toList());

        // 4. 分页处理
        int total = voList.size();
        int pageNo = reqVO.getPageNo();
        int pageSize = reqVO.getPageSize();
        int start = (pageNo - 1) * pageSize;
        int end = Math.min(start + pageSize, total);
        
        List<AuntOrderListRespVO> pageList = voList.subList(start, end);

        return new PageResult<>(pageList, (long) total);
    }

    @Override
    public AuntOrderDetailRespVO getOrderDetail(Long orderId, String auntOneId) {
        // 1. 查询主订单信息
        PublicbizOrderDO mainOrder = orderMapper.selectById(orderId);
        if (mainOrder == null) {
            log.error("主订单不存在，orderId: {}", orderId);
            return null;
        }

        // 2. 查询订单详情信息
        DomesticOrderDO domesticOrder = domesticOrderMapper.selectByOrderId(orderId);
        if (domesticOrder == null) {
            log.error("订单详情不存在，orderId: {}", orderId);
            return null;
        }

        // 3. 验证订单是否属于该阿姨
        if (!auntOneId.equals(domesticOrder.getPractitionerOneid())) {
            log.error("订单不属于该阿姨，orderId: {}, auntOneId: {}", orderId, auntOneId);
            return null;
        }

        // 4. 转换为详情VO，使用真实的订单状态
        return convertToOrderDetailRespVO(domesticOrder, mainOrder);
    }

    @Override
    public void confirmOrder(Long orderId, String auntOneId) {
        // 1. 查询主订单信息
        PublicbizOrderDO mainOrder = orderMapper.selectById(orderId);
        if (mainOrder == null) {
            log.error("主订单不存在，orderId: {}", orderId);
            throw new RuntimeException("订单不存在");
        }

        // 2. 查询订单详情信息
        DomesticOrderDO domesticOrder = domesticOrderMapper.selectByOrderId(orderId);
        if (domesticOrder == null) {
            log.error("订单详情不存在，orderId: {}", orderId);
            throw new RuntimeException("订单详情不存在");
        }

        // 3. 验证订单是否属于该阿姨
        if (!auntOneId.equals(domesticOrder.getPractitionerOneid())) {
            log.error("订单不属于该阿姨，orderId: {}, auntOneId: {}", orderId, auntOneId);
            throw new RuntimeException("订单不属于该阿姨");
        }

        // 4. 验证订单状态是否允许确认
        if (!"pending".equals(mainOrder.getOrderStatus())) {
            log.error("订单状态不允许确认，orderId: {}, currentStatus: {}", orderId, mainOrder.getOrderStatus());
            throw new RuntimeException("订单状态不允许确认");
        }

        // 5. 更新主订单状态为进行中
        PublicbizOrderDO updateOrder = new PublicbizOrderDO();
        updateOrder.setId(orderId);
        updateOrder.setOrderStatus("in_progress");
        updateOrder.setUpdateTime(LocalDateTime.now());
        updateOrder.setUpdater(auntOneId);
        
        int updateCount = orderMapper.updateById(updateOrder);
        if (updateCount == 0) {
            log.error("更新订单状态失败，orderId: {}", orderId);
            throw new RuntimeException("更新订单状态失败");
        }

        log.info("阿姨确认订单成功，orderId: {}, auntOneId: {}, 新状态: in_progress", orderId, auntOneId);
    }

    @Override
    public void rejectOrder(Long orderId, String auntOneId, String rejectReason) {
        // 1. 查询主订单信息
        PublicbizOrderDO mainOrder = orderMapper.selectById(orderId);
        if (mainOrder == null) {
            log.error("主订单不存在，orderId: {}", orderId);
            throw new RuntimeException("订单不存在");
        }

        // 2. 查询订单详情信息
        DomesticOrderDO domesticOrder = domesticOrderMapper.selectByOrderId(orderId);
        if (domesticOrder == null) {
            log.error("订单详情不存在，orderId: {}", orderId);
            throw new RuntimeException("订单详情不存在");
        }

        // 3. 验证订单是否属于该阿姨
        if (!auntOneId.equals(domesticOrder.getPractitionerOneid())) {
            log.error("订单不属于该阿姨，orderId: {}, auntOneId: {}", orderId, auntOneId);
            throw new RuntimeException("订单不属于该阿姨");
        }

        // 4. 验证订单状态是否允许拒绝
        if (!"pending".equals(mainOrder.getOrderStatus())) {
            log.error("订单状态不允许拒绝，orderId: {}, currentStatus: {}", orderId, mainOrder.getOrderStatus());
            throw new RuntimeException("订单状态不允许拒绝");
        }

        // 5. 更新主订单状态为已拒绝，并保存拒绝原因
        PublicbizOrderDO updateOrder = new PublicbizOrderDO();
        updateOrder.setId(orderId);
        updateOrder.setOrderStatus("rejected");
        updateOrder.setRejectReason(rejectReason); // 将拒绝原因保存到专门的字段
        updateOrder.setUpdateTime(LocalDateTime.now());
        updateOrder.setUpdater(auntOneId);
        
        int updateCount = orderMapper.updateById(updateOrder);
        if (updateCount == 0) {
            log.error("更新订单状态失败，orderId: {}", orderId);
            throw new RuntimeException("更新订单状态失败");
        }

        log.info("阿姨拒绝订单成功，orderId: {}, auntOneId: {}, 新状态: rejected, 拒绝原因: {}", 
                orderId, auntOneId, rejectReason);
    }

    /**
     * 转换为订单列表响应VO
     */
    private AuntOrderListRespVO convertToOrderListRespVO(DomesticOrderDO order) {
        AuntOrderListRespVO vo = new AuntOrderListRespVO();
        vo.setOrderId(order.getOrderId());
        vo.setOrderNo(order.getOrderNo());
        vo.setCustomerName(order.getCustomerName());
        vo.setCustomerPhone(order.getCustomerPhone());
        vo.setServiceAddress(order.getServiceAddress());
        vo.setServicePackageName(order.getServicePackageName());
        
        // 转换日期
        if (order.getServiceStartDate() != null) {
            vo.setServiceStartDate(order.getServiceStartDate().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDate());
        }
        if (order.getServiceEndDate() != null) {
            vo.setServiceEndDate(order.getServiceEndDate().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDate());
        }
        
        vo.setServiceDuration(order.getServiceDuration());
        vo.setServiceAmount(order.getActualAmount());
        
        // 获取主订单的真实状态
        PublicbizOrderDO mainOrder = orderMapper.selectById(order.getOrderId());
        if (mainOrder != null) {
            vo.setOrderStatus(mainOrder.getOrderStatus());
        } else {
            vo.setOrderStatus("unknown"); // 如果查询不到主订单，设置为未知状态
        }
        
        vo.setCreateTime(order.getCreateTime().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime());
        vo.setAgencyName(order.getAgencyName());
        
        return vo;
    }

    /**
     * 转换为订单详情响应VO
     */
    private AuntOrderDetailRespVO convertToOrderDetailRespVO(DomesticOrderDO domesticOrder, PublicbizOrderDO mainOrder) {
        AuntOrderDetailRespVO vo = new AuntOrderDetailRespVO();
        vo.setOrderId(domesticOrder.getOrderId());
        vo.setOrderNo(domesticOrder.getOrderNo());
        vo.setOrderStatus(mainOrder.getOrderStatus()); // 使用主订单的真实状态
        vo.setPaymentStatus(mainOrder.getPaymentStatus()); // 使用主订单的真实支付状态
        vo.setSettlementStatus(mainOrder.getSettlementStatus()); // 使用主订单的真实结算状态
        vo.setCreateTime(domesticOrder.getCreateTime().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime());
        vo.setTotalAmount(domesticOrder.getActualAmount());
        vo.setPractitionerIncome(domesticOrder.getServiceFee());
        vo.setPlatformIncome(domesticOrder.getPlatformFee());
        vo.setAgencyName(domesticOrder.getAgencyName());
        
        // 设置客户信息
        AuntOrderDetailRespVO.CustomerInfo customerInfo = new AuntOrderDetailRespVO.CustomerInfo();
        customerInfo.setCustomerName(domesticOrder.getCustomerName());
        customerInfo.setCustomerPhone(domesticOrder.getCustomerPhone());
        customerInfo.setServiceAddress(domesticOrder.getServiceAddress());
        customerInfo.setCustomerRemark(domesticOrder.getCustomerRemark());
        vo.setCustomerInfo(customerInfo);
        
        // 设置服务信息
        AuntOrderDetailRespVO.ServiceInfo serviceInfo = new AuntOrderDetailRespVO.ServiceInfo();
        serviceInfo.setServicePackageName(domesticOrder.getServicePackageName());
        serviceInfo.setServicePackagePrice(domesticOrder.getServicePackagePrice());
        if (domesticOrder.getServiceStartDate() != null) {
            serviceInfo.setServiceStartDate(domesticOrder.getServiceStartDate().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDate());
        }
        if (domesticOrder.getServiceEndDate() != null) {
            serviceInfo.setServiceEndDate(domesticOrder.getServiceEndDate().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDate());
        }
        serviceInfo.setServiceDuration(domesticOrder.getServiceDuration());
        serviceInfo.setServiceDescription(domesticOrder.getServiceDescription());
        serviceInfo.setServiceDetails(domesticOrder.getServiceDetails());
        serviceInfo.setServiceProcess(domesticOrder.getServiceProcess());
        vo.setServiceInfo(serviceInfo);
        
        return vo;
    }
} 