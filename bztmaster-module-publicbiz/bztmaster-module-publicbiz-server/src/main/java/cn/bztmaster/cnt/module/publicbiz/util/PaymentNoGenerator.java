package cn.bztmaster.cnt.module.publicbiz.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 支付单号生成器
 *
 * <AUTHOR>
 */
public class PaymentNoGenerator {

    private static final String PAYMENT_PREFIX = "PAY";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 生成支付单号
     * 格式：PAY + yyyyMMdd + 6位随机数
     * 示例：PAY202412190001
     *
     * @return 支付单号
     */
    public static String generatePaymentNo() {
        String dateStr = LocalDateTime.now().format(DATE_FORMATTER);
        String randomStr = String.format("%06d", (int) (Math.random() * 1000000));
        return PAYMENT_PREFIX + dateStr + randomStr;
    }

    /**
     * 生成支付单号（带重试机制）
     * 如果生成的单号已存在，会重新生成
     *
     * @param existsChecker 单号存在性检查器
     * @param maxRetries 最大重试次数
     * @return 支付单号
     */
    public static String generatePaymentNoWithRetry(PaymentNoExistsChecker existsChecker, int maxRetries) {
        for (int i = 0; i < maxRetries; i++) {
            String paymentNo = generatePaymentNo();
            if (!existsChecker.exists(paymentNo)) {
                return paymentNo;
            }
        }
        // 如果重试次数用完，使用时间戳+随机数的方式
        return PAYMENT_PREFIX + System.currentTimeMillis() + IdUtil.getSnowflakeNextId();
    }

    /**
     * 支付单号存在性检查器接口
     */
    @FunctionalInterface
    public interface PaymentNoExistsChecker {
        /**
         * 检查支付单号是否存在
         *
         * @param paymentNo 支付单号
         * @return true-存在，false-不存在
         */
        boolean exists(String paymentNo);
    }
}






