package cn.bztmaster.cnt.module.publicbiz.service.order.impl;

import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderLogDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderLogMapper;
import cn.bztmaster.cnt.module.publicbiz.service.order.OrderLogService;
import cn.bztmaster.cnt.module.publicbiz.util.OrderLogContentBuilder;
import cn.bztmaster.cnt.framework.web.core.util.WebFrameworkUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单日志记录服务实现类
 * 支持所有类型的订单操作日志记录
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderLogServiceImpl implements OrderLogService {

    @Resource
    private PublicbizOrderLogMapper orderLogMapper;

    @Override
    public void recordOrderCreate(String orderNo, String orderType, Map<String, Object> details,
                                Long operatorId, String operatorName, String operatorRole) {
        try {
            log.info("开始记录订单创建日志，订单号：{}，订单类型：{}", orderNo, orderType);
            
            // 生成日志内容
            String logContent = OrderLogContentBuilder.buildCreateLogContent(
                    (String) details.get("projectName"),
                    (String) details.get("universityName"),
                    (String) details.get("enterpriseName"),
                    details.get("totalAmount")
            );
            
            // 插入日志记录
            insertOrderLog(orderNo, orderType, "订单创建", "创建" + getOrderTypeName(orderType) + "订单", 
                    logContent, "", "draft", operatorId, operatorName, operatorRole);
            
            log.info("订单创建日志记录成功");
        } catch (Exception e) {
            log.error("记录订单创建日志失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
        }
    }

    @Override
    public void recordOrderEdit(String orderNo, String orderType, List<FieldChange> changes,
                              String oldStatus, String newStatus,
                              Long operatorId, String operatorName, String operatorRole) {
        try {
            log.info("开始记录订单编辑日志，订单号：{}，订单类型：{}", orderNo, orderType);
            
            // 生成日志内容
            String logContent = OrderLogContentBuilder.buildEditLogContent(
                    convertToFieldChanges(changes),
                    String.format("更新了%s订单【%s】", getOrderTypeName(orderType), orderNo)
            );
            
            // 插入日志记录
            insertOrderLog(orderNo, orderType, "订单编辑", "更新" + getOrderTypeName(orderType) + "订单", 
                    logContent, oldStatus, newStatus, operatorId, operatorName, operatorRole);
            
            log.info("订单编辑日志记录成功");
        } catch (Exception e) {
            log.error("记录订单编辑日志失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
        }
    }

    @Override
    public void recordOrderStatusChange(String orderNo, String orderType, String changeType,
                                     String oldStatus, String newStatus, String description,
                                     Long operatorId, String operatorName, String operatorRole) {
        try {
            log.info("开始记录订单状态变更日志，订单号：{}，变更类型：{}", orderNo, changeType);
            
            String logContent;
            String logTitle;
            
            switch (changeType) {
                case "审批通过":
                    logContent = OrderLogContentBuilder.buildApprovalLogContent(oldStatus, newStatus, description);
                    logTitle = "审批通过";
                    break;
                case "确认收款":
                    logContent = OrderLogContentBuilder.buildPaymentConfirmLogContent(
                            oldStatus, newStatus, oldStatus, newStatus, description);
                    logTitle = "确认收款";
                    break;
                default:
                    // 自定义状态变更
                    logContent = OrderLogContentBuilder.buildEditLogContent(
                            Arrays.asList(OrderLogContentBuilder.FieldChange.builder()
                                    .field("订单状态")
                                    .oldValue(oldStatus)
                                    .newValue(newStatus)
                                    .build()),
                            description
                    );
                    logTitle = changeType;
                    break;
            }
            
            // 插入日志记录
            insertOrderLog(orderNo, orderType, changeType, logTitle, 
                    logContent, oldStatus, newStatus, operatorId, operatorName, operatorRole);
            
            log.info("订单状态变更日志记录成功");
        } catch (Exception e) {
            log.error("记录订单状态变更日志失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
        }
    }

    @Override
    public void recordOrderDelete(String orderNo, String orderType, String projectName,
                                Long operatorId, String operatorName, String operatorRole) {
        try {
            log.info("开始记录订单删除日志，订单号：{}，订单类型：{}", orderNo, orderType);
            
            // 生成日志内容
            String logContent = OrderLogContentBuilder.buildDeleteLogContent(orderNo, projectName);
            
            // 插入日志记录
            insertOrderLog(orderNo, orderType, "订单删除", "删除" + getOrderTypeName(orderType) + "订单", 
                    logContent, "未知", null, operatorId, operatorName, operatorRole);
            
            log.info("订单删除日志记录成功");
        } catch (Exception e) {
            log.error("记录订单删除日志失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
        }
    }

    @Override
    public void recordCustomLog(String orderNo, String orderType, String logType, String logTitle,
                              String logContent, String oldStatus, String newStatus,
                              Long operatorId, String operatorName, String operatorRole) {
        try {
            log.info("开始记录自定义操作日志，订单号：{}，日志类型：{}", orderNo, logType);
            
            // 插入日志记录
            insertOrderLog(orderNo, orderType, logType, logTitle, 
                    logContent, oldStatus, newStatus, operatorId, operatorName, operatorRole);
            
            log.info("自定义操作日志记录成功");
        } catch (Exception e) {
            log.error("记录自定义操作日志失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
        }
    }

    /**
     * 插入订单操作日志
     */
    private void insertOrderLog(String orderNo, String orderType, String logType, String logTitle,
                              String logContent, String oldStatus, String newStatus,
                              Long operatorId, String operatorName, String operatorRole) {
        try {
            // 如果没有提供操作人信息，尝试从当前登录用户获取
            if (operatorId == null) {
                operatorId = WebFrameworkUtils.getLoginUserId();
            }
            if (operatorName == null) {
                operatorName = "系统管理员";
            }
            if (operatorRole == null) {
                operatorRole = "管理员";
            }
            
            PublicbizOrderLogDO orderLog = PublicbizOrderLogDO.builder()
                    .orderNo(orderNo)
                    .logType(logType)
                    .logTitle(logTitle)
                    .logContent(logContent)
                    .oldStatus(oldStatus != null ? oldStatus : "")
                    .newStatus(newStatus != null ? newStatus : "")
                    .operatorId(operatorId)
                    .operatorName(operatorName)
                    .operatorRole(operatorRole)
                    .relatedPartyType(orderType)
                    .relatedPartyName(getOrderTypeName(orderType))
                    .build();
            
            orderLogMapper.insert(orderLog);
        } catch (Exception e) {
            log.error("插入订单操作日志失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            throw new RuntimeException("插入订单操作日志失败", e);
        }
    }

    /**
     * 转换字段变更列表
     */
    private List<OrderLogContentBuilder.FieldChange> convertToFieldChanges(List<FieldChange> changes) {
        return changes.stream()
                .map(change -> OrderLogContentBuilder.FieldChange.builder()
                        .field(change.getField())
                        .oldValue(change.getOldValue())
                        .newValue(change.getNewValue())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 获取订单类型的中文名称
     */
    private String getOrderTypeName(String orderType) {
        switch (orderType) {
            case "practice":
                return "高校实践";
            case "training":
                return "企业培训";
            case "consulting":
                return "咨询服务";
            case "recruitment":
                return "招聘服务";
            default:
                return orderType;
        }
    }
}
