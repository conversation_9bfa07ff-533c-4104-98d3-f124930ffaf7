package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 高校实践订单详情请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "高校实践订单详情请求")
@Data
public class UniversityPracticeOrderDetailReqVO {

    @Schema(description = "订单ID", example = "1", required = true)
    @NotNull(message = "订单ID不能为空")
    private Long id;

    @Schema(description = "订单号", example = "HP20250811001", required = true)
    @NotNull(message = "订单号不能为空")
    private String orderNo;
}





