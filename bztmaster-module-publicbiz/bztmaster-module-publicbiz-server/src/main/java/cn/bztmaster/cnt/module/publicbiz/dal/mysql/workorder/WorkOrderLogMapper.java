package cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder;

import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderLogDO;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工单处理日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WorkOrderLogMapper extends BaseMapperX<WorkOrderLogDO> {

    /**
     * 根据工单ID查询日志列表
     *
     * @param workOrderId 工单ID
     * @return 日志列表
     */
    List<WorkOrderLogDO> selectByWorkOrderId(@Param("workOrderId") Long workOrderId);

    /**
     * 根据工单编号查询日志列表
     *
     * @param workOrderNo 工单编号
     * @return 日志列表
     */
    List<WorkOrderLogDO> selectByWorkOrderNo(@Param("workOrderNo") String workOrderNo);

    /**
     * 根据工单ID和操作类型查询日志列表
     *
     * @param workOrderId 工单ID
     * @param operationType 操作类型
     * @return 日志列表
     */
    List<WorkOrderLogDO> selectByWorkOrderIdAndOperationType(@Param("workOrderId") Long workOrderId,
                                                             @Param("operationType") String operationType);
}
