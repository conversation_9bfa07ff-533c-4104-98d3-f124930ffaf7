package cn.bztmaster.cnt.module.publicbiz.service.order.impl;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderDropdownDataReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderDropdownDataRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.business.BusinessMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.leads.LeadInfoMapper;
import java.util.ArrayList;
import java.util.Objects;
import java.util.Set;
import java.util.HashSet;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.BusinessModuleEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.LeadSourceEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.LeadStatusEnum;
import cn.bztmaster.cnt.module.publicbiz.service.order.OrderDropdownDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单下拉数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderDropdownDataServiceImpl implements OrderDropdownDataService {

    @Resource
    private BusinessMapper businessMapper;

    @Resource
    private LeadInfoMapper leadInfoMapper;

    @Override
    public OrderDropdownDataRespVO getDropdownData(OrderDropdownDataReqVO reqVO) {
        try {
            log.info("开始获取下拉数据，请求参数：orderType={}, businessLine={}", 
                    reqVO.getOrderType(), reqVO.getBusinessLine());
            
            OrderDropdownDataRespVO respVO = new OrderDropdownDataRespVO();

            // 根据订单类型获取对应的业务类型
            String businessType = mapOrderTypeToBusinessType(reqVO.getOrderType());
            Integer businessModule = mapOrderTypeToBusinessModule(reqVO.getOrderType());
            
            log.info("映射结果：businessType={}, businessModule={}", businessType, businessModule);

        // 1. 获取商机列表
        log.info("开始查询商机数据，业务类型：{}", businessType);
        List<BusinessDO> businessList = businessMapper.selectListByBusinessType(businessType);
        if (businessList == null) {
            businessList = new ArrayList<>();
        }
        log.info("商机查询结果：数量={}", businessList.size());
        
        // 安全处理商机数据
        List<OrderDropdownDataRespVO.BusinessOption> businessOptions = new ArrayList<>();
        for (BusinessDO business : businessList) {
            try {
                if (business != null) {
                    OrderDropdownDataRespVO.BusinessOption option = convertToBusinessOption(business);
                    if (option != null) {
                        businessOptions.add(option);
                    }
                }
            } catch (Exception e) {
                log.warn("转换商机数据时发生异常，跳过该记录：{}", e.getMessage());
            }
        }
        respVO.setBusinessOptions(businessOptions);

        // 2. 获取线索列表
        log.info("开始查询线索数据，业务模块：{}", businessModule);
        List<LeadInfoDO> leadList = leadInfoMapper.selectListByBusinessModule(businessModule);
        if (leadList == null) {
            leadList = new ArrayList<>();
        }
        log.info("线索查询结果：数量={}", leadList.size());
        
        // 安全处理线索数据
        List<OrderDropdownDataRespVO.LeadOption> leadOptions = new ArrayList<>();
        for (LeadInfoDO lead : leadList) {
            try {
                if (lead != null) {
                    OrderDropdownDataRespVO.LeadOption option = convertToLeadOption(lead);
                    if (option != null) {
                        leadOptions.add(option);
                    }
                }
            } catch (Exception e) {
                log.warn("转换线索数据时发生异常，跳过该记录：{}", e.getMessage());
            }
        }
        respVO.setLeadOptions(leadOptions);

        // 3. 获取客户列表（从商机中提取唯一客户）
        respVO.setCustomerOptions(extractUniqueCustomers(businessList));

        // 4. 获取负责人列表（从商机中提取唯一负责人）
        respVO.setManagerOptions(extractUniqueManagers(businessList));

        log.info("下拉数据获取完成，商机={}, 线索={}, 客户={}, 负责人={}", 
                respVO.getBusinessOptions().size(),
                respVO.getLeadOptions().size(),
                respVO.getCustomerOptions().size(),
                respVO.getManagerOptions().size());

        return respVO;
        } catch (Exception e) {
            log.error("获取下拉数据过程中发生异常", e);
            throw e;
        }
    }

    /**
     * 将订单类型映射到业务类型
     */
    private String mapOrderTypeToBusinessType(String orderType) {
        switch (orderType) {
            case "practice":
                return "高校业务";
            case "training":
                return "培训业务";
            case "domestic":
                return "家政业务";
            case "certification":
                return "认证业务";
            default:
                return "高校业务"; // 默认返回高校业务
        }
    }

    /**
     * 将订单类型映射到业务模块
     */
    private Integer mapOrderTypeToBusinessModule(String orderType) {
        switch (orderType) {
            case "practice":
                return 1; // 高校业务
            case "training":
                return 3; // 培训业务
            case "domestic":
                return 2; // 家政业务
            case "certification":
                return 4; // 认证业务
            default:
                return 1; // 默认返回高校业务
        }
    }

    /**
     * 转换为商机选项
     */
    private OrderDropdownDataRespVO.BusinessOption convertToBusinessOption(BusinessDO business) {
        if (business == null) {
            return null;
        }
        
        OrderDropdownDataRespVO.BusinessOption option = new OrderDropdownDataRespVO.BusinessOption();
        option.setId(business.getId());
        option.setName(business.getName() != null ? business.getName() : "");
        option.setCustomerName(business.getCustomerName() != null ? business.getCustomerName() : "");
        option.setBusinessType(business.getBusinessType() != null ? business.getBusinessType() : "");
        option.setTotalPrice(business.getTotalPrice());
        option.setBusinessStage(business.getBusinessStage() != null ? business.getBusinessStage() : "");
        option.setOwnerUserName(business.getOwnerUserName() != null ? business.getOwnerUserName() : "");
        return option;
    }

    /**
     * 转换为线索选项
     */
    private OrderDropdownDataRespVO.LeadOption convertToLeadOption(LeadInfoDO lead) {
        if (lead == null) {
            return null;
        }
        
        OrderDropdownDataRespVO.LeadOption option = new OrderDropdownDataRespVO.LeadOption();
        option.setId(lead.getId());
        option.setLeadId(lead.getLeadId() != null ? lead.getLeadId() : "");
        option.setCustomerName(lead.getCustomerName() != null ? lead.getCustomerName() : "");
        option.setCustomerPhone(lead.getCustomerPhone() != null ? lead.getCustomerPhone() : "");
        
        // 获取业务模块名称
        BusinessModuleEnum businessModule = BusinessModuleEnum.getByType(lead.getBusinessModule());
        option.setBusinessModule(businessModule != null ? businessModule.getDesc() : "");
        
        // 获取线索来源名称
        LeadSourceEnum leadSource = LeadSourceEnum.getByType(lead.getLeadSource());
        option.setLeadSource(leadSource != null ? leadSource.getDesc() : "");
        
        // 获取线索状态名称
        LeadStatusEnum leadStatus = LeadStatusEnum.getByType(lead.getLeadStatus());
        option.setLeadStatus(leadStatus != null ? leadStatus.getDesc() : "");
        
        return option;
    }

    /**
     * 提取唯一客户列表
     */
    private List<OrderDropdownDataRespVO.CustomerOption> extractUniqueCustomers(List<BusinessDO> businessList) {
        if (businessList == null || businessList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 使用Set来去重，避免groupingBy的复杂性
        Set<String> uniqueCustomerKeys = new HashSet<>();
        List<OrderDropdownDataRespVO.CustomerOption> result = new ArrayList<>();
        
        for (BusinessDO business : businessList) {
            if (business != null && business.getCustomerId() != null && business.getCustomerName() != null) {
                String key = business.getCustomerId() + "_" + business.getCustomerName();
                if (!uniqueCustomerKeys.contains(key)) {
                    uniqueCustomerKeys.add(key);
                    
                    OrderDropdownDataRespVO.CustomerOption customer = new OrderDropdownDataRespVO.CustomerOption();
                    customer.setId(business.getCustomerId());
                    customer.setCustomerName(business.getCustomerName());
                    result.add(customer);
                }
            }
        }
        
        return result;
    }

    /**
     * 提取唯一负责人列表
     */
    private List<OrderDropdownDataRespVO.ManagerOption> extractUniqueManagers(List<BusinessDO> businessList) {
        if (businessList == null || businessList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 使用Set来去重，避免groupingBy的复杂性
        Set<String> uniqueManagerKeys = new HashSet<>();
        List<OrderDropdownDataRespVO.ManagerOption> result = new ArrayList<>();
        
        for (BusinessDO business : businessList) {
            if (business != null && business.getOwnerUserId() != null && business.getOwnerUserName() != null) {
                String key = business.getOwnerUserId() + "_" + business.getOwnerUserName();
                if (!uniqueManagerKeys.contains(key)) {
                    uniqueManagerKeys.add(key);
                    
                    OrderDropdownDataRespVO.ManagerOption manager = new OrderDropdownDataRespVO.ManagerOption();
                    manager.setId(business.getOwnerUserId());
                    manager.setManagerName(business.getOwnerUserName());
                    result.add(manager);
                }
            }
        }
        
        return result;
    }
}
