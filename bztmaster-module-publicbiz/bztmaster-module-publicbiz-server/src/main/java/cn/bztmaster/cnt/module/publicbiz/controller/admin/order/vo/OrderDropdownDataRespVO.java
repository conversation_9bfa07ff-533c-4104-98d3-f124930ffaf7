package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单下拉数据响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "订单下拉数据响应")
@Data
public class OrderDropdownDataRespVO {

    @Schema(description = "商机列表")
    private List<BusinessOption> businessOptions;

    @Schema(description = "线索列表")
    private List<LeadOption> leadOptions;

    @Schema(description = "客户列表")
    private List<CustomerOption> customerOptions;

    @Schema(description = "负责人列表")
    private List<ManagerOption> managerOptions;

    @Data
    @Schema(description = "商机选项")
    public static class BusinessOption {
        @Schema(description = "商机ID", example = "1")
        private Long id;

        @Schema(description = "商机名称", example = "YY大学春季实习项目商机")
        private String name;

        @Schema(description = "客户名称", example = "YY大学")
        private String customerName;

        @Schema(description = "业务类型", example = "高校")
        private String businessType;

        @Schema(description = "商机金额", example = "500000.00")
        private BigDecimal totalPrice;

        @Schema(description = "销售阶段", example = "方案报价")
        private String businessStage;

        @Schema(description = "负责人姓名", example = "张三")
        private String ownerUserName;
    }

    @Data
    @Schema(description = "线索选项")
    public static class LeadOption {
        @Schema(description = "线索ID", example = "1")
        private Long id;

        @Schema(description = "线索编号", example = "LX202406001")
        private String leadId;

        @Schema(description = "客户姓名", example = "李四")
        private String customerName;

        @Schema(description = "联系电话", example = "***********")
        private String customerPhone;

        @Schema(description = "业务模块", example = "高校业务")
        private String businessModule;

        @Schema(description = "线索来源", example = "官网注册")
        private String leadSource;

        @Schema(description = "线索状态", example = "未处理")
        private String leadStatus;
    }

    @Data
    @Schema(description = "客户选项")
    public static class CustomerOption {
        @Schema(description = "客户ID", example = "1")
        private Long id;

        @Schema(description = "客户名称", example = "YY大学")
        private String customerName;

        @Schema(description = "联系电话", example = "028-12345678")
        private String customerPhone;
    }

    @Data
    @Schema(description = "负责人选项")
    public static class ManagerOption {
        @Schema(description = "负责人ID", example = "1")
        private Long id;

        @Schema(description = "负责人姓名", example = "张三")
        private String managerName;

        @Schema(description = "负责人电话", example = "***********")
        private String managerPhone;
    }
}





