package cn.bztmaster.cnt.module.publicbiz.service.order;

import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderLogDO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 订单日志记录服务接口
 * 支持所有类型的订单操作日志记录
 *
 * <AUTHOR>
 */
public interface OrderLogService {

    /**
     * 记录订单创建日志
     *
     * @param orderNo 订单号
     * @param orderType 订单类型（如：practice-高校实践、training-企业培训等）
     * @param details 订单详情信息
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param operatorRole 操作人角色
     */
    void recordOrderCreate(String orderNo, String orderType, Map<String, Object> details,
                          Long operatorId, String operatorName, String operatorRole);

    /**
     * 记录订单编辑日志
     *
     * @param orderNo 订单号
     * @param orderType 订单类型
     * @param changes 字段变更列表
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param operatorRole 操作人角色
     */
    void recordOrderEdit(String orderNo, String orderType, List<FieldChange> changes,
                        String oldStatus, String newStatus,
                        Long operatorId, String operatorName, String operatorRole);

    /**
     * 记录订单状态变更日志
     *
     * @param orderNo 订单号
     * @param orderType 订单类型
     * @param changeType 变更类型（如：审批通过、确认收款等）
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param description 变更描述
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param operatorRole 操作人角色
     */
    void recordOrderStatusChange(String orderNo, String orderType, String changeType,
                               String oldStatus, String newStatus, String description,
                               Long operatorId, String operatorName, String operatorRole);

    /**
     * 记录订单删除日志
     *
     * @param orderNo 订单号
     * @param orderType 订单类型
     * @param projectName 项目名称
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param operatorRole 操作人角色
     */
    void recordOrderDelete(String orderNo, String orderType, String projectName,
                          Long operatorId, String operatorName, String operatorRole);

    /**
     * 记录自定义操作日志
     *
     * @param orderNo 订单号
     * @param orderType 订单类型
     * @param logType 日志类型
     * @param logTitle 日志标题
     * @param logContent 日志内容
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param operatorRole 操作人角色
     */
    void recordCustomLog(String orderNo, String orderType, String logType, String logTitle,
                        String logContent, String oldStatus, String newStatus,
                        Long operatorId, String operatorName, String operatorRole);

    /**
     * 字段变更信息
     */
    class FieldChange {
        private String field;
        private String oldValue;
        private String newValue;

        public FieldChange(String field, String oldValue, String newValue) {
            this.field = field;
            this.oldValue = oldValue;
            this.newValue = newValue;
        }

        // Getters and Setters
        public String getField() { return field; }
        public void setField(String field) { this.field = field; }
        public String getOldValue() { return oldValue; }
        public void setOldValue(String oldValue) { this.oldValue = oldValue; }
        public String getNewValue() { return newValue; }
        public void setNewValue(String newValue) { this.newValue = newValue; }
    }
}


