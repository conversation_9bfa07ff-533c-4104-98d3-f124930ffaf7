package cn.bztmaster.cnt.module.publicbiz.controller.admin.order;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.DomesticTaskGenerateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.DomesticTaskGenerateRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.service.domestic.DomesticTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 家政服务任务订单
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 家政服务任务订单")
@RestController
@RequestMapping("/publicbiz/domestic-task-order")
@Validated
@Slf4j
public class DomesticTaskOrderController {

    @Resource
    private DomesticTaskService domesticTaskService;

    @Resource
    private PublicbizOrderMapper orderMapper;

    @PostMapping("/generate-tasks")
    @Operation(summary = "批量生成家政服务任务")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:generate')")
    public CommonResult<Map<String, Object>> generateTasks() {
        try {
            log.info("开始批量生成家政服务任务");

            // 1. 查询符合条件的订单
            List<PublicbizOrderDO> eligibleOrders = orderMapper.selectList(
                new LambdaQueryWrapperX<PublicbizOrderDO>()
                    .eq(PublicbizOrderDO::getOrderType, "domestic")
                    .eq(PublicbizOrderDO::getPaymentStatus, "paid")
                    .eq(PublicbizOrderDO::getOrderStatus, "executing")
            );

            log.info("查询到符合条件的家政订单数量: {}", eligibleOrders.size());

            // 2. 批量处理结果统计
            int totalOrders = eligibleOrders.size();
            int successCount = 0;
            int skipCount = 0;
            int failCount = 0;
            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> failList = new ArrayList<>();

            // 3. 遍历处理每个订单
            for (PublicbizOrderDO order : eligibleOrders) {
                try {
                    // 检查是否已生成任务
                    if (domesticTaskService.hasTasksGenerated(order.getId())) {
                        log.debug("订单 {} 已生成任务，跳过处理", order.getOrderNo());
                        skipCount++;
                        continue;
                    }

                    // 生成任务
                    DomesticTaskGenerateReqVO reqVO = new DomesticTaskGenerateReqVO();
                    reqVO.setOrderId(order.getId());
                    reqVO.setForceRegenerate(false);

                    DomesticTaskGenerateRespVO result = domesticTaskService.generateTasks(reqVO);

                    // 记录成功结果
                    Map<String, Object> successItem = new HashMap<>();
                    successItem.put("orderId", order.getId());
                    successItem.put("orderNo", order.getOrderNo());
                    successItem.put("taskCount", result.getTaskCount());
                    successItem.put("packageType", result.getPackageType());
                    successList.add(successItem);

                    successCount++;
                    log.info("订单 {} 任务生成成功，生成任务数量: {}", order.getOrderNo(), result.getTaskCount());

                } catch (Exception e) {
                    // 记录失败结果
                    Map<String, Object> failItem = new HashMap<>();
                    failItem.put("orderId", order.getId());
                    failItem.put("orderNo", order.getOrderNo());
                    failItem.put("errorMessage", e.getMessage());
                    failList.add(failItem);

                    failCount++;
                    log.error("订单 {} 任务生成失败: {}", order.getOrderNo(), e.getMessage());
                }
            }

            // 4. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("totalOrders", totalOrders);
            result.put("successCount", successCount);
            result.put("skipCount", skipCount);
            result.put("failCount", failCount);
            result.put("successList", successList);
            result.put("failList", failList);

            log.info("批量生成家政服务任务完成 - 总订单数: {}, 成功: {}, 跳过: {}, 失败: {}",
                    totalOrders, successCount, skipCount, failCount);

            return success(result);

        } catch (Exception e) {
            log.error("批量生成家政服务任务失败", e);
            return CommonResult.error(500, "批量生成任务失败: " + e.getMessage());
        }
    }
}
