package cn.bztmaster.cnt.module.publicbiz.controller.admin.order;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.service.domestic.DomesticTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 家政服务任务订单
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 家政服务任务订单")
@RestController
@RequestMapping("/publicbiz/domestic-task-order")
@Validated
@Slf4j
public class DomesticTaskOrderController {

    @Resource
    private DomesticTaskService domesticTaskService;

    @PostMapping("/generate-tasks")
    @Operation(summary = "批量生成家政服务任务")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:generate')")
    public CommonResult<Map<String, Object>> generateTasks() {
        try {
            log.info("接收到批量生成家政服务任务请求");

            // 调用服务层执行批量任务生成业务逻辑
            Map<String, Object> result = domesticTaskService.batchGenerateTasks();

            log.info("批量生成家政服务任务请求处理完成");

            return success(result);

        } catch (Exception e) {
            log.error("批量生成家政服务任务请求处理失败", e);
            return CommonResult.error(500, "批量生成任务失败: " + e.getMessage());
        }
    }
}
