package cn.bztmaster.cnt.module.publicbiz.controller.admin.order;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.DomesticTaskGenerateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.DomesticTaskGenerateRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import cn.bztmaster.cnt.module.publicbiz.service.domestic.DomesticTaskService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;
import static cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants.*;

/**
 * 管理后台 - 家政服务任务订单
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 家政服务任务订单")
@RestController
@RequestMapping("/publicbiz/domestic-task-order")
@Validated
@Slf4j
public class DomesticTaskOrderController {

    @Resource
    private DomesticTaskService domesticTaskService;

    @PostMapping("/generate-tasks")
    @Operation(summary = "生成家政服务任务")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:generate')")
    @LogRecord(type = "DOMESTIC_TASK_ORDER", subType = "生成家政服务任务", 
               bizNo = "{{#reqVO.orderId}}", 
               success = "为订单【{{#reqVO.orderId}}】生成了{{#result.data.taskCount}}个家政服务任务")
    public CommonResult<DomesticTaskGenerateRespVO> generateTasks(@Valid @RequestBody DomesticTaskGenerateReqVO reqVO) {
        try {
            log.info("开始生成家政服务任务，请求参数: {}", reqVO);
            
            // 参数验证
            if (reqVO.getOrderId() == null || reqVO.getOrderId() <= 0) {
                return CommonResult.error(400, "订单ID不能为空且必须大于0");
            }
            
            // 生成任务
            DomesticTaskGenerateRespVO result = domesticTaskService.generateTasks(reqVO);
            
            log.info("家政服务任务生成成功，订单ID: {}, 生成任务数量: {}", 
                    reqVO.getOrderId(), result.getTaskCount());
            
            return success(result);
            
        } catch (Exception e) {
            log.error("生成家政服务任务失败，订单ID: {}", reqVO.getOrderId(), e);
            return CommonResult.error(500, "生成任务失败: " + e.getMessage());
        }
    }

    @GetMapping("/tasks/by-order/{orderId}")
    @Operation(summary = "根据订单ID查询任务列表")
    @Parameter(name = "orderId", description = "订单ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<List<DomesticTaskDO>> getTasksByOrderId(@PathVariable("orderId") Long orderId) {
        try {
            log.info("查询订单任务列表，订单ID: {}", orderId);
            
            // 参数验证
            if (orderId == null || orderId <= 0) {
                return CommonResult.error(400, "订单ID不能为空且必须大于0");
            }
            
            List<DomesticTaskDO> tasks = domesticTaskService.getTasksByOrderId(orderId);
            
            log.info("查询订单任务列表成功，订单ID: {}, 任务数量: {}", orderId, tasks.size());
            
            return success(tasks);
            
        } catch (Exception e) {
            log.error("查询订单任务列表失败，订单ID: {}", orderId, e);
            return CommonResult.error(500, "查询任务列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/tasks/by-domestic-order/{domesticOrderId}")
    @Operation(summary = "根据家政订单ID查询任务列表")
    @Parameter(name = "domesticOrderId", description = "家政订单ID", required = true, example = "2048")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<List<DomesticTaskDO>> getTasksByDomesticOrderId(@PathVariable("domesticOrderId") Long domesticOrderId) {
        try {
            log.info("查询家政订单任务列表，家政订单ID: {}", domesticOrderId);
            
            // 参数验证
            if (domesticOrderId == null || domesticOrderId <= 0) {
                return CommonResult.error(400, "家政订单ID不能为空且必须大于0");
            }
            
            List<DomesticTaskDO> tasks = domesticTaskService.getTasksByDomesticOrderId(domesticOrderId);
            
            log.info("查询家政订单任务列表成功，家政订单ID: {}, 任务数量: {}", domesticOrderId, tasks.size());
            
            return success(tasks);
            
        } catch (Exception e) {
            log.error("查询家政订单任务列表失败，家政订单ID: {}", domesticOrderId, e);
            return CommonResult.error(500, "查询任务列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/tasks/check-generated/{orderId}")
    @Operation(summary = "检查订单是否已生成任务")
    @Parameter(name = "orderId", description = "订单ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<Boolean> checkTasksGenerated(@PathVariable("orderId") Long orderId) {
        try {
            log.info("检查订单是否已生成任务，订单ID: {}", orderId);
            
            // 参数验证
            if (orderId == null || orderId <= 0) {
                return CommonResult.error(400, "订单ID不能为空且必须大于0");
            }
            
            boolean hasGenerated = domesticTaskService.hasTasksGenerated(orderId);
            
            log.info("检查订单任务生成状态完成，订单ID: {}, 已生成: {}", orderId, hasGenerated);
            
            return success(hasGenerated);
            
        } catch (Exception e) {
            log.error("检查订单任务生成状态失败，订单ID: {}", orderId, e);
            return CommonResult.error(500, "检查任务生成状态失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/tasks/by-order/{orderId}")
    @Operation(summary = "删除订单相关的所有任务")
    @Parameter(name = "orderId", description = "订单ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:delete')")
    @LogRecord(type = "DOMESTIC_TASK_ORDER", subType = "删除家政服务任务", 
               bizNo = "{{#orderId}}", 
               success = "删除了订单【{{#orderId}}】的所有家政服务任务")
    public CommonResult<Boolean> deleteTasksByOrderId(@PathVariable("orderId") Long orderId) {
        try {
            log.info("删除订单相关任务，订单ID: {}", orderId);
            
            // 参数验证
            if (orderId == null || orderId <= 0) {
                return CommonResult.error(400, "订单ID不能为空且必须大于0");
            }
            
            domesticTaskService.deleteTasksByOrderId(orderId);
            
            log.info("删除订单相关任务成功，订单ID: {}", orderId);
            
            return success(true);
            
        } catch (Exception e) {
            log.error("删除订单相关任务失败，订单ID: {}", orderId, e);
            return CommonResult.error(500, "删除任务失败: " + e.getMessage());
        }
    }
}
