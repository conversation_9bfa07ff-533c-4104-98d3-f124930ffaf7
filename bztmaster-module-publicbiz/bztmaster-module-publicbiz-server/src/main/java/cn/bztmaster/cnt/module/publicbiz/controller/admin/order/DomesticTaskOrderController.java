package cn.bztmaster.cnt.module.publicbiz.controller.admin.order;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.DomesticTaskGenerateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.DomesticTaskGenerateRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.domestic.DomesticTaskService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 家政服务任务订单
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 家政服务任务订单")
@RestController
@RequestMapping("/publicbiz/domestic-task-order")
@Validated
@Slf4j
public class DomesticTaskOrderController {

    @Resource
    private DomesticTaskService domesticTaskService;

    @PostMapping("/generate-tasks")
    @Operation(summary = "生成家政服务任务")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:generate')")
    @LogRecord(type = "DOMESTIC_TASK_ORDER", subType = "生成家政服务任务", 
               bizNo = "{{#reqVO.orderId}}", 
               success = "为订单【{{#reqVO.orderId}}】生成了{{#result.data.taskCount}}个家政服务任务")
    public CommonResult<DomesticTaskGenerateRespVO> generateTasks(@Valid @RequestBody DomesticTaskGenerateReqVO reqVO) {
        try {
            log.info("开始生成家政服务任务，请求参数: {}", reqVO);
            
            // 参数验证
            if (reqVO.getOrderId() == null || reqVO.getOrderId() <= 0) {
                return CommonResult.error(400, "订单ID不能为空且必须大于0");
            }
            
            // 生成任务
            DomesticTaskGenerateRespVO result = domesticTaskService.generateTasks(reqVO);
            
            log.info("家政服务任务生成成功，订单ID: {}, 生成任务数量: {}", 
                    reqVO.getOrderId(), result.getTaskCount());
            
            return success(result);
            
        } catch (Exception e) {
            log.error("生成家政服务任务失败，订单ID: {}", reqVO.getOrderId(), e);
            return CommonResult.error(500, "生成任务失败: " + e.getMessage());
        }
    }
}
