package cn.bztmaster.cnt.module.publicbiz.convert.order;

import cn.bztmaster.cnt.framework.common.util.collection.CollectionUtils;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.UniversityPracticeOrderRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.UniversityPracticeOrderSaveReqDTO;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderPageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderSaveReqVO;

import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizPracticeOrderDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;
import java.math.BigDecimal;
import cn.hutool.core.util.StrUtil;

/**
 * 高校实践订单转换类
 *
 * <AUTHOR>
 */
@Mapper
public interface UniversityPracticeOrderConvert {

    UniversityPracticeOrderConvert INSTANCE = Mappers.getMapper(UniversityPracticeOrderConvert.class);

    /**
     * 转换订单DO为分页响应VO
     *
     * @param order 订单DO
     * @param practiceOrder 高校实践订单详情DO
     * @return 分页响应VO
     */
    default UniversityPracticeOrderPageRespVO convert(PublicbizOrderDO order, PublicbizPracticeOrderDO practiceOrder) {
        UniversityPracticeOrderPageRespVO vo = BeanUtils.toBean(order, UniversityPracticeOrderPageRespVO.class);
        if (practiceOrder != null) {
            vo.setUniversityName(practiceOrder.getUniversityName());
            vo.setEnterpriseName(practiceOrder.getEnterpriseName());
            vo.setStudentCount(practiceOrder.getStudentCount());
            vo.setPracticeDuration(practiceOrder.getPracticeDuration());
            vo.setPracticeLocation(practiceOrder.getPracticeLocation());
        }
        return vo;
    }

    /**
     * 转换订单DO列表为分页响应VO列表
     *
     * @param orderList 订单DO列表
     * @param practiceOrderMap 高校实践订单详情DO映射
     * @return 分页响应VO列表
     */
    default List<UniversityPracticeOrderPageRespVO> convertList(List<PublicbizOrderDO> orderList, 
                                                               Map<Long, PublicbizPracticeOrderDO> practiceOrderMap) {
        return CollectionUtils.convertList(orderList, order -> convert(order, practiceOrderMap.get(order.getId())));
    }

    /**
     * 转换订单DO为详情响应VO
     *
     * @param order 订单DO
     * @param practiceOrder 高校实践订单详情DO
     * @return 详情响应VO
     */
    default UniversityPracticeOrderDetailRespVO convertDetail(PublicbizOrderDO order, PublicbizPracticeOrderDO practiceOrder) {
        UniversityPracticeOrderDetailRespVO vo = BeanUtils.toBean(order, UniversityPracticeOrderDetailRespVO.class);
        if (practiceOrder != null) {
            vo.setUniversityName(practiceOrder.getUniversityName());
            vo.setUniversityContact(practiceOrder.getUniversityContact());
            vo.setUniversityPhone(practiceOrder.getUniversityPhone());
            vo.setUniversityEmail(practiceOrder.getUniversityEmail());
            vo.setEnterpriseName(practiceOrder.getEnterpriseName());
            vo.setEnterpriseContact(practiceOrder.getEnterpriseContact());
            vo.setEnterprisePhone(practiceOrder.getEnterprisePhone());
            vo.setEnterpriseEmail(practiceOrder.getEnterpriseEmail());
            vo.setStudentCount(practiceOrder.getStudentCount());
            vo.setPracticeDuration(practiceOrder.getPracticeDuration());
            vo.setPracticeLocation(practiceOrder.getPracticeLocation());
            vo.setServiceFee(practiceOrder.getServiceFee());
            vo.setManagementFee(practiceOrder.getManagementFee());
            vo.setOtherFee(practiceOrder.getOtherFee());
        }
        
        // 映射收款信息字段（从现有字段获取）
        if (order != null) {
            // 收款金额 -> paidAmount
            vo.setCollectionAmount(order.getPaidAmount());
            // 收款方式 -> settlementMethod
            vo.setCollectionMethod(order.getSettlementMethod());
            // 收款日期 -> settlementTime的日期部分
            if (order.getSettlementTime() != null) {
                vo.setCollectionDate(order.getSettlementTime().toLocalDate());
            }
            // 操作人 -> selectorName
            vo.setOperatorName(order.getSelectorName());
            // 收款备注 -> remark（如果remark包含收款相关信息）
            if (StrUtil.isNotBlank(order.getRemark())) {
                vo.setCollectionRemark(order.getRemark());
            }
        }
        
        return vo;
    }

    /**
     * 转换保存请求VO为订单DO
     *
     * @param reqVO 保存请求VO
     * @return 订单DO
     */
    default PublicbizOrderDO convert(UniversityPracticeOrderSaveReqVO reqVO) {
        PublicbizOrderDO order = BeanUtils.toBean(reqVO, PublicbizOrderDO.class);
        
        // 记录转换后的对象信息（移除可能导致问题的System.out.println）
        // System.out.println("转换后的订单对象 - contractFileUrl: " + order.getContractFileUrl());
        
        order.setOrderType("practice");
        order.setBusinessLine("高校实践");
        order.setOrderStatus("draft");
        // 如果VO中没有设置paymentStatus，则使用默认值
        if (order.getPaymentStatus() == null) {
            order.setPaymentStatus("pending");
        }
        order.setContractStatus("unsigned");
        order.setSettlementStatus("pending");
        order.setPaidAmount(BigDecimal.ZERO);
        order.setRefundAmount(BigDecimal.ZERO);
        
        // 处理收款信息字段映射（使用现有字段）
        if ("paid".equals(reqVO.getPaymentStatus())) {
            // 使用paidAmount字段存储收款金额
            if (reqVO.getCollectionAmount() != null) {
                order.setPaidAmount(reqVO.getCollectionAmount());
            }
            // 使用settlementMethod字段存储收款方式
            if (reqVO.getCollectionMethod() != null) {
                order.setSettlementMethod(reqVO.getCollectionMethod());
            }
            // 使用settlementTime字段存储收款时间
            if (reqVO.getCollectionDate() != null) {
                order.setSettlementTime(reqVO.getCollectionDate().atStartOfDay());
            }
            // 使用selectorName字段存储操作人姓名
            if (reqVO.getOperatorName() != null) {
                order.setSelectorName(reqVO.getOperatorName());
            }
            // 使用remark字段存储收款备注（如果原remark为空）
            if (StrUtil.isBlank(order.getRemark()) && StrUtil.isNotBlank(reqVO.getCollectionRemark())) {
                order.setRemark(reqVO.getCollectionRemark());
            }
        }
        
        return order;
    }

    /**
     * 转换保存请求VO为高校实践订单详情DO
     *
     * @param reqVO 保存请求VO
     * @param orderId 订单ID
     * @return 高校实践订单详情DO
     */
    default PublicbizPracticeOrderDO convertDetail(UniversityPracticeOrderSaveReqVO reqVO, Long orderId) {
        PublicbizPracticeOrderDO practiceOrder = BeanUtils.toBean(reqVO, PublicbizPracticeOrderDO.class);
        practiceOrder.setOrderId(orderId);
        practiceOrder.setOrderNo(reqVO.getOrderNo());
        return practiceOrder;
    }

    /**
     * 转换订单DO为API响应DTO
     *
     * @param order 订单DO
     * @param practiceOrder 高校实践订单详情DO
     * @return API响应DTO
     */
    default UniversityPracticeOrderRespDTO convertApi(PublicbizOrderDO order, PublicbizPracticeOrderDO practiceOrder) {
        UniversityPracticeOrderRespDTO dto = BeanUtils.toBean(order, UniversityPracticeOrderRespDTO.class);
        if (practiceOrder != null) {
            dto.setUniversityName(practiceOrder.getUniversityName());
            dto.setUniversityContact(practiceOrder.getUniversityContact());
            dto.setUniversityPhone(practiceOrder.getUniversityPhone());
            dto.setUniversityEmail(practiceOrder.getUniversityEmail());
            dto.setEnterpriseName(practiceOrder.getEnterpriseName());
            dto.setEnterpriseContact(practiceOrder.getEnterpriseContact());
            dto.setEnterprisePhone(practiceOrder.getEnterprisePhone());
            dto.setEnterpriseEmail(practiceOrder.getEnterpriseEmail());
            dto.setStudentCount(practiceOrder.getStudentCount());
            dto.setPracticeDuration(practiceOrder.getPracticeDuration());
            dto.setPracticeLocation(practiceOrder.getPracticeLocation());
            dto.setServiceFee(practiceOrder.getServiceFee());
            dto.setManagementFee(practiceOrder.getManagementFee());
            dto.setOtherFee(practiceOrder.getOtherFee());
        }
        return dto;
    }

    /**
     * 转换保存请求DTO为保存请求VO
     *
     * @param reqDTO 保存请求DTO
     * @return 保存请求VO
     */
    UniversityPracticeOrderSaveReqVO convert(UniversityPracticeOrderSaveReqDTO reqDTO);



}




