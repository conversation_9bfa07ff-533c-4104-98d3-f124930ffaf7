package cn.bztmaster.cnt.module.publicbiz.service.aunt;

import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.*;

/**
 * 阿姨排班管理 Service 接口
 *
 * <AUTHOR>
 */
public interface AuntScheduleService {

    /**
     * 获得阿姨排班信息
     *
     * @param reqVO 查询条件
     * @return 排班信息
     */
    AuntScheduleInfoRespVO getScheduleInfo(AuntScheduleInfoReqVO reqVO);

    /**
     * 更新排班状态
     *
     * @param reqVO 更新信息
     * @return 更新结果
     */
    Boolean updateScheduleStatus(AuntScheduleUpdateReqVO reqVO);
}
