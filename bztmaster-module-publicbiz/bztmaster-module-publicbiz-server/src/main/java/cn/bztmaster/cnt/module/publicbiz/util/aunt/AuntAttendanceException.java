package cn.bztmaster.cnt.module.publicbiz.util.aunt;

/**
 * 阿姨考勤管理异常类
 *
 * <AUTHOR>
 */
public class AuntAttendanceException extends RuntimeException {

    private final String errorCode;

    public AuntAttendanceException(String message) {
        super(message);
        this.errorCode = "AUNT_ATTENDANCE_ERROR";
    }

    public AuntAttendanceException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public AuntAttendanceException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "AUNT_ATTENDANCE_ERROR";
    }

    public AuntAttendanceException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 参数错误异常
     */
    public static class ParameterException extends AuntAttendanceException {
        public ParameterException(String message) {
            super("1001", message);
        }
    }

    /**
     * 阿姨信息不存在异常
     */
    public static class AuntNotFoundException extends AuntAttendanceException {
        public AuntNotFoundException(String message) {
            super("1002", message);
        }
    }

    /**
     * 申请时间冲突异常
     */
    public static class TimeConflictException extends AuntAttendanceException {
        public TimeConflictException(String message) {
            super("1003", message);
        }
    }

    /**
     * 申请单号已存在异常
     */
    public static class ApplyIdExistsException extends AuntAttendanceException {
        public ApplyIdExistsException(String message) {
            super("1004", message);
        }
    }

    /**
     * 无权限操作异常
     */
    public static class NoPermissionException extends AuntAttendanceException {
        public NoPermissionException(String message) {
            super("1005", message);
        }
    }

    /**
     * 申请状态不允许修改异常
     */
    public static class StatusNotModifiableException extends AuntAttendanceException {
        public StatusNotModifiableException(String message) {
            super("1006", message);
        }
    }

    /**
     * 排班信息不存在异常
     */
    public static class ScheduleNotFoundException extends AuntAttendanceException {
        public ScheduleNotFoundException(String message) {
            super("1007", message);
        }
    }

    /**
     * 工作日配置错误异常
     */
    public static class WorkdayConfigException extends AuntAttendanceException {
        public WorkdayConfigException(String message) {
            super("1008", message);
        }
    }
}
