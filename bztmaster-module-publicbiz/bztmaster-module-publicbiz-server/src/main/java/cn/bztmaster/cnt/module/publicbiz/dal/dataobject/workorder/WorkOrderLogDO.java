package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 工单处理日志 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_work_order_log")
@KeySequence("publicbiz_work_order_log_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkOrderLogDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 工单ID
     */
    private Long workOrderId;

    /**
     * 工单编号
     */
    private String workOrderNo;

    /**
     * 操作类型：create-创建/update-更新/assign-分配/process-处理/resolve-解决/close-关闭
     */
    private String operationType;

    /**
     * 操作描述
     */
    private String operationDescription;

    /**
     * 操作前状态
     */
    private String beforeStatus;

    /**
     * 操作后状态
     */
    private String afterStatus;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作人角色
     */
    private String operatorRole;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 操作备注
     */
    private String operationRemark;

    /**
     * 附件数量
     */
    private Integer attachmentCount;

    /**
     * 附件URL列表，用逗号分隔
     */
    private String attachmentUrls;
}
