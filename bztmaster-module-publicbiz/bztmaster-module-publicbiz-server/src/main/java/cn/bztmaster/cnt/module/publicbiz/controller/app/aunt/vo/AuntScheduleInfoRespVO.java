package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "用户 APP - 阿姨排班信息查询 Response VO")
@Data
public class AuntScheduleInfoRespVO {

    @Schema(description = "阿姨信息")
    private AuntInfo auntInfo;

    @Schema(description = "排班列表")
    private List<ScheduleInfo> scheduleList;

    @Schema(description = "阿姨信息")
    @Data
    public static class AuntInfo {
        @Schema(description = "阿姨OneID", example = "aunt_001")
        private String oneId;

        @Schema(description = "阿姨姓名", example = "张阿姨")
        private String name;

        @Schema(description = "阿姨电话", example = "13800138000")
        private String phone;
    }

    @Schema(description = "排班信息")
    @Data
    public static class ScheduleInfo {
        @Schema(description = "排班日期", example = "2025-07-01")
        private String date;

        @Schema(description = "星期几", example = "星期二")
        private String dayOfWeek;

        @Schema(description = "是否工作日", example = "true")
        private Boolean isWorkday;

        @Schema(description = "开始时间", example = "09:00")
        private String startTime;

        @Schema(description = "结束时间", example = "18:00")
        private String endTime;

        @Schema(description = "工作时长", example = "8")
        private BigDecimal workHours;

        @Schema(description = "排班状态", example = "CONFIRMED", allowableValues = {"CONFIRMED", "PENDING", "CANCELLED"})
        private String status;

        @Schema(description = "客户信息")
        private ClientInfo clientInfo;
    }

    @Schema(description = "客户信息")
    @Data
    public static class ClientInfo {
        @Schema(description = "客户ID", example = "client_001")
        private String clientId;

        @Schema(description = "客户姓名", example = "李女士")
        private String clientName;

        @Schema(description = "服务地址", example = "北京市朝阳区xxx小区")
        private String address;
    }
}
