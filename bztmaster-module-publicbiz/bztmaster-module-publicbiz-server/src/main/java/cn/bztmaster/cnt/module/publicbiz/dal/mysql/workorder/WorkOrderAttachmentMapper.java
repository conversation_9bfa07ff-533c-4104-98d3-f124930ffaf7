package cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderAttachmentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工单附件 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WorkOrderAttachmentMapper extends BaseMapperX<WorkOrderAttachmentDO> {

    /**
     * 根据工单ID查询附件列表
     *
     * @param workOrderId 工单ID
     * @return 附件列表
     */
    List<WorkOrderAttachmentDO> selectByWorkOrderId(@Param("workOrderId") Long workOrderId);

    /**
     * 根据工单编号查询附件列表
     *
     * @param workOrderNo 工单编号
     * @return 附件列表
     */
    List<WorkOrderAttachmentDO> selectByWorkOrderNo(@Param("workOrderNo") String workOrderNo);

    /**
     * 根据工单ID删除附件
     *
     * @param workOrderId 工单ID
     * @return 删除数量
     */
    int deleteByWorkOrderId(@Param("workOrderId") Long workOrderId);
}
