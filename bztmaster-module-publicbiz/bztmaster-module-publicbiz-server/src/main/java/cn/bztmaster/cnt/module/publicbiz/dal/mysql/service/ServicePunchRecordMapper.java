package cn.bztmaster.cnt.module.publicbiz.dal.mysql.service;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.service.ServicePunchRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 服务打卡记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ServicePunchRecordMapper extends BaseMapperX<ServicePunchRecordDO> {

    /**
     * 根据阿姨OneID和日期范围查询打卡记录
     *
     * @param auntOneId 阿姨OneID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 打卡记录列表
     */
    List<ServicePunchRecordDO> selectByAuntOneIdAndDateRange(@Param("auntOneId") String auntOneId,
                                                             @Param("startDate") LocalDate startDate,
                                                             @Param("endDate") LocalDate endDate);

    /**
     * 根据阿姨OneID查询指定日期的打卡记录
     *
     * @param auntOneId 阿姨OneID
     * @param scheduleDate 排班日期
     * @return 打卡记录列表
     */
    List<ServicePunchRecordDO> selectByAuntOneIdAndScheduleDate(@Param("auntOneId") String auntOneId,
                                                                @Param("scheduleDate") LocalDate scheduleDate);

    /**
     * 根据阿姨OneID和打卡类型查询打卡记录
     *
     * @param auntOneId 阿姨OneID
     * @param punchType 打卡类型
     * @return 打卡记录列表
     */
    List<ServicePunchRecordDO> selectByAuntOneIdAndPunchType(@Param("auntOneId") String auntOneId,
                                                             @Param("punchType") Integer punchType);

    /**
     * 统计阿姨指定日期范围内的出勤天数
     *
     * @param auntOneId 阿姨OneID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 出勤天数
     */
    Integer countAttendanceDays(@Param("auntOneId") String auntOneId,
                               @Param("startDate") LocalDate startDate,
                               @Param("endDate") LocalDate endDate);
}
