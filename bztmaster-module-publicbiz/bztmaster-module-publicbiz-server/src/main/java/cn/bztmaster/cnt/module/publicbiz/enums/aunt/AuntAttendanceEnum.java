package cn.bztmaster.cnt.module.publicbiz.enums.aunt;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 阿姨考勤管理相关枚举
 *
 * <AUTHOR>
 */
public class AuntAttendanceEnum {

    /**
     * 申请类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum ApplyType {
        LEAVE("LEAVE", "请假"),
        ADJUST("ADJUST", "调休");

        private final String code;
        private final String name;

        public static String getNameByCode(String code) {
            for (ApplyType type : values()) {
                if (type.getCode().equals(code)) {
                    return type.getName();
                }
            }
            return "未知类型";
        }
    }

    /**
     * 申请状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum ApplyStatus {
        PENDING("PENDING", "审批中"),
        APPROVED("APPROVED", "已批准"),
        REJECTED("REJECTED", "已驳回");

        private final String code;
        private final String name;

        public static String getNameByCode(String code) {
            for (ApplyStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status.getName();
                }
            }
            return "未知状态";
        }
    }

    /**
     * 审批操作枚举
     */
    @Getter
    @AllArgsConstructor
    public enum ApproveAction {
        APPROVE("APPROVE", "批准"),
        REJECT("REJECT", "驳回");

        private final String code;
        private final String name;
    }

    /**
     * 排班状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum ScheduleStatus {
        CONFIRMED("CONFIRMED", "已确认"),
        PENDING("PENDING", "待确认"),
        CANCELLED("CANCELLED", "已取消");

        private final String code;
        private final String name;

        public static String getNameByCode(String code) {
            for (ScheduleStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status.getName();
                }
            }
            return "未知状态";
        }
    }

    /**
     * 排班操作枚举
     */
    @Getter
    @AllArgsConstructor
    public enum ScheduleAction {
        CONFIRM("CONFIRM", "确认"),
        CANCEL("CANCEL", "取消");

        private final String code;
        private final String name;
    }
}
