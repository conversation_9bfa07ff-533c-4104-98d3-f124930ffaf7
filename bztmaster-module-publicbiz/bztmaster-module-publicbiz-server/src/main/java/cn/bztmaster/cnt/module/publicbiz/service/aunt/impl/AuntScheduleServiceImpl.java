package cn.bztmaster.cnt.module.publicbiz.service.aunt.impl;

import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic.DomesticTaskMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.service.ServicePunchRecordMapper;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class AuntScheduleServiceImpl implements AuntScheduleService {

    @Resource
    private DomesticTaskMapper domesticTaskMapper;

    @Resource
    private ServicePunchRecordMapper servicePunchRecordMapper;

    @Resource
    private PractitionerMapper practitionerMapper;

    @Override
    public AuntScheduleInfoRespVO getScheduleInfo(AuntScheduleInfoReqVO reqVO) {
        log.info("查询阿姨排班信息，reqVO: {}", reqVO);

        AuntScheduleInfoRespVO respVO = new AuntScheduleInfoRespVO();

        // 根据oneId查询阿姨信息
        PractitionerDO practitioner = practitionerMapper.selectByAuntOneId(reqVO.getOneId());
        if (practitioner == null) {
            throw new RuntimeException("未找到阿姨信息或阿姨已离职: " + reqVO.getOneId());
        }

        // 设置阿姨信息
        AuntScheduleInfoRespVO.AuntInfo auntInfo = new AuntScheduleInfoRespVO.AuntInfo();
        auntInfo.setOneId(practitioner.getAuntOneid());
        auntInfo.setName(practitioner.getName());
        auntInfo.setPhone(practitioner.getPhone());
        respVO.setAuntInfo(auntInfo);

        // 查询排班任务
        LocalDate startDate = reqVO.getStartDate() != null ? LocalDate.parse(reqVO.getStartDate()) : LocalDate.now();
        LocalDate endDate = reqVO.getEndDate() != null ? LocalDate.parse(reqVO.getEndDate()) : startDate.plusDays(6);
        
        List<DomesticTaskDO> taskList = domesticTaskMapper.selectByAuntOneIdAndDateRange(
                reqVO.getOneId(), startDate, endDate);

        // 转换为排班信息
        List<AuntScheduleInfoRespVO.ScheduleInfo> scheduleList = new ArrayList<>();
        for (DomesticTaskDO task : taskList) {
            AuntScheduleInfoRespVO.ScheduleInfo scheduleInfo = convertToScheduleInfo(task);
            scheduleList.add(scheduleInfo);
        }

        // 如果没有排班数据，生成默认排班信息
        if (scheduleList.isEmpty()) {
            scheduleList = generateDefaultSchedule(startDate, endDate);
        }

        respVO.setScheduleList(scheduleList);

        log.info("阿姨排班信息查询完成");
        return respVO;
    }

    @Override
    public Boolean updateScheduleStatus(AuntScheduleUpdateReqVO reqVO) {
        log.info("更新阿姨排班状态，reqVO: {}", reqVO);

        // 查询排班任务
        List<DomesticTaskDO> taskList = domesticTaskMapper.selectByAuntOneIdAndScheduleDate(
                reqVO.getOneId(), LocalDate.now());

        if (taskList.isEmpty()) {
            throw new RuntimeException("未找到相关排班信息");
        }

        // 更新排班状态
        for (DomesticTaskDO task : taskList) {
            if ("CONFIRM".equals(reqVO.getAction())) {
                task.setTaskStatus("assigned");
            } else if ("CANCEL".equals(reqVO.getAction())) {
                task.setTaskStatus("cancelled");
            }
            domesticTaskMapper.updateById(task);
        }

        log.info("阿姨排班状态更新完成，scheduleId: {}, action: {}", reqVO.getScheduleId(), reqVO.getAction());
        return Boolean.TRUE;
    }

    /**
     * 转换为排班信息
     */
    private AuntScheduleInfoRespVO.ScheduleInfo convertToScheduleInfo(DomesticTaskDO task) {
        AuntScheduleInfoRespVO.ScheduleInfo scheduleInfo = new AuntScheduleInfoRespVO.ScheduleInfo();
        scheduleInfo.setDate(task.getScheduleDate().toString());
        scheduleInfo.setDayOfWeek(getDayOfWeek(task.getScheduleDate().getDayOfWeek().getValue()));
        scheduleInfo.setIsWorkday(task.getScheduleDate().getDayOfWeek().getValue() <= 5);
        scheduleInfo.setStartTime(task.getPlannedStartTime() != null ? 
                task.getPlannedStartTime().toLocalTime().toString() : "09:00");
        scheduleInfo.setEndTime(task.getPlannedEndTime() != null ? 
                task.getPlannedEndTime().toLocalTime().toString() : "18:00");
        scheduleInfo.setWorkHours(calculateWorkHours(task.getPlannedStartTime(), task.getPlannedEndTime()));
        scheduleInfo.setStatus(convertTaskStatus(task.getTaskStatus()));

        // 设置客户信息
        AuntScheduleInfoRespVO.ClientInfo clientInfo = new AuntScheduleInfoRespVO.ClientInfo();
        clientInfo.setClientId(task.getCustomerId() != null ? task.getCustomerId().toString() : "client_001");
        clientInfo.setClientName(task.getCustomerName() != null ? task.getCustomerName() : "客户");
        clientInfo.setAddress(task.getServiceAddress() != null ? task.getServiceAddress() : "北京市朝阳区xxx小区");
        scheduleInfo.setClientInfo(clientInfo);

        return scheduleInfo;
    }

    /**
     * 生成默认排班信息
     */
    private List<AuntScheduleInfoRespVO.ScheduleInfo> generateDefaultSchedule(LocalDate startDate, LocalDate endDate) {
        List<AuntScheduleInfoRespVO.ScheduleInfo> scheduleList = new ArrayList<>();
        
        LocalDate current = startDate;
        while (!current.isAfter(endDate)) {
            AuntScheduleInfoRespVO.ScheduleInfo scheduleInfo = new AuntScheduleInfoRespVO.ScheduleInfo();
            scheduleInfo.setDate(current.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            scheduleInfo.setDayOfWeek(getDayOfWeek(current.getDayOfWeek().getValue()));
            scheduleInfo.setIsWorkday(current.getDayOfWeek().getValue() <= 5);
            scheduleInfo.setStartTime("09:00");
            scheduleInfo.setEndTime("18:00");
            scheduleInfo.setWorkHours(new BigDecimal("8"));
            scheduleInfo.setStatus("PENDING");
            
            // 设置客户信息
            AuntScheduleInfoRespVO.ClientInfo clientInfo = new AuntScheduleInfoRespVO.ClientInfo();
            clientInfo.setClientId("client_" + current.getDayOfYear());
            clientInfo.setClientName("客户" + current.getDayOfYear());
            clientInfo.setAddress("北京市朝阳区xxx小区" + current.getDayOfYear() + "号");
            scheduleInfo.setClientInfo(clientInfo);
            
            scheduleList.add(scheduleInfo);
            current = current.plusDays(1);
        }
        
        return scheduleList;
    }

    /**
     * 计算工作时长
     */
    private BigDecimal calculateWorkHours(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return new BigDecimal("8");
        }
        
        long hours = java.time.Duration.between(startTime, endTime).toHours();
        return BigDecimal.valueOf(hours);
    }

    /**
     * 转换任务状态
     */
    private String convertTaskStatus(String taskStatus) {
        switch (taskStatus) {
            case "assigned": return "CONFIRMED";
            case "pending": return "PENDING";
            case "cancelled": return "CANCELLED";
            default: return "PENDING";
        }
    }

    /**
     * 获取星期几的中文描述
     */
    private String getDayOfWeek(int dayOfWeek) {
        switch (dayOfWeek) {
            case 1: return "星期一";
            case 2: return "星期二";
            case 3: return "星期三";
            case 4: return "星期四";
            case 5: return "星期五";
            case 6: return "星期六";
            case 7: return "星期日";
            default: return "未知";
        }
    }
}
