package cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 家政服务任务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DomesticTaskMapper extends BaseMapperX<DomesticTaskDO> {

    /**
     * 根据阿姨OneID和日期范围查询排班任务
     *
     * @param auntOneId 阿姨OneID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 排班任务列表
     */
    List<DomesticTaskDO> selectByAuntOneIdAndDateRange(@Param("auntOneId") String auntOneId,
                                                       @Param("startDate") LocalDate startDate,
                                                       @Param("endDate") LocalDate endDate);

    /**
     * 根据阿姨OneID查询指定日期的排班任务
     *
     * @param auntOneId 阿姨OneID
     * @param scheduleDate 排班日期
     * @return 排班任务列表
     */
    List<DomesticTaskDO> selectByAuntOneIdAndScheduleDate(@Param("auntOneId") String auntOneId,
                                                          @Param("scheduleDate") LocalDate scheduleDate);

    /**
     * 根据阿姨OneID查询进行中的任务
     *
     * @param auntOneId 阿姨OneID
     * @return 进行中的任务列表
     */
    List<DomesticTaskDO> selectInProgressByAuntOneId(@Param("auntOneId") String auntOneId);

    /**
     * 查询阿姨今日排班数
     *
     * @param auntOneId 阿姨OneID
     * @param scheduleDate 排班日期
     * @return 今日排班任务数量
     */
    Integer selectTodayScheduleCount(@Param("auntOneId") String auntOneId, 
                                    @Param("scheduleDate") LocalDate scheduleDate);

    /**
     * 查询阿姨本月服务时长
     *
     * @param auntOneId 阿姨OneID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 本月服务时长（小时）
     */
    Integer selectMonthlyServiceHours(@Param("auntOneId") String auntOneId,
                                     @Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime);
} 