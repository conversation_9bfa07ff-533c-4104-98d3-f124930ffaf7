package cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 任务工单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WorkOrderMapper extends BaseMapperX<WorkOrderDO> {

    /**
     * 根据阿姨OneID查询工单列表
     *
     * @param auntOneId 阿姨OneID
     * @return 工单列表
     */
    List<WorkOrderDO> selectByAuntOneId(@Param("auntOneId") String auntOneId);

    /**
     * 根据阿姨OneID和工单类型查询工单列表
     *
     * @param auntOneId 阿姨OneID
     * @param workOrderType 工单类型
     * @return 工单列表
     */
    List<WorkOrderDO> selectByAuntOneIdAndType(@Param("auntOneId") String auntOneId,
                                               @Param("workOrderType") String workOrderType);

    /**
     * 根据阿姨OneID、工单类型和状态查询工单列表
     *
     * @param auntOneId 阿姨OneID
     * @param workOrderType 工单类型
     * @param status 状态
     * @return 工单列表
     */
    List<WorkOrderDO> selectByAuntOneIdAndTypeAndStatus(@Param("auntOneId") String auntOneId,
                                                        @Param("workOrderType") String workOrderType,
                                                        @Param("status") String status);

    /**
     * 根据阿姨OneID、工单类型、状态和年月查询工单列表
     *
     * @param auntOneId 阿姨OneID
     * @param workOrderType 工单类型
     * @param status 状态
     * @param year 年份
     * @param month 月份
     * @return 工单列表
     */
    List<WorkOrderDO> selectByAuntOneIdAndTypeAndStatus(@Param("auntOneId") String auntOneId,
                                                        @Param("workOrderType") String workOrderType,
                                                        @Param("status") String status,
                                                        @Param("year") Integer year,
                                                        @Param("month") Integer month);

    /**
     * 根据工单编号查询工单
     *
     * @param workOrderNo 工单编号
     * @return 工单信息
     */
    WorkOrderDO selectByWorkOrderNo(@Param("workOrderNo") String workOrderNo);

    /**
     * 统计阿姨指定年月范围内的请假天数
     *
     * @param auntOneId 阿姨OneID
     * @param year 年份
     * @param month 月份
     * @return 请假天数
     */
    Integer countLeaveDays(@Param("auntOneId") String auntOneId,
                           @Param("year") Integer year,
                           @Param("month") Integer month);

    /**
     * 统计阿姨指定年月范围内的调休天数
     *
     * @param auntOneId 阿姨OneID
     * @param year 年份
     * @param month 月份
     * @return 调休天数
     */
    Integer countAdjustDays(@Param("auntOneId") String auntOneId,
                            @Param("year") Integer year,
                            @Param("month") Integer month);
}
