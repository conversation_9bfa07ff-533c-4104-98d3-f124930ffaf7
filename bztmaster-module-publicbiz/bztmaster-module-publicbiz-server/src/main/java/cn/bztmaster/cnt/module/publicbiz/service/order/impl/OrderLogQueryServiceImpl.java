package cn.bztmaster.cnt.module.publicbiz.service.order.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderLogPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderLogPageRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderLogDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderLogMapper;
import cn.bztmaster.cnt.module.publicbiz.service.order.OrderLogQueryService;
import cn.bztmaster.cnt.module.publicbiz.convert.order.OrderLogConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单日志查询服务实现类
 * 支持查询所有类型订单的操作日志
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderLogQueryServiceImpl implements OrderLogQueryService {

    @Resource
    private PublicbizOrderLogMapper orderLogMapper;

    @Override
    public PageResult<UniversityPracticeOrderLogPageRespVO> getOrderLogPage(UniversityPracticeOrderLogPageReqVO reqVO) {
        // 使用现有的Mapper方法进行分页查询
        PageResult<PublicbizOrderLogDO> pageResult = orderLogMapper.selectOrderLogPage(reqVO);
        
        // 转换为VO
        List<UniversityPracticeOrderLogPageRespVO> voList = OrderLogConvert.INSTANCE.convertList(pageResult.getList());
        
        return new PageResult<>(voList, pageResult.getTotal());
    }

    @Override
    public List<UniversityPracticeOrderLogPageRespVO> getOrderLogListByOrderNo(String orderNo) {
        List<PublicbizOrderLogDO> logList = orderLogMapper.selectListByOrderNo(orderNo);
        return OrderLogConvert.INSTANCE.convertList(logList);
    }

    @Override
    public List<UniversityPracticeOrderLogPageRespVO> getOrderLogListByOrderNoAndType(String orderNo, String logType) {
        List<PublicbizOrderLogDO> logList = orderLogMapper.selectListByLogType(logType);
        // 过滤指定订单号的日志
        logList = logList.stream()
                .filter(log -> orderNo.equals(log.getOrderNo()))
                .collect(Collectors.toList());
        return OrderLogConvert.INSTANCE.convertList(logList);
    }

    @Override
    public List<UniversityPracticeOrderLogPageRespVO> getOrderLogListByOrderType(String orderType, Integer limit) {
        // 这里需要在Mapper中添加按订单类型查询的方法
        // 暂时使用现有方法，后续可以优化
        List<PublicbizOrderLogDO> logList = orderLogMapper.selectList();
        logList = logList.stream()
                .filter(log -> orderType.equals(log.getRelatedPartyType()))
                .limit(limit != null ? limit : 100)
                .collect(Collectors.toList());
        return OrderLogConvert.INSTANCE.convertList(logList);
    }

    @Override
    public List<UniversityPracticeOrderLogPageRespVO> getOrderLogListByOperator(Long operatorId, Integer limit) {
        // 这里需要在Mapper中添加按操作人查询的方法
        // 暂时使用现有方法，后续可以优化
        List<PublicbizOrderLogDO> logList = orderLogMapper.selectList();
        logList = logList.stream()
                .filter(log -> operatorId.equals(log.getOperatorId()))
                .limit(limit != null ? limit : 100)
                .collect(Collectors.toList());
        return OrderLogConvert.INSTANCE.convertList(logList);
    }
}

