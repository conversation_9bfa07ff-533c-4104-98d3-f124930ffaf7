package cn.bztmaster.cnt.module.publicbiz.service.order;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderDropdownDataReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderDropdownDataRespVO;

/**
 * 订单下拉数据服务接口
 *
 * <AUTHOR>
 */
public interface OrderDropdownDataService {

    /**
     * 获取订单下拉数据
     *
     * @param reqVO 请求参数
     * @return 下拉数据
     */
    OrderDropdownDataRespVO getDropdownData(OrderDropdownDataReqVO reqVO);
}





