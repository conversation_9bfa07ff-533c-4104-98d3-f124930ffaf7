package cn.bztmaster.cnt.module.publicbiz.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 订单日志内容构建器
 */
public class OrderLogContentBuilder {

    public static String buildGenericCreateLogContent(String orderType, Map<String, Object> details) {
        CreateDetails createDetails = CreateDetails.builder()
                .projectName((String) details.get("projectName"))
                .universityName((String) details.get("universityName"))
                .enterpriseName((String) details.get("enterpriseName"))
                .totalAmount((String) details.get("totalAmount"))
                .build();
        
        CreateLogContent content = CreateLogContent.builder()
                .action("创建" + getOrderTypeDisplayName(orderType) + "订单")
                .details(createDetails)
                .description(String.format("创建了新的%s订单", getOrderTypeDisplayName(orderType)))
                .build();
        
        return JSONUtil.toJsonStr(content);
    }

    public static String buildEditLogContent(List<FieldChange> changes, String description) {
        EditLogContent content = EditLogContent.builder()
                .action("更新订单")
                .changes(changes)
                .description(description)
                .build();
        
        return JSONUtil.toJsonStr(content);
    }

    public static String buildGenericEditLogContent(String orderType, List<FieldChange> changes, String description) {
        EditLogContent content = EditLogContent.builder()
                .action("更新" + getOrderTypeDisplayName(orderType) + "订单")
                .changes(changes)
                .description(description)
                .build();
        
        return JSONUtil.toJsonStr(content);
    }

    public static String buildGenericDeleteLogContent(String orderType, String orderNo, String projectName) {
        DeleteLogContent content = DeleteLogContent.builder()
                .action("删除" + getOrderTypeDisplayName(orderType) + "订单")
                .details(DeleteDetails.builder()
                        .orderNo(orderNo)
                        .projectName(projectName)
                        .build())
                .description(String.format("删除了%s订单【%s】，项目为【%s】", 
                        getOrderTypeDisplayName(orderType), orderNo, projectName))
                .build();
        
        return JSONUtil.toJsonStr(content);
    }

    /**
     * 创建订单日志内容
     */
    public static String buildCreateLogContent(String projectName, String universityName, 
                                            String enterpriseName, Object totalAmount) {
        CreateLogContent content = CreateLogContent.builder()
                .action("创建订单")
                .details(CreateDetails.builder()
                        .projectName(projectName)
                        .universityName(universityName)
                        .enterpriseName(enterpriseName)
                        .totalAmount(totalAmount != null ? totalAmount.toString() : "0")
                        .build())
                .description(String.format("创建了新的订单，项目：%s，高校：%s，企业：%s", 
                        projectName, universityName, enterpriseName))
                .build();
        
        return JSONUtil.toJsonStr(content);
    }

    /**
     * 删除订单日志内容
     */
    public static String buildDeleteLogContent(String orderNo, String projectName) {
        DeleteLogContent content = DeleteLogContent.builder()
                .action("删除订单")
                .details(DeleteDetails.builder()
                        .orderNo(orderNo)
                        .projectName(projectName)
                        .build())
                .description(String.format("删除了订单【%s】，项目为【%s】", orderNo, projectName))
                .build();
        
        return JSONUtil.toJsonStr(content);
    }

    /**
     * 审批通过日志内容
     */
    public static String buildApprovalLogContent(String oldStatus, String newStatus, String description) {
        ApprovalLogContent content = ApprovalLogContent.builder()
                .action("审批通过")
                .statusChange(StatusChange.builder()
                        .field("订单状态")
                        .oldValue(oldStatus)
                        .newValue(newStatus)
                        .build())
                .description(description)
                .build();
        
        return JSONUtil.toJsonStr(content);
    }

    /**
     * 确认收款日志内容
     */
    public static String buildPaymentConfirmLogContent(String oldPaymentStatus, String newPaymentStatus,
                                                     String oldOrderStatus, String newOrderStatus, String description) {
        PaymentLogContent content = PaymentLogContent.builder()
                .action("确认收款")
                .changes(Arrays.asList(
                        FieldChange.builder()
                                .field("支付状态")
                                .oldValue(oldPaymentStatus)
                                .newValue(newPaymentStatus)
                                .build(),
                        FieldChange.builder()
                                .field("订单状态")
                                .oldValue(oldOrderStatus)
                                .newValue(newOrderStatus)
                                .build()
                ))
                .description(description)
                .build();
        
        return JSONUtil.toJsonStr(content);
    }

    private static String getOrderTypeDisplayName(String orderType) {
        switch (orderType) {
            case "practice":
                return "高校实践";
            case "training":
                return "企业培训";
            case "consulting":
                return "咨询服务";
            case "recruitment":
                return "招聘服务";
            case "outsourcing":
                return "外包服务";
            case "maintenance":
                return "维护服务";
            default:
                return orderType;
        }
    }

    @Data
    @Builder
    public static class CreateLogContent {
        private String action;
        private CreateDetails details;
        private String description;
    }

    @Data
    @Builder
    public static class CreateDetails {
        private String projectName;
        private String universityName;
        private String enterpriseName;
        private String totalAmount;
    }

    @Data
    @Builder
    public static class EditLogContent {
        private String action;
        private List<FieldChange> changes;
        private String description;
    }

    @Data
    @Builder
    public static class DeleteLogContent {
        private String action;
        private DeleteDetails details;
        private String description;
    }

    @Data
    @Builder
    public static class DeleteDetails {
        private String orderNo;
        private String projectName;
    }

    @Data
    @Builder
    public static class FieldChange {
        private String field;
        private String oldValue;
        private String newValue;
    }

    @Data
    @Builder
    public static class ApprovalLogContent {
        private String action;
        private StatusChange statusChange;
        private String description;
    }

    @Data
    @Builder
    public static class StatusChange {
        private String field;
        private String oldValue;
        private String newValue;
    }

    @Data
    @Builder
    public static class PaymentLogContent {
        private String action;
        private List<FieldChange> changes;
        private String description;
    }
}
