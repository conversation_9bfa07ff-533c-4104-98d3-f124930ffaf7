package cn.bztmaster.cnt.module.publicbiz.service.domestic;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.DomesticTaskGenerateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.DomesticTaskGenerateRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 家政服务任务 Service 接口
 *
 * <AUTHOR>
 */
public interface DomesticTaskService {

    /**
     * 根据家政订单生成任务
     *
     * @param reqVO 任务生成请求
     * @return 任务生成结果
     */
    DomesticTaskGenerateRespVO generateTasks(@Valid DomesticTaskGenerateReqVO reqVO);

    /**
     * 根据订单ID查询任务列表
     *
     * @param orderId 订单ID
     * @return 任务列表
     */
    List<DomesticTaskDO> getTasksByOrderId(Long orderId);

    /**
     * 根据家政订单ID查询任务列表
     *
     * @param domesticOrderId 家政订单ID
     * @return 任务列表
     */
    List<DomesticTaskDO> getTasksByDomesticOrderId(Long domesticOrderId);

    /**
     * 检查订单是否已生成任务
     *
     * @param orderId 订单ID
     * @return 是否已生成任务
     */
    boolean hasTasksGenerated(Long orderId);

    /**
     * 删除订单相关的所有任务
     *
     * @param orderId 订单ID
     */
    void deleteTasksByOrderId(Long orderId);

    /**
     * 生成任务编号
     *
     * @return 任务编号
     */
    String generateTaskNo();

    /**
     * 批量生成家政服务任务
     * 查询符合条件的订单并批量生成任务
     *
     * @return 批量处理结果
     */
    Map<String, Object> batchGenerateTasks();
}
