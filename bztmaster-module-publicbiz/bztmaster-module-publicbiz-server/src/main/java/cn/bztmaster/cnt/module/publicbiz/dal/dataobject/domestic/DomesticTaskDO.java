package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 家政服务任务 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_domestic_task")
@KeySequence("publicbiz_domestic_task_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DomesticTaskDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 家政服务订单ID
     */
    private Long domesticOrderId;

    /**
     * 任务编号
     */
    private String taskNo;

    /**
     * 任务序号
     */
    private Integer taskSequence;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务描述
     */
    private String taskDescription;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务状态：pending-待分配/assigned-已分配/in_progress-进行中/completed-已完成/cancelled-已取消
     */
    private String taskStatus;

    /**
     * 计划开始时间
     */
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime plannedEndTime;

    /**
     * 实际开始时间
     */
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    private LocalDateTime actualEndTime;

    /**
     * 任务时长
     */
    private String duration;

    /**
     * 服务人员OneID
     */
    private String practitionerOneid;

    /**
     * 服务人员姓名
     */
    private String practitionerName;

    /**
     * 服务人员电话
     */
    private String practitionerPhone;

    /**
     * 排班日期
     */
    private LocalDate scheduleDate;

    /**
     * 服务分类ID
     */
    private Long serviceCategoryId;

    /**
     * 服务分类名称
     */
    private String serviceCategoryName;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 客户电话
     */
    private String customerPhone;

    /**
     * 服务地址
     */
    private String serviceAddress;

    /**
     * 打卡开始时间
     */
    private LocalDateTime punchInTime;

    /**
     * 打卡结束时间
     */
    private LocalDateTime punchOutTime;

    /**
     * 打卡位置
     */
    private String punchLocation;

    /**
     * 打卡纬度
     */
    private BigDecimal punchLatitude;

    /**
     * 打卡经度
     */
    private BigDecimal punchLongitude;
} 