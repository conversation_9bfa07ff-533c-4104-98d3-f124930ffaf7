package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 订单下拉数据请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "订单下拉数据请求")
@Data
public class OrderDropdownDataReqVO {

    @Schema(description = "订单类型", example = "practice", required = true, 
            allowableValues = {"practice", "training", "domestic", "certification"})
    @NotBlank(message = "订单类型不能为空")
    private String orderType;

    @Schema(description = "业务线", example = "高校实践", 
            allowableValues = {"高校实践", "企业培训", "个人培训与认证", "家政服务"})
    private String businessLine;
}





