package cn.bztmaster.cnt.module.publicbiz.util.aunt;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * 阿姨考勤管理工具类
 *
 * <AUTHOR>
 */
public class AuntAttendanceUtil {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    /**
     * 计算两个日期之间的工作日数量
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 工作日数量
     */
    public static int calculateWorkDays(LocalDate startDate, LocalDate endDate) {
        int workDays = 0;
        LocalDate current = startDate;
        
        while (!current.isAfter(endDate)) {
            // 周一到周五为工作日
            if (current.getDayOfWeek().getValue() <= 5) {
                workDays++;
            }
            current = current.plusDays(1);
        }
        
        return workDays;
    }

    /**
     * 计算指定年月的工作日数量
     *
     * @param year 年份
     * @param month 月份
     * @return 工作日数量
     */
    public static int calculateWorkDays(int year, int month) {
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.plusMonths(1).minusDays(1);
        return calculateWorkDays(startDate, endDate);
    }

    /**
     * 计算两个时间之间的时长（小时）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 时长（小时）
     */
    public static double calculateDurationHours(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime.isAfter(endTime)) {
            return 0.0;
        }
        
        long minutes = ChronoUnit.MINUTES.between(startTime, endTime);
        return minutes / 60.0;
    }

    /**
     * 计算两个时间之间的时长（天）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 时长（天）
     */
    public static double calculateDurationDays(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime.isAfter(endTime)) {
            return 0.0;
        }
        
        long days = ChronoUnit.DAYS.between(startTime, endTime);
        long hours = ChronoUnit.HOURS.between(startTime, endTime) % 24;
        
        return days + (hours / 24.0);
    }

    /**
     * 格式化日期为字符串
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatDate(LocalDate date) {
        return date != null ? date.format(DATE_FORMATTER) : "";
    }

    /**
     * 格式化时间为字符串
     *
     * @param time 时间
     * @return 格式化后的时间字符串
     */
    public static String formatTime(LocalDateTime time) {
        return time != null ? time.format(TIME_FORMATTER) : "";
    }

    /**
     * 格式化日期时间为字符串
     *
     * @param dateTime 日期时间
     * @return 格式化后的日期时间字符串
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATETIME_FORMATTER) : "";
    }

    /**
     * 解析日期字符串
     *
     * @param dateStr 日期字符串
     * @return 日期对象
     */
    public static LocalDate parseDate(String dateStr) {
        return LocalDate.parse(dateStr, DATE_FORMATTER);
    }

    /**
     * 解析时间字符串
     *
     * @param timeStr 时间字符串
     * @return 时间对象
     */
    public static LocalDateTime parseTime(String timeStr) {
        return LocalDateTime.parse(timeStr, TIME_FORMATTER);
    }

    /**
     * 解析日期时间字符串
     *
     * @param dateTimeStr 日期时间字符串
     * @return 日期时间对象
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        return LocalDateTime.parse(dateTimeStr, DATETIME_FORMATTER);
    }

    /**
     * 获取星期几的中文描述
     *
     * @param dayOfWeek 星期几（1-7）
     * @return 中文描述
     */
    public static String getDayOfWeekText(int dayOfWeek) {
        switch (dayOfWeek) {
            case 1: return "星期一";
            case 2: return "星期二";
            case 3: return "星期三";
            case 4: return "星期四";
            case 5: return "星期五";
            case 6: return "星期六";
            case 7: return "星期日";
            default: return "未知";
        }
    }

    /**
     * 验证时间范围是否有效
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否有效
     */
    public static boolean isValidTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return startTime != null && endTime != null && !startTime.isAfter(endTime);
    }

    /**
     * 验证申请时间是否在合理范围内
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param maxDays 最大申请天数
     * @return 是否有效
     */
    public static boolean isValidApplyTimeRange(LocalDateTime startTime, LocalDateTime endTime, int maxDays) {
        if (!isValidTimeRange(startTime, endTime)) {
            return false;
        }
        
        long days = ChronoUnit.DAYS.between(startTime, endTime);
        return days <= maxDays;
    }
}
