package cn.bztmaster.cnt.module.publicbiz.convert.order;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderLogPageRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 订单日志转换器
 * 用于DO和VO之间的转换
 *
 * <AUTHOR>
 */
@Mapper
public interface OrderLogConvert {

    OrderLogConvert INSTANCE = Mappers.getMapper(OrderLogConvert.class);

    /**
     * 转换单个DO到VO
     */
    UniversityPracticeOrderLogPageRespVO convert(PublicbizOrderLogDO bean);

    /**
     * 转换DO列表到VO列表
     */
    List<UniversityPracticeOrderLogPageRespVO> convertList(List<PublicbizOrderLogDO> list);
}


