package cn.bztmaster.cnt.module.publicbiz.dal.mysql.order;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * 订单主表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PublicbizOrderMapper extends BaseMapperX<PublicbizOrderDO> {

    /**
     * 通过订单号查询订单
     *
     * @param orderNo 订单号
     * @return 订单对象
     */
    default PublicbizOrderDO selectByOrderNo(String orderNo) {
        return selectOne(PublicbizOrderDO::getOrderNo, orderNo);
    }

    /**
     * 通过订单ID列表查询订单列表
     *
     * @param ids 订单ID列表
     * @return 订单列表
     */
    default List<PublicbizOrderDO> selectListByIds(Collection<Long> ids) {
        return selectList(PublicbizOrderDO::getId, ids);
    }

    /**
     * 分页查询高校实践订单
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    default PageResult<PublicbizOrderDO> selectPracticeOrderPage(UniversityPracticeOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PublicbizOrderDO>()
                .eq(PublicbizOrderDO::getOrderType, "practice")
                .eq(PublicbizOrderDO::getBusinessLine, "高校实践")
                .likeIfPresent(PublicbizOrderDO::getProjectName, reqVO.getKeyword())
                .likeIfPresent(PublicbizOrderDO::getManagerName, reqVO.getKeyword())
                .eqIfPresent(PublicbizOrderDO::getOrderStatus, reqVO.getOrderStatus())
                .eqIfPresent(PublicbizOrderDO::getPaymentStatus, reqVO.getPaymentStatus())
                .eqIfPresent(PublicbizOrderDO::getManagerId, reqVO.getManagerId())
                .eqIfPresent(PublicbizOrderDO::getOpportunityId, reqVO.getOpportunityId())
                .betweenIfPresent(PublicbizOrderDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PublicbizOrderDO::getId));
    }

    /**
     * 通过负责人ID查询订单列表
     *
     * @param managerId 负责人ID
     * @return 订单列表
     */
    default List<PublicbizOrderDO> selectListByManagerId(Long managerId) {
        return selectList(PublicbizOrderDO::getManagerId, managerId);
    }

    /**
     * 通过订单状态查询订单列表
     *
     * @param orderStatus 订单状态
     * @return 订单列表
     */
    default List<PublicbizOrderDO> selectListByOrderStatus(String orderStatus) {
        return selectList(PublicbizOrderDO::getOrderStatus, orderStatus);
    }

    /**
     * 通过支付状态查询订单列表
     *
     * @param paymentStatus 支付状态
     * @return 订单列表
     */
    default List<PublicbizOrderDO> selectListByPaymentStatus(String paymentStatus) {
        return selectList(PublicbizOrderDO::getPaymentStatus, paymentStatus);
    }

    /**
     * 通过订单号前缀查询当天已生成的订单数量
     *
     * @param orderNoPrefix 订单号前缀（如：HP20250731）
     * @return 订单数量
     */
    default int selectCountByOrderNoPrefix(String orderNoPrefix) {
        Long count = selectCount(new LambdaQueryWrapperX<PublicbizOrderDO>()
                .likeRight(PublicbizOrderDO::getOrderNo, orderNoPrefix));
        return count != null ? count.intValue() : 0;
    }

    /**
     * 根据订单类型查询总金额
     *
     * @param orderType 订单类型
     * @return 总金额
     */
    @Select("SELECT COALESCE(SUM(total_amount), 0) FROM publicbiz_order WHERE order_type = #{orderType} AND deleted = 0")
    BigDecimal selectTotalAmountByOrderType(@Param("orderType") String orderType);

    /**
     * 根据订单类型和日期范围查询月度金额
     *
     * @param orderType 订单类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 月度金额
     */
    @Select("SELECT COALESCE(SUM(total_amount), 0) FROM publicbiz_order WHERE order_type = #{orderType} AND create_time >= #{startDate} AND create_time <= #{endDate} AND deleted = 0")
    BigDecimal selectMonthlyAmountByOrderType(@Param("orderType") String orderType, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 根据状态和阿姨查询订单数量
     *
     * @param practitionerOneId 阿姨OneID
     * @param orderStatus 订单状态
     * @return 订单数量
     */
    @Select("SELECT COUNT(*) FROM publicbiz_order o " +
            "INNER JOIN publicbiz_domestic_order do ON o.id = do.order_id " +
            "WHERE do.practitioner_oneid = #{practitionerOneId} " +
            "  AND o.order_status = #{orderStatus} " +
            "  AND o.deleted = 0 " +
            "  AND do.deleted = 0")
    Integer selectCountByStatusAndPractitioner(@Param("practitionerOneId") String practitionerOneId,
                                             @Param("orderStatus") String orderStatus);

}




