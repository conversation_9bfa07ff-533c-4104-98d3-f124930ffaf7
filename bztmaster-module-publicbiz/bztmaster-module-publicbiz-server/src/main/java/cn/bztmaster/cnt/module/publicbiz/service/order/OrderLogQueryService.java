package cn.bztmaster.cnt.module.publicbiz.service.order;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderLogPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderLogPageRespVO;

import java.util.List;

/**
 * 订单日志查询服务接口
 * 支持查询所有类型订单的操作日志
 *
 * <AUTHOR>
 */
public interface OrderLogQueryService {

    /**
     * 分页查询订单操作日志
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    PageResult<UniversityPracticeOrderLogPageRespVO> getOrderLogPage(UniversityPracticeOrderLogPageReqVO reqVO);

    /**
     * 通过订单号查询日志列表
     *
     * @param orderNo 订单号
     * @return 日志列表
     */
    List<UniversityPracticeOrderLogPageRespVO> getOrderLogListByOrderNo(String orderNo);

    /**
     * 通过订单号和日志类型查询日志列表
     *
     * @param orderNo 订单号
     * @param logType 日志类型
     * @return 日志列表
     */
    List<UniversityPracticeOrderLogPageRespVO> getOrderLogListByOrderNoAndType(String orderNo, String logType);

    /**
     * 通过订单类型查询日志列表
     *
     * @param orderType 订单类型
     * @param limit 限制条数
     * @return 日志列表
     */
    List<UniversityPracticeOrderLogPageRespVO> getOrderLogListByOrderType(String orderType, Integer limit);

    /**
     * 通过操作人查询日志列表
     *
     * @param operatorId 操作人ID
     * @param limit 限制条数
     * @return 日志列表
     */
    List<UniversityPracticeOrderLogPageRespVO> getOrderLogListByOperator(Long operatorId, Integer limit);
}


