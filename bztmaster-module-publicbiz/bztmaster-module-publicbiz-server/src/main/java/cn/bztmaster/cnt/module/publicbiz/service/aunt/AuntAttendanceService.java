package cn.bztmaster.cnt.module.publicbiz.service.aunt;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.*;

/**
 * 阿姨考勤管理 Service 接口
 *
 * <AUTHOR>
 */
public interface AuntAttendanceService {

    /**
     * 获得阿姨月度考勤统计信息
     *
     * @param reqVO 查询条件
     * @return 考勤统计信息
     */
    AuntAttendanceSummaryRespVO getAttendanceSummary(AuntAttendanceSummaryReqVO reqVO);

    /**
     * 提交请假或调休申请
     *
     * @param reqVO 申请信息
     * @return 申请结果
     */
    AuntAttendanceApplyRespVO submitAttendanceApply(AuntAttendanceApplyReqVO reqVO);

    /**
     * 获得阿姨历史请假/调休申请列表
     *
     * @param reqVO 查询条件
     * @return 申请列表
     */
    PageResult<AuntAttendanceHistoryRespVO> getAttendanceHistory(AuntAttendanceHistoryReqVO reqVO);

    /**
     * 更新请假/调休申请状态
     *
     * @param reqVO 审批信息
     * @return 审批结果
     */
    AuntAttendanceApproveRespVO approveAttendanceApply(AuntAttendanceApproveReqVO reqVO);
}
