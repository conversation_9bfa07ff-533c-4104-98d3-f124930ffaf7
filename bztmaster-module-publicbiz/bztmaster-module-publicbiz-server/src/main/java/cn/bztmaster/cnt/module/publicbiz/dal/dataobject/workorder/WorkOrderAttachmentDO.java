package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 工单附件 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_work_order_attachment")
@KeySequence("publicbiz_work_order_attachment_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkOrderAttachmentDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 工单ID
     */
    private Long workOrderId;

    /**
     * 工单编号
     */
    private String workOrderNo;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 附件类型
     */
    private String attachmentType;

    /**
     * 附件大小（字节）
     */
    private Long attachmentSize;

    /**
     * 附件URL
     */
    private String attachmentUrl;

    /**
     * 附件路径
     */
    private String attachmentPath;

    /**
     * 上传人ID
     */
    private Long uploaderId;

    /**
     * 上传人姓名
     */
    private String uploaderName;

    /**
     * 备注
     */
    private String remark;
}
