package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 高校实践订单收款信息 VO
 *
 * <AUTHOR>
 */
@Schema(description = "高校实践订单收款信息")
@Data
public class UniversityPracticeOrderCollectionInfoVO {

    @Schema(description = "收款金额", example = "4200001.00")
    @NotNull(message = "收款金额不能为空")
    @DecimalMin(value = "0.01", message = "收款金额必须大于0")
    private BigDecimal collectionAmount;

    @Schema(description = "收款方式", example = "bank_transfer")
    @NotBlank(message = "收款方式不能为空")
    private String collectionMethod;

    @Schema(description = "收款日期", example = "2024-12-19")
    @NotNull(message = "收款日期不能为空")
    private LocalDate collectionDate;

    @Schema(description = "操作人姓名", example = "李四")
    @NotBlank(message = "操作人姓名不能为空")
    private String operatorName;

    @Schema(description = "收款备注", example = "银行转账收款")
    private String collectionRemark;
}






