### 家政服务任务批量生成接口测试

### 1. 批量生成家政服务任务（无参数）
POST {{baseUrl}}/publicbiz/domestic-task-order/generate-tasks
Content-Type: application/json
Authorization: Bearer {{token}}

### 预期响应格式示例：
# {
#   "code": 0,
#   "data": {
#     "totalOrders": 10,
#     "successCount": 8,
#     "skipCount": 1,
#     "failCount": 1,
#     "successList": [
#       {
#         "orderId": 1024,
#         "orderNo": "JZ20250814001",
#         "taskCount": 30,
#         "packageType": "long-term"
#       },
#       {
#         "orderId": 2048,
#         "orderNo": "JZ20250814002",
#         "taskCount": 4,
#         "packageType": "count-card"
#       }
#     ],
#     "failList": [
#       {
#         "orderId": 3072,
#         "orderNo": "JZ20250814003",
#         "errorMessage": "家政订单详情不存在"
#       }
#     ]
#   },
#   "msg": "操作成功"
# }

### 测试说明：
# 该接口会自动查询符合以下条件的订单：
# - order_type = 'domestic' (家政服务)
# - payment_status = 'paid' (已支付)
# - order_status = 'executing' (执行中)
#
# 对于每个符合条件的订单：
# - 检查是否已生成任务，如已生成则跳过
# - 未生成任务的订单会自动生成任务
# - 返回详细的处理结果统计

### 测试环境变量
# @baseUrl = http://localhost:8080
# @token = your_jwt_token_here
