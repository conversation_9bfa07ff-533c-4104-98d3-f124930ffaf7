### 家政服务任务生成接口测试

### 1. 生成家政服务任务 - 长周期套餐（30天每日服务）
POST {{baseUrl}}/publicbiz/domestic-task-order/generate-tasks
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "orderId": 1024,
  "forceRegenerate": false
}

### 2. 生成家政服务任务 - 强制重新生成
POST {{baseUrl}}/publicbiz/domestic-task-order/generate-tasks
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "orderId": 1024,
  "forceRegenerate": true
}

### 3. 生成家政服务任务 - 次数次卡套餐（4次上门收纳）
POST {{baseUrl}}/publicbiz/domestic-task-order/generate-tasks
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "orderId": 2048,
  "forceRegenerate": false
}

### 4. 根据订单ID查询任务列表
GET {{baseUrl}}/publicbiz/domestic-task-order/tasks/by-order/1024
Authorization: Bearer {{token}}

### 5. 根据家政订单ID查询任务列表
GET {{baseUrl}}/publicbiz/domestic-task-order/tasks/by-domestic-order/2021
Authorization: Bearer {{token}}

### 6. 检查订单是否已生成任务
GET {{baseUrl}}/publicbiz/domestic-task-order/tasks/check-generated/1024
Authorization: Bearer {{token}}

### 7. 删除订单相关的所有任务
DELETE {{baseUrl}}/publicbiz/domestic-task-order/tasks/by-order/1024
Authorization: Bearer {{token}}

### 8. 参数验证测试 - 订单ID为空
POST {{baseUrl}}/publicbiz/domestic-task-order/generate-tasks
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "forceRegenerate": false
}

### 9. 参数验证测试 - 订单ID为负数
POST {{baseUrl}}/publicbiz/domestic-task-order/generate-tasks
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "orderId": -1,
  "forceRegenerate": false
}

### 10. 错误处理测试 - 不存在的订单ID
POST {{baseUrl}}/publicbiz/domestic-task-order/generate-tasks
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "orderId": 999999,
  "forceRegenerate": false
}

### 测试环境变量
# @baseUrl = http://localhost:8080
# @token = your_jwt_token_here
